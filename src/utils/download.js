/**
 * 文件下载工具类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */

/**
 * 下载文件
 * @param {Blob} blob 文件数据
 * @param {string} fileName 文件名
 */
export function downloadFile(blob, fileName) {
  // 创建下载链接
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  
  // 触发下载
  document.body.appendChild(link)
  link.click()
  
  // 清理
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * 从响应头中获取文件名
 * @param {Object} response 响应对象
 * @returns {string} 文件名
 */
export function getFileNameFromResponse(response) {
  const contentDisposition = response.headers['content-disposition']
  if (contentDisposition) {
    const fileNameMatch = contentDisposition.match(/filename\*?=['"]?([^'";]+)['"]?/)
    if (fileNameMatch) {
      return decodeURIComponent(fileNameMatch[1])
    }
  }
  return `下载文件_${new Date().getTime()}.xlsx`
}

/**
 * 导出Excel文件
 * @param {Function} exportApi 导出API函数
 * @param {Object} params 导出参数
 * @param {string} defaultFileName 默认文件名
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 错误回调
 * @param {Function} onProgress 进度回调
 */
export function exportExcel(exportApi, params, defaultFileName, onSuccess, onError, onProgress) {
  // 开始导出，触发进度回调
  if (onProgress) {
    onProgress({ status: 'processing', progress: 0, message: '正在准备导出数据...' })
  }

  return exportApi(params)
    .then(response => {
      if (onProgress) {
        onProgress({ status: 'processing', progress: 90, message: '正在生成文件...' })
      }

      if (response.data && response.data.size > 0) {
        const fileName = getFileNameFromResponse(response) || defaultFileName
        downloadFile(response.data, fileName)

        if (onProgress) {
          onProgress({
            status: 'completed',
            progress: 100,
            message: '导出完成',
            fileName: fileName,
            fileSize: response.data.size,
            exportTime: new Date()
          })
        }

        if (onSuccess) {
          onSuccess(fileName, response.data.size)
        }
      } else {
        throw new Error('导出文件为空')
      }
    })
    .catch(error => {
      console.error('导出失败:', error)

      if (onProgress) {
        onProgress({
          status: 'failed',
          progress: 0,
          message: '导出失败',
          errorMessage: error.message || '未知错误'
        })
      }

      if (onError) {
        onError(error)
      }
    })
}

/**
 * 格式化导出参数
 * @param {Object} params 原始参数
 * @returns {Object} 格式化后的参数
 */
export function formatExportParams(params) {
  const formattedParams = {}
  
  // 过滤空值
  Object.keys(params).forEach(key => {
    const value = params[key]
    if (value !== null && value !== undefined && value !== '') {
      formattedParams[key] = value
    }
  })
  
  return formattedParams
}

/**
 * 验证导出参数
 * @param {Object} params 导出参数
 * @param {number} maxCount 最大导出数量
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
export function validateExportParams(params, maxCount = 10000) {
  // 检查时间范围
  if (params.createTimeStart && params.createTimeEnd) {
    const startTime = new Date(params.createTimeStart)
    const endTime = new Date(params.createTimeEnd)
    const diffDays = (endTime - startTime) / (1000 * 60 * 60 * 24)
    
    if (diffDays > 365) {
      return {
        valid: false,
        message: '导出时间范围不能超过365天'
      }
    }
  }
  
  // 检查导出数量限制
  if (params.maxExportCount && params.maxExportCount > maxCount) {
    return {
      valid: false,
      message: `导出数量不能超过${maxCount}条`
    }
  }
  
  return {
    valid: true,
    message: ''
  }
}

/**
 * 生成导出文件名
 * @param {string} prefix 文件名前缀
 * @param {Object} params 导出参数
 * @returns {string} 文件名
 */
export function generateExportFileName(prefix, params = {}) {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
  let fileName = `${prefix}_${timestamp}`
  
  // 添加条件标识
  if (params.orderStatus) {
    fileName += `_${params.orderStatus}`
  }
  if (params.createTimeStart && params.createTimeEnd) {
    const startDate = new Date(params.createTimeStart).toISOString().slice(0, 10)
    const endDate = new Date(params.createTimeEnd).toISOString().slice(0, 10)
    fileName += `_${startDate}_${endDate}`
  }
  
  return `${fileName}.xlsx`
}
