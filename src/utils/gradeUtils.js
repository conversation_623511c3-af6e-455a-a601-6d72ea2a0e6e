/**
 * 年级相关工具函数
 * 统一管理年级的显示和转换逻辑
 */

// 年级映射表：数字值 -> 显示文本
export const GRADE_MAP = {
  1: '一年级',
  2: '二年级', 
  3: '三年级',
  4: '四年级',
  5: '五年级',
  6: '六年级',
  7: '初一',
  8: '初二',
  9: '初三',
  10: '高一',
  11: '高二',
  12: '高三'
}

// 年级选项列表（用于下拉选择）
export const GRADE_OPTIONS = [
  { label: '一年级', value: 1 },
  { label: '二年级', value: 2 },
  { label: '三年级', value: 3 },
  { label: '四年级', value: 4 },
  { label: '五年级', value: 5 },
  { label: '六年级', value: 6 },
  { label: '初一', value: 7 },
  { label: '初二', value: 8 },
  { label: '初三', value: 9 },
  { label: '高一', value: 10 },
  { label: '高二', value: 11 },
  { label: '高三', value: 12 }
]

// 年级分组选项（用于快速选择）
export const GRADE_GROUP_OPTIONS = [
  {
    label: '小学',
    value: 'primary',
    grades: [1, 2, 3, 4, 5, 6]
  },
  {
    label: '初中', 
    value: 'middle',
    grades: [7, 8, 9]
  },
  {
    label: '高中',
    value: 'high', 
    grades: [10, 11, 12]
  }
]

/**
 * 根据年级值获取显示文本
 * @param {number|string} grade - 年级值
 * @returns {string} 年级显示文本
 */
export function getGradeText(grade) {
  const gradeNum = typeof grade === 'string' ? parseInt(grade) : grade
  return GRADE_MAP[gradeNum] || grade || '-'
}

/**
 * 根据年级值获取学段
 * @param {number|string} grade - 年级值
 * @returns {string} 学段（小学/初中/高中）
 */
export function getGradeStage(grade) {
  const gradeNum = typeof grade === 'string' ? parseInt(grade) : grade
  
  if (gradeNum >= 1 && gradeNum <= 6) {
    return '小学'
  } else if (gradeNum >= 7 && gradeNum <= 9) {
    return '初中'
  } else if (gradeNum >= 10 && gradeNum <= 12) {
    return '高中'
  }
  
  return '未知'
}

/**
 * 根据学段获取年级列表
 * @param {string} stage - 学段（小学/初中/高中）
 * @returns {number[]} 年级值数组
 */
export function getGradesByStage(stage) {
  const stageMap = {
    '小学': [1, 2, 3, 4, 5, 6],
    '初中': [7, 8, 9],
    '高中': [10, 11, 12]
  }
  
  return stageMap[stage] || []
}

/**
 * 获取年级选项（用于表单选择）
 * @param {boolean} includeAll - 是否包含"全部"选项
 * @returns {Array} 年级选项数组
 */
export function getGradeSelectOptions(includeAll = false) {
  const options = [...GRADE_OPTIONS]
  
  if (includeAll) {
    options.unshift({ label: '全部年级', value: '' })
  }
  
  return options
}

/**
 * 获取分组年级选项（用于快速选择）
 * @returns {Array} 分组年级选项数组
 */
export function getGradeGroupSelectOptions() {
  return GRADE_GROUP_OPTIONS.map(group => ({
    label: group.label,
    value: group.value,
    children: group.grades.map(grade => ({
      label: GRADE_MAP[grade],
      value: grade
    }))
  }))
}

/**
 * 验证年级值是否有效
 * @param {number|string} grade - 年级值
 * @returns {boolean} 是否有效
 */
export function isValidGrade(grade) {
  const gradeNum = typeof grade === 'string' ? parseInt(grade) : grade
  return gradeNum >= 1 && gradeNum <= 12
}

/**
 * 格式化年级数组为显示文本
 * @param {Array} grades - 年级值数组
 * @returns {string} 格式化后的显示文本
 */
export function formatGradesText(grades) {
  if (!grades || grades.length === 0) {
    return '未设置'
  }
  
  return grades.map(grade => getGradeText(grade)).join('、')
}

/**
 * 根据学生年级获取默认的适合年级列表
 * @param {number|string} studentGrade - 学生年级
 * @returns {number[]} 适合的年级列表
 */
export function getDefaultSuitableGrades(studentGrade) {
  const gradeNum = typeof studentGrade === 'string' ? parseInt(studentGrade) : studentGrade
  
  if (!isValidGrade(gradeNum)) {
    return []
  }
  
  // 默认返回学生当前年级
  return [gradeNum]
}
