<template>
  <el-drawer
    v-model="visible"
    title="学习环节列表"
    size="400px"
    :with-header="false"
    direction="rtl"
    class="review-basket-drawer"
  >
    <div class="review-basket-container" v-loading="disabled">
      <!-- 抽屉头部 -->
      <div class="review-basket-header">
        <div class="header-title">
          <el-icon class="header-icon"><Notebook /></el-icon>
          学习环节
        </div>
        <div class="header-subtitle">明理致用，奠定基础</div>
      </div>

      <!-- 复习内容区域 -->
      <div class="stages-list-content">
        <div v-if="sections && sections.length > 0">
          <div v-for="(section, index) in sections" :key="index" class="stage-item" @click="selectSection(index, section)">
            <div class="stage-icon">
              <el-icon>
                <Check />
              </el-icon>
            </div>
            <div class="stage-info">
              <div class="stage-title">{{ section.title }}</div>
              <div class="stage-type">{{ section.type }}</div>
            </div>
            <div class="stage-status">
              <el-tag size="small" type="success">
                {{ section.status }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import {computed, onMounted, ref} from "vue";
import {Check, Notebook} from "@element-plus/icons-vue";
import {COURSE_sessionStorage_INFO_KEY, Section} from "@/api/course";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  studentId: {
    type: String,
    required: true,
  },
  courseId: {
    type: String,
    required: true,
  },
});

const disabled = ref<boolean>(false);
const sections = ref<Section[]>([])
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "close"): void;
  (e: "startReviewCourse", nodeInfo: any): void;
  (e: "changeCurrentStageType", index: number, value: string, text: string, stepIndex: number): void;
}>();

const selectSection = (index:number, section:Section) => {
  console.log("选中环节：", section);
  // 切换环节只是临时的，如果刷新页面。按照最新的接口返回
  // 这里只需要覆盖掉sessionStorage里面的currentSectionIndex数据即可
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      // 获取当前阶段的提交结果
      courseInfo!.content!.currentSectionIndex = index
      // 在赋值回去
      sessionStorage.setItem(COURSE_sessionStorage_INFO_KEY + props.courseId, JSON.stringify(courseInfo))
      // 切换stage
      let currentWord = section?.words[section?.currentWordIndex];
      let currentStep = currentWord?.steps[currentWord?.currentStepIndex];
      emit('changeCurrentStageType', index, currentStep?.type, currentWord?.wordInfo?.word, currentWord?.currentStepIndex)
      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }

}

onMounted(() => {
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      // 获取当前阶段的提交结果
      sections.value = courseInfo?.content?.sections
      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
});

// 关闭抽屉
const handleClose = () => {
  emit("update:visible", false);
  emit("close");
};

// 监听visible属性变化
const visible = computed({
  get: () => props.visible,
  set: (value) => {
    emit("update:visible", value);
  },
});
</script>

<style lang="scss" scoped>
.review-basket-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff8e1;
}

.review-basket-header {
  padding: 24px;
  background: linear-gradient(135deg, #ffe0b2, #ffecb3);
  border-bottom: 2px solid #ffd54f;

  .header-title {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #ff9800;
    margin-bottom: 8px;

    .header-icon {
      margin-right: 8px;
      font-size: 24px;
    }
  }

  .header-subtitle {
    color: #f57c00;
    font-size: 14px;
    opacity: 0.8;
  }
}


.stages-list-content {
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  padding: 10px;
  background: #fff8e1;
  scrollbar-width: thin;
  scrollbar-color: #ffb74d #fff3e0;
}

.stages-list-content::-webkit-scrollbar {
  width: 6px;
}

.stages-list-content::-webkit-scrollbar-track {
  background: #fff3e0;
  border-radius: 3px;
}

.stages-list-content::-webkit-scrollbar-thumb {
  background: #ffb74d;
  border-radius: 3px;
  border: 1px solid #fff3e0;
}

.stages-list-content::-webkit-scrollbar-thumb:hover {
  background: #ff9800;
}

.stage-item {
  padding: 12px 16px;
  border-radius: 12px;
  background: #ffffff;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid #ffe0b2;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stage-item:hover {
  background: #fff8e1;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(255, 152, 0, 0.1);
  border-color: #ffd54f;
}

.stage-item.active-stage {
  background: #fff3e0;
  border-color: #ffb74d;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.15);
}

.stage-item:last-child {
  margin-bottom: 0;
}

.stage-icon {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ff9800;
  background: #fff8e1;
  border-radius: 50%;
  padding: 5px;
  border: 1px solid #ffd54f;
  transition: all 0.3s ease;
}

.active-stage .stage-icon {
  background: #ffd54f;
  color: #ffffff;
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(255, 152, 0, 0.2);
}

.stage-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.stage-title {
  font-weight: bold;
  color: #ff9800;
  font-size: 16px;
  line-height: 1.2;
}

.stage-type {
  font-size: 12px;
  color: #9e9e9e;
  line-height: 1.2;
}

.stage-status {
  display: flex;
  align-items: center;
}

.stage-status .el-tag {
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 11px;
  font-weight: 600;
  font-size: 11px;
  border-width: 1px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

@media screen and (max-width: 768px) {
  .stage-title {
    font-size: 14px;
  }

  .stage-type {
    font-size: 11px;
  }

  .stage-icon {
    width: 24px;
    height: 24px;
  }

  .stage-status .el-tag {
    padding: 0 6px;
    height: 20px;
    line-height: 18px;
    font-size: 10px;
  }
}
</style>
