<template>
  <el-drawer
    v-model="visible"
    title="单词测验结果"
    size="500px"
    :with-header="false"
    direction="rtl"
    class="word-test-drawer"
  >
    <div class="word-test-container" v-loading="disabled">
      <!-- 抽屉头部 -->
      <div class="word-test-header">
        <div class="header-content">
          <div class="header-title">
            <el-icon class="header-icon"><TrendCharts /></el-icon>
            单词测验结果
          </div>
          <div class="header-subtitle">
            <span class="sparkle">✨</span>
            每一次的进步，都值得被嘉奖
            <span class="sparkle">✨</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="large"
            class="test-button"
            :loading="disabled"
            v-if="!showSelectArea"
            @click="showSelectAreaBtn"
          >
            <el-icon><RefreshRight /></el-icon>
            再次测验
          </el-button>
        </div>
      </div>

      <!-- 测验内容区域 -->
      <div class="word-test-content" v-if="!showSelectArea">
        <!-- 测验结果卡片 -->
        <div
          v-for="(detail, detailIndex) in wordTestedList"
          :key="detailIndex"
          class="test-result-card"
        >
          <!-- 测验基本信息 -->
          <div class="test-header">
            <div class="test-date">
              <el-icon><Calendar /></el-icon>
              <span>{{ detail.testedTime }}</span>
            </div>
          </div>

          <!-- 成绩概览区域 -->
          <div class="score-overview">
            <div class="score-stats">
              <div class="stat-item">
                <div class="stat-icon">📝</div>
                <div class="stat-value">{{ detail.testedWordNum }}</div>
                <div class="stat-label">测验单词数量</div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">✅</div>
                <div class="stat-value">{{ detail.successWordNum }}</div>
                <div class="stat-label">正确单词</div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">🎯</div>
                <div class="stat-value">{{ detail.successRate }}</div>
                <div class="stat-label">正确率</div>
              </div>
            </div>
          </div>

          <!-- 词汇水平展示 -->
          <!-- <div class="vocabulary-section">
            <div class="level-display">
              <div class="level-title">
                <el-icon><Trophy /></el-icon>
                词汇水平
              </div>
              <div
                class="level-badge"
                :class="{
                  'level-low': detail.result === '低',
                  'level-medium': detail.result === '中',
                  'level-high': detail.result === '高',
                }"
              >
                {{ detail.result }}
              </div>
            </div>
            <div class="vocab-stats">
              <div class="vocab-item">
                <div class="vocab-icon">📚</div>
                <div class="vocab-info">
                  <div class="vocab-label">预估掌握词汇量</div>
                  <div class="vocab-value">{{ detail.estimatedWordNum }}</div>
                </div>
              </div>
            </div>
          </div> -->

          <!-- 详细分析区域 -->
          <div class="analysis-section" v-if="detail.testDetailInfo">
            <div class="section-title">
              <el-icon><DataAnalysis /></el-icon>
              测验分析
            </div>

            <!-- 学习进度分析 -->
            <!-- <div class="progress-analysis">
              <div class="progress-item">
                <div class="progress-label">课本单词掌握率</div>
                <el-progress 
                  :percentage="parseFloat(detail.testDetailInfo.lastSemesterWordCollectRate)" 
                  :color="getProgressColor(detail.testDetailInfo.lastSemesterWordCollectRate)"
                />
              </div>
              <div class="progress-item">
                <div class="progress-label">高分词汇掌握率</div>
                <el-progress 
                  :percentage="parseFloat(detail.testDetailInfo.specialWordCollectRate)"
                  :color="getProgressColor(detail.testDetailInfo.specialWordCollectRate)"
                />
              </div>
            </div> -->

            <!-- 分析结果区域 -->
            <div class="analysis-result">
              <!-- 这里可以添加其他分析结果 -->
            </div>
          </div>

          <!-- 学习建议 -->
          <div class="suggestions-section">
            <div class="suggestions-header">
              <el-icon><InfoFilled /></el-icon>
              <span>建议教材：{{ detail.suggestions }}</span>
            </div>
            <div class="suggestions-header">
              <el-icon><Timer /></el-icon>
              <span>测验耗时：{{ detail.consumTime }}</span>
            </div>

            <!-- 错误内容下载区域 -->
            <div class="error-download-section" v-if="hasErrorDownloads(detail)">
              <div class="download-header">
                <el-icon><Download /></el-icon>
                <span>错误内容下载</span>
              </div>
              <div class="download-actions">
                <el-button
                  v-if="detail.errorHandoutPdfUrl"
                  type="primary"
                  size="small"
                  :icon="Document"
                  @click="openDownloadUrl(detail.errorHandoutPdfUrl)"
                >
                  错词讲义
                </el-button>
                <el-button
                  v-if="detail.errorExercisePdfUrl"
                  type="success"
                  size="small"
                  :icon="EditPen"
                  @click="openDownloadUrl(detail.errorExercisePdfUrl)"
                >
                  错题练习
                </el-button>
              </div>
            </div>
            <!-- <div class="suggestions-content">
              <div class="suggestion-tabs">
                <div 
                  class="tab-item" 
                  :class="{ active: activeTab === 'general' }"
                  @click="activeTab = 'general'"
                >
                  课本词汇建议
                </div>
                <div 
                  class="tab-item" 
                  :class="{ active: activeTab === 'special' }"
                  @click="activeTab = 'special'"
                >
                  高分词汇建议
                </div>
                <div
                  class="tab-item"
                  :class="{ active: activeTab === 'plan' }"
                  @click="activeTab = 'plan'"
                >
                  学习计划
                </div>
              </div>
              <div class="tab-content" v-if="activeTab === 'general'">
                {{ detail.testDetailInfo?.lastSemesterSuggestions || '暂无' }}
              </div>
              <div class="tab-content" v-else-if="activeTab === 'special'">
                {{ detail.testDetailInfo?.specialWordSuggestions || '暂无' }}
              </div>
              <div class="tab-content" v-if="activeTab === 'plan'">
                <div class="plan-content">
                  <div class="plan-cards">
                    <div
                      class="suggestion-card"
                      :class="{ expanded: expandedTextbook }"
                    >
                      <div class="suggestion-icon">📖</div>
                      <div class="suggestion-content">
                        <div class="suggestion-title">建议教材</div>
                        <div
                          class="suggestion-text"
                          :class="{
                            'long-text': isLongText(detail.suggestions),
                          }"
                        >
                          {{ detail.suggestions || "暂无" }}
                        </div>
                        <div
                          v-if="isLongText(detail.suggestions)"
                          class="expand-toggle"
                          @click="expandedTextbook = !expandedTextbook"
                        >
                          {{ expandedTextbook ? "收起" : "展开" }}
                        </div>
                      </div>
                    </div>
                    <div class="suggestion-card">
                      <div class="suggestion-icon">⏰</div>
                      <div class="suggestion-content">
                        <div class="suggestion-title">建议学习时间</div>
                        <div class="suggestion-text">{{ detail.testDetailInfo?.suggestLearnTime || '暂无' }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
      <div v-else-if="showSelectArea" class="select-area-container">
        <div class="select-area-card">
          <div class="section-title">
            <el-icon><Collection /></el-icon>
            单词测验设置
          </div>
          <div class="select-area-content">
            <el-form label-width="80px" label-position="left">
              <el-form-item label="选择教材">
                <el-select
                  v-model="textbookId"
                  filterable
                  clearable
                  placeholder="请选择教材名称"
                  class="select-input"
                  @change="onTextbookChange"
                >
                  <el-option
                    v-for="item in searchOptions"
                    :key="item.id"
                    :label="`${item.name} (${item.statWordCnt || 0}个单词)`"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="选择方式">
                <el-radio-group v-model="testMode" @change="onTestModeChange">
                  <el-radio label="random">随机选择</el-radio>
                  <el-radio label="range">自定范围</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="testMode === 'random'" label="单词数量" label-width="80">
                <el-input-number
                  v-model="wordNum"
                  :min="1"
                  :max="Math.max(1, currentTextbookWordCount || 1)"
                  placeholder="请输入单词数量"
                  class="number-input"
                />
                <div class="form-tip" v-if="currentTextbookWordCount > 0">
                  最多可选择 {{ currentTextbookWordCount }} 个单词
                </div>
              </el-form-item>
              <el-form-item v-if="testMode === 'range'" label="单词范围" label-width="80">
                <div class="range-input-group">
                  <el-input-number
                    v-model="startIndex"
                    :min="1"
                    :max="Math.max(1, currentTextbookWordCount || 1)"
                    placeholder="起始位置"
                    class="range-input"
                  />
                  <span class="range-separator">至</span>
                  <el-input-number
                    v-model="endIndex"
                    :min="Math.max(1, startIndex || 1)"
                    :max="Math.max(startIndex || 1, currentTextbookWordCount || 1)"
                    placeholder="结束位置"
                    class="range-input"
                  />
                </div>
                <div class="form-tip" v-if="currentTextbookWordCount > 0">
                  教材共有 {{ currentTextbookWordCount }} 个单词，当前选择 {{ rangeWordCount }} 个单词
                </div>
              </el-form-item>
              <div class="form-actions">
                <el-button 
                  type="primary" 
                  @click="startWordTest" 
                  :loading="disabled"
                  class="start-test-button"
                >
                  <el-icon><Trophy /></el-icon>
                  开始测验
                </el-button>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  TrendCharts,
  Calendar,
  Collection,
  UserFilled,
  InfoFilled,
  Loading,
  CircleCheck,
  RefreshRight,
  Trophy,
  DataAnalysis,
  Download,
  Document,
  EditPen,
  Timer,
} from "@element-plus/icons-vue";
import {
  WordTestInfo,
  getWordTestListApi,
  startWordTestCourseInfoApi,
} from "../../../api/course";
import {
  listAllApi
} from "../../../api/textbook";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  studentId: {
    type: String,
    required: true,
  },
  courseId: {
    type: String,
    required: true,
  },
});

const searchOptions = ref<any[]>([]);
const wordNum = ref<number>(10);
const textbookId = ref<string>("");
const showSelectArea = ref(false);
const wordTestedList = ref<WordTestInfo[]>([]);
const disabled = ref<boolean>(false);
const activeTab = ref<string>("general"); // 控制建议标签页
const expandedTextbook = ref<boolean>(false); // 控制建议教材展开状态

// 新增的响应式数据
const testMode = ref<string>("random"); // 测试模式：random 或 range
const startIndex = ref<number>(1); // 范围开始位置
const endIndex = ref<number>(10); // 范围结束位置
const currentTextbookWordCount = ref<number>(1); // 当前教材的单词数量，初始化为1避免min>max错误

// 移除未使用的变量

// 计算范围选择的单词数量
const rangeWordCount = computed(() => {
  if (testMode.value === 'range' && startIndex.value && endIndex.value) {
    return Math.max(0, endIndex.value - startIndex.value + 1);
  }
  return 0;
});

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "close"): void;
  (e: "startLearning", nodeInfo: any): void;
}>();

onMounted(() => {
  // 初始化数据
  initData();
});

const initData = () => {
  getWordTestListApi(props.studentId).then((res: any) => {
    if (res.code == 200) {
      wordTestedList.value = res.data as WordTestInfo[];
      // 初始化时重置标签页
      activeTab.value = "general";
    }
  });
};

// 获取进度条颜色
const getProgressColor = (percentage: string) => {
  const value = parseInt(percentage);
  if (value < 40) return "#F56C6C";
  if (value < 70) return "#E6A23C";
  return "#67C23A";
};

// 教材选择变化事件
const onTextbookChange = (textbookId: string) => {
  if (textbookId) {
    const selectedTextbook = searchOptions.value.find(item => item.id === textbookId);
    if (selectedTextbook) {
      currentTextbookWordCount.value = Math.max(1, selectedTextbook.statWordCnt || 1);
      // 重置相关数据
      if (testMode.value === 'random') {
        wordNum.value = Math.min(wordNum.value || 10, currentTextbookWordCount.value);
      } else {
        startIndex.value = 1;
        endIndex.value = Math.min(10, currentTextbookWordCount.value);
      }
    }
  } else {
    currentTextbookWordCount.value = 1; // 设置为1避免min > max错误
  }
};

// 测试模式变化事件
const onTestModeChange = (mode: string) => {
  const maxCount = Math.max(1, currentTextbookWordCount.value || 1);
  if (mode === 'random') {
    wordNum.value = Math.min(wordNum.value || 10, maxCount);
  } else {
    startIndex.value = 1;
    endIndex.value = Math.min(10, maxCount);
  }
};

const showSelectAreaBtn =()=>{
  disabled.value = true;
  listAllApi().then((res) => {
    if (res.code == 200) {
      searchOptions.value = res.data as any[];
      showSelectArea.value = true;
    }
  }).finally(()=>{
    disabled.value = false;
  });

}

// 开始复习
const startWordTest = () => {
  // 验证输入
  if (!textbookId.value) {
    ElMessage.warning("请选择教材");
    return;
  }

  let requestData: any = {
    textbookId: textbookId.value,
    testMode: testMode.value
  };

  if (testMode.value === 'random') {
    if (!wordNum.value || wordNum.value <= 0) {
      ElMessage.warning("请输入有效的单词数量");
      return;
    }
    if (currentTextbookWordCount.value > 1 && wordNum.value > currentTextbookWordCount.value) {
      ElMessage.warning(`单词数量不能超过教材总数 ${currentTextbookWordCount.value}`);
      return;
    }
    requestData.wordNum = wordNum.value;
  } else {
    if (!startIndex.value || !endIndex.value || startIndex.value > endIndex.value) {
      ElMessage.warning("请输入有效的单词范围");
      return;
    }
    if (currentTextbookWordCount.value > 1 && endIndex.value > currentTextbookWordCount.value) {
      ElMessage.warning(`结束位置不能超过教材总数 ${currentTextbookWordCount.value}`);
      return;
    }
    requestData.startIndex = startIndex.value;
    requestData.endIndex = endIndex.value;
    requestData.wordNum = rangeWordCount.value;
  }

  disabled.value = true;
  startWordTestCourseInfoApi(props.studentId, props.courseId, requestData)
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success("开始测验成功");
        // localStorage.setItem(COURSE_LOCALSTORAGE_INFO_KEY + courseId.value,JSON.stringify(res.data));
        emit("startLearning", res.data);
        handleClose();
      }
    })
    .finally(() => {
      disabled.value = false;
    });
};

// 关闭抽屉
const handleClose = () => {
  emit("update:visible", false);
  emit("close");
};

// 监听visible属性变化
const visible = computed({
  get: () => props.visible,
  set: (value) => {
    emit("update:visible", value);
  },
});

// 判断是否有错误内容下载
const hasErrorDownloads = (detail: any) => {
  return detail.errorHandoutPdfUrl || detail.errorExercisePdfUrl;
};

// 打开下载链接
const openDownloadUrl = (url: string) => {
  if (url) {
    window.open(url, '_blank');
  }
};
</script>

<style lang="scss" scoped>
.word-test-drawer {
  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.word-test-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;
}

.word-test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;

  .header-content {
    .header-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;

      .header-icon {
        margin-right: 8px;
        font-size: 24px;
      }
    }

    .header-subtitle {
      font-size: 14px;
      opacity: 0.9;
      display: flex;
      align-items: center;

      .sparkle {
        margin: 0 6px;
      }
    }
  }

  .header-actions {
    .test-button {
      background: white;
      color: #409eff;
      border: none;
      transition: all 0.3s;
      padding: 10px 20px;
      font-size: 16px;
      border-radius: 20px;
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(255, 255, 255, 0.3);
      }
    }
  }
}

.word-test-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.test-result-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  position: relative;

  .test-date {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #606266;
    margin-bottom: 16px;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
    }
  }

  .test-status-tag {
    width: 60px;
    padding: 4px 12px;
    margin-left: 10px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 4px;
      font-size: 14px;
    }
  }

  .status-in-progress {
    background-color: #e6a23c;
    color: white;
  }

  .status-completed {
    background-color: #67c23a;
    color: white;
  }
}

.score-overview {
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f3ff 100%);
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .score-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;

    .stat-item {
      text-align: center;
      padding: 15px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;

      &:hover {
        transform: translateY(-5px);
      }

      .stat-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .stat-value {
        font-size: 22px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.vocabulary-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .level-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .level-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #ffc107;
        font-size: 20px;
      }
    }

    .level-badge {
      padding: 8px 20px;
      border-radius: 20px;
      font-weight: bold;
      font-size: 16px;
      text-transform: uppercase;
      letter-spacing: 1px;

      &.level-low {
        background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
        color: #fff;
      }

      &.level-medium {
        background: linear-gradient(135deg, #ffd1ff 0%, #fad0c4 100%);
        color: #fff;
      }

      &.level-high {
        background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
        color: #fff;
      }
    }
  }

  .vocab-stats {
    .vocab-item {
      display: flex;
      align-items: center;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 12px;
      transition: transform 0.3s ease;

      &:hover {
        transform: translateX(5px);
      }

      .vocab-icon {
        font-size: 24px;
        margin-right: 15px;
      }

      .vocab-info {
        flex: 1;

        .vocab-label {
          font-size: 14px;
          color: #606266;
        }

        .vocab-value {
          font-size: 20px;
          font-weight: bold;
          color: #409eff;
          margin-top: 4px;
        }
      }
    }
  }
}

.analysis-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 20px;
    }
  }

  .progress-analysis {
    margin: 20px 0;

    .progress-item {
      margin-bottom: 15px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 12px;
      transition: transform 0.3s ease;

      &:hover {
        transform: translateX(5px);
        background: #f0f7ff;
      }

      .progress-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 10px;
      }

      :deep(.el-progress-bar__outer) {
        border-radius: 8px;
      }

      :deep(.el-progress-bar__inner) {
        border-radius: 8px;
        transition: width 0.6s ease;
      }
    }
  }

  .suggestions-area {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 20px;

    .suggestion-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 15px;
      display: flex;
      gap: 12px;
      transition: all 0.3s ease;
      min-height: 80px;
      overflow: hidden;

      &:hover {
        background: #f0f7ff;
        transform: translateY(-2px);
      }

      &.expanded {
        height: auto;

        .suggestion-text {
          display: block;
          white-space: normal;
          -webkit-line-clamp: unset;
        }
      }

      .suggestion-icon {
        font-size: 24px;
        flex-shrink: 0;
      }

      .suggestion-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .suggestion-title {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }

        .suggestion-text {
          font-size: 14px;
          font-weight: bold;
          color: #303133;
          line-height: 1.5;

          &.long-text {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .expand-toggle {
          color: #409eff;
          font-size: 12px;
          cursor: pointer;
          margin-top: 8px;
          user-select: none;
          transition: color 0.3s ease;

          &:hover {
            color: #66b1ff;
          }
        }
      }
    }
  }
}

.suggestions-section {
  .suggestions-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-weight: bold;
    color: #606266;

    .el-icon {
      margin-right: 8px;
      color: #e6a23c;
    }
  }

  .suggestions-content {
    padding: 12px;
    background-color: #fdf6ec;
    border-radius: 6px;
    color: #e6a23c;
    line-height: 1.5;
  }
}

// 错误内容下载区域样式
.error-download-section {
  margin-top: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
  border-radius: 12px;
  border: 1px solid #fecaca;

  .download-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: bold;
    color: #dc2626;

    .el-icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  .download-actions {
    display: flex;
    gap: 8px;

    .el-button {
      flex: 1;
      font-size: 12px;
      padding: 6px 12px;
      border-radius: 6px;

      .el-icon {
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }
}

// 自定义滚动条样式
.word-test-content::-webkit-scrollbar {
  width: 6px;
}

.word-test-content::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

.word-test-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;

  &:hover {
    background: #909399;
  }
}

// 建议区域样式优化
.suggestions-section {
  margin-top: 20px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .suggestions-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
    color: #303133;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 20px;
    }
  }

  .suggestions-content {
    background-color: #f5f7fa;
    border-radius: 12px;
    overflow: hidden;

    .suggestion-tabs {
      display: flex;
      background: white;
      padding: 4px;
      border-bottom: 1px solid #e4e7ed;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 12px;
        font-size: 14px;
        color: #606266;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 0 4px;

        &:hover {
          color: #409eff;
          background: #f0f7ff;
        }

        &.active {
          color: #409eff;
          font-weight: bold;
          background: #ecf5ff;
        }
      }
    }

    .tab-content {
      padding: 20px;
      color: #606266;
      font-size: 14px;
      line-height: 1.8;
      position: relative;
      min-height: 100px;

      &:not(.plan-content) {
        &::before {
          content: '"';
          position: absolute;
          top: 10px;
          left: 10px;
          font-size: 40px;
          color: #dcdfe6;
          font-family: Arial, sans-serif;
          opacity: 0.5;
        }

        &::after {
          content: '"';
          position: absolute;
          bottom: 0;
          right: 10px;
          font-size: 40px;
          color: #dcdfe6;
          font-family: Arial, sans-serif;
          opacity: 0.5;
        }
      }

      .plan-content {
        .plan-suggestions {
          background: #f5f7fa;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 20px;
          position: relative;

          &::before {
            content: '"';
            position: absolute;
            top: 5px;
            left: 10px;
            font-size: 24px;
            color: #dcdfe6;
            font-family: Arial, sans-serif;
            opacity: 0.5;
          }

          &::after {
            content: '"';
            position: absolute;
            bottom: -5px;
            right: 10px;
            font-size: 24px;
            color: #dcdfe6;
            font-family: Arial, sans-serif;
            opacity: 0.5;
          }
        }

        .plan-cards {
          display: flex;
          flex-direction: column;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
          margin-top: 20px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .suggestion-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            gap: 12px;
            transition: all 0.3s ease;
            min-height: 80px;

            &:hover {
              background: #f0f7ff;
              transform: translateY(-2px);
            }

            &.expanded {
              grid-column: span 2;

              @media (max-width: 768px) {
                grid-column: span 1;
              }

              .suggestion-text {
                display: block;
                white-space: normal;
                -webkit-line-clamp: unset;
              }
            }
          }
        }
      }
    }
  }
}

// 全局动画定义
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes twinkle {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 添加全局过渡效果
.test-result-card {
}

.stat-item,
.vocab-item,
.suggestion-card {
}

.select-area-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.select-area-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;

  .section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 20px;
    }
  }

  .select-area-content {
    padding: 10px;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #606266;
    }

    .select-input, .number-input {
      width: 100%;
    }

    .range-input-group {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .range-input {
      flex: 1;
      width: 120px;
    }

    .range-separator {
      color: #666;
      font-weight: 500;
    }

    .form-tip {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
      line-height: 1.4;
    }

    .form-actions {
      display: flex;
      justify-content: center;
      margin-top: 30px;
    }

    .start-test-button {
      padding: 12px 30px;
      font-size: 16px;
      border-radius: 20px;
      background: linear-gradient(135deg, #409eff, #67c23a);
      border: none;
      color: white;
      transition: all 0.3s;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
      }

      .el-icon {
        margin-right: 8px;
      }
    }
  }
}
.suggestions-section {
  margin-top: 20px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .suggestions-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
    color: #303133;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 20px;
    }
  }

  .suggestions-content {
    background-color: #f5f7fa;
    border-radius: 12px;
    overflow: hidden;

    .suggestion-tabs {
      display: flex;
      background: white;
      padding: 4px;
      border-bottom: 1px solid #e4e7ed;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 12px;
        font-size: 14px;
        color: #606266;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 0 4px;

        &:hover {
          color: #409eff;
          background: #f0f7ff;
        }

        &.active {
          color: #409eff;
          font-weight: bold;
          background: #ecf5ff;
        }
      }
    }

    .tab-content {
      padding: 20px;
      color: #606266;
      font-size: 14px;
      line-height: 1.8;
      position: relative;
      min-height: 100px;

      &:not(.plan-content) {
        &::before {
          content: '"';
          position: absolute;
          top: 10px;
          left: 10px;
          font-size: 40px;
          color: #dcdfe6;
          font-family: Arial, sans-serif;
          opacity: 0.5;
        }

        &::after {
          content: '"';
          position: absolute;
          bottom: 0;
          right: 10px;
          font-size: 40px;
          color: #dcdfe6;
          font-family: Arial, sans-serif;
          opacity: 0.5;
        }
      }

      .plan-content {
        .plan-suggestions {
          background: #f5f7fa;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 20px;
          position: relative;

          &::before {
            content: '"';
            position: absolute;
            top: 5px;
            left: 10px;
            font-size: 24px;
            color: #dcdfe6;
            font-family: Arial, sans-serif;
            opacity: 0.5;
          }

          &::after {
            content: '"';
            position: absolute;
            bottom: -5px;
            right: 10px;
            font-size: 24px;
            color: #dcdfe6;
            font-family: Arial, sans-serif;
            opacity: 0.5;
          }
        }

        .plan-cards {
          display: flex;
          flex-direction: column;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
          margin-top: 20px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .suggestion-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            gap: 12px;
            transition: all 0.3s ease;
            min-height: 80px;

            &:hover {
              background: #f0f7ff;
              transform: translateY(-2px);
            }

            &.expanded {
              grid-column: span 2;

              @media (max-width: 768px) {
                grid-column: span 1;
              }

              .suggestion-text {
                display: block;
                white-space: normal;
                -webkit-line-clamp: unset;
              }
            }
          }
        }
      }
    }
  }
}

// 全局动画定义
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes twinkle {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 添加全局过渡效果
.test-result-card {
}

.stat-item,
.vocab-item,
.suggestion-card {
}
</style>
