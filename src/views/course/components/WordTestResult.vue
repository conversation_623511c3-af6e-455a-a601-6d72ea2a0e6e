<template>
  <div class="word-test-result" v-loading="loading" :class="{ 'is-mobile': isMobile }">
    <!-- 装饰元素 -->
    <div class="decoration-star star1"></div>
    <div class="decoration-star star2"></div>
    <div class="decoration-star star3"></div>
    <div class="decoration-circle circle1"></div>

    <!-- 顶部祝贺区域 -->
    <div class="celebration-section">
      <div class="celebration-content">
        <div class="celebration-icon">🎉</div>
        <div class="celebration-text">
          <h1 class="wave-text">恭喜你完成本次词汇量测评！</h1>
          <p class="wave-text-sub">无论结果如何，我们都有了一个好的开端！</p>
          <p class="wave-text-sub">好的开始就是成功的一半，学习也可以很有趣，我们一定加油成为英语王者！</p>
          <p class="prompt-action">本次测评结果如下</p>
        </div>
        <div class="celebration-icon">🎉</div>
      </div>
    </div>

    <div class="result-container" v-if="wordTestedResult" ref="resultContainer">
      <div class="result-card score-card" ref="scoreCard">
        <div class="card-header">
          <div class="header-title">
            <el-icon><Star /></el-icon>
            <span>本次测验成绩</span>
          </div>
          <div class="test-time">
            <el-icon><Timer /></el-icon>
            耗时：{{ wordTestedResult.consumTime }}
          </div>
        </div>

        <div class="score-grid">
          <div class="score-item">
            <div class="score-icon">📝</div>
            <div class="score-value">{{ wordTestedResult.testedWordNum }}</div>
            <div class="score-label">测验单词数</div>
          </div>
          <div class="score-item">
            <div class="score-icon">✅</div>
            <div class="score-value">{{ wordTestedResult.successWordNum }}</div>
            <div class="score-label">答对单词数</div>
          </div>
          <div class="score-item highlight">
            <div class="score-icon">🎯</div>
            <div class="score-value">{{ wordTestedResult.successRate }}</div>
            <div class="score-label">正确率</div>
          </div>
        </div>
      </div>

      <!-- 进度分析卡片 -->
      <!-- <div class="result-card progress-card">
        <div class="card-header">
          <div class="header-title">
            <el-icon><TrendCharts /></el-icon>
            <span>学习进度</span>
          </div>
        </div>

        <div class="progress-items">
          <div class="progress-section">
            <div class="progress-item">
              <div class="progress-label">
                <span>课本单词掌握度</span>
                <span class="progress-value">{{ wordTestedResult.testDetailInfo?.lastSemesterWordCollectRate }}</span>
              </div>
              <el-progress 
                :percentage="parseFloat(wordTestedResult.testDetailInfo?.lastSemesterWordCollectRate)"
                :color="getProgressColor(wordTestedResult.testDetailInfo?.lastSemesterWordCollectRate)"
                :format="() => ''"
                :stroke-width="12"
              />
            </div>
            <div class="suggestion-content">
              <div class="suggestion-icon">💡</div>
              <div class="suggestion-text">{{ wordTestedResult.testDetailInfo?.lastSemesterSuggestions || '暂无建议' }}</div>
            </div>
          </div>
          <div class="progress-section">
            <div class="progress-item">
              <div class="progress-label">
                <span>高分词汇掌握度</span>
                <span class="progress-value">{{ wordTestedResult.testDetailInfo?.specialWordCollectRate }}</span>
              </div>
              <el-progress 
                :percentage="parseFloat(wordTestedResult.testDetailInfo?.specialWordCollectRate)"
                :color="getProgressColor(wordTestedResult.testDetailInfo?.specialWordCollectRate)"
                :format="() => ''"
                :stroke-width="12"
              />
            </div>
            <div class="suggestion-content">
              <div class="suggestion-icon">💡</div>
              <div class="suggestion-text">{{ wordTestedResult.testDetailInfo?.specialWordSuggestions || '暂无建议' }}</div>
            </div>
          </div>
        </div>
      </div> -->

      <!-- 学习建议卡片 -->
      <div class="result-card suggestion-card">
        <div class="card-header">
          <div class="header-title">
            <el-icon><Reading /></el-icon>
            <span>学习建议</span>
          </div>
        </div>

        <div class="plan-cards">
          <div class="plan-card">
            <div class="plan-icon">📚</div>
            <div class="plan-info">
              <div class="plan-title">建议教材</div>
              <div class="plan-text">{{ wordTestedResult.suggestions || '暂无' }}</div>
            </div>
          </div>
          <!-- <div class="plan-card">
            <div class="plan-icon">⏰</div>
            <div class="plan-info">
              <div class="plan-title">建议学习计划</div>
              <div class="plan-text">{{ wordTestedResult.testDetailInfo?.suggestLearnTime || '暂无' }}</div>
            </div>
          </div> -->
          <!-- <div class="plan-card">
            <div class="plan-icon">📝</div>
            <div class="plan-info">
              <div class="plan-title">高分必备词汇进阶建议</div>
              <div class="plan-text">{{ wordTestedResult.testDetailInfo?.specialWordLearnSuggestions || '暂无' }}</div>
            </div>
          </div> -->
        </div>
      </div>

      <!-- 错误内容下载卡片 -->
      <div class="result-card download-card" v-if="hasWrongWords">
        <div class="card-header">
          <div class="header-title">
            <el-icon><Download /></el-icon>
            <span>错误内容下载</span>
          </div>
        </div>

        <div class="download-buttons">
          <el-button
            type="primary"
            :icon="Document"
            @click="downloadErrorHandout"
            :loading="downloadLoading.handout"
          >
            错词讲义
          </el-button>
          <el-button
            type="success"
            :icon="EditPen"
            @click="downloadErrorExercise"
            :loading="downloadLoading.exercise"
          >
            错题练习
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { ElMessage } from "element-plus";
import {
  Star,
  Timer,
  TrendCharts,
  Collection,
  Reading,
  Download,
  Document,
  EditPen,
} from "@element-plus/icons-vue";
import {
  WordTestInfo,
  getWordByCourseIdApi,
  downloadWordTestErrorMaterialApi,
} from "../../../api/course";

const props = defineProps({
  courseId: {
    type: String,
    required: true,
  },
});

const loading = ref<boolean>(false);
const wordTestedResult = ref<WordTestInfo>();
const activeTab = ref<string>('textbook');

// 下载加载状态
const downloadLoading = ref({
  handout: false,
  exercise: false
});

// 是否有错误单词
const hasWrongWords = computed(() => {
  return wordTestedResult.value &&
         wordTestedResult.value.successWordNum < wordTestedResult.value.testedWordNum;
});

// 移动端检测
const isMobile = computed(() => {
  return window.innerWidth <= 768;
});

// 监听窗口大小变化
let resizeTimeout: number | null = null;
const handleResize = () => {
  if (resizeTimeout) {
    window.clearTimeout(resizeTimeout);
  }
  resizeTimeout = window.setTimeout(() => {
    // 触发重新计算
    isMobile.value;
  }, 250);
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (resizeTimeout) {
    window.clearTimeout(resizeTimeout);
  }
});

// 获取等级图标
const getBadgeIcon = (level: string) => {
  switch(level) {
    case '高': return '🏆';
    case '中': return '🌟';
    case '低': return '🎯';
    default: return '🎯';
  }
};

// 获取等级文本
const getLevelText = (level: string) => {
  switch(level) {
    case '高': return '优秀水平';
    case '中': return '良好水平';
    case '低': return '继续加油';
    default: return '继续加油';
  }
};

// 获取进度条颜色
const getProgressColor = (percentage: string) => {
  const value = parseFloat(percentage);
  if (value < 40) return "#F56C6C";
  if (value < 70) return "#E6A23C";
  return "#67C23A";
};

// 下载错词讲义
const downloadErrorHandout = async () => {
  downloadLoading.value.handout = true;
  try {
    const response = await downloadWordTestErrorMaterialApi(props.courseId, 'error_handout');
    if (response.code === 200) {
      // 打开下载链接
      window.open(response.data.downloadUrl, '_blank');
      ElMessage.success('错词讲义下载成功');
    } else {
      ElMessage.error(response.msg || '下载失败');
    }
  } catch (error) {
    console.error('下载错词讲义失败:', error);
    ElMessage.error('下载失败');
  } finally {
    downloadLoading.value.handout = false;
  }
};

// 下载错题练习
const downloadErrorExercise = async () => {
  downloadLoading.value.exercise = true;
  try {
    const response = await downloadWordTestErrorMaterialApi(props.courseId, 'error_exercise');
    if (response.code === 200) {
      // 打开下载链接
      window.open(response.data.downloadUrl, '_blank');
      ElMessage.success('错题练习下载成功');
    } else {
      ElMessage.error(response.msg || '下载失败');
    }
  } catch (error) {
    console.error('下载错题练习失败:', error);
    ElMessage.error('下载失败');
  } finally {
    downloadLoading.value.exercise = false;
  }
};

onMounted(() => {
  loading.value = true;
  console.log('showWordTestResult变化加载结果页面');
  getWordByCourseIdApi(props.courseId).then((res) => {
    if(res.code === 200) {
      wordTestedResult.value = res.data;
    } else {
      ElMessage.error("获取测验结果失败");
    }
  }).catch((err) => {
    ElMessage.error("获取测验结果失败");
  }).finally(() => {
    loading.value = false;
    console.log('showWordTestResult变化加载结果页面完成');
  });
});
</script>

<style lang="scss" scoped>
.word-test-result {
  width: 100%;
  height: 100vh;
  max-height: 100%;
  background-color: #fff8e1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  @media (max-width: 768px) {
    height: calc(100vh - 56px); /* 考虑移动端顶部导航栏高度 */
  }

  &.is-mobile {
    .celebration-section {
      margin: 8px;
      padding: 12px 8px;
    }

    .result-container {
      padding: 0 8px 8px;
    }

    .score-grid {
      grid-template-columns: 1fr;
      gap: 8px;
    }
  }
}

/* 删除重复的动画关键帧定义，保留一个优化版本 */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.8) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(5deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translate(0);
  }
  25% {
    transform: translate(-10px, -10px);
  }
  50% {
    transform: translate(0, -15px);
  }
  75% {
    transform: translate(10px, -10px);
  }
}

/* 装饰元素样式 */
.decoration-star {
  position: absolute;
  background: #ffd54f;
  clip-path: polygon(
    50% 0%,
    61% 35%,
    98% 35%,
    68% 57%,
    79% 91%,
    50% 70%,
    21% 91%,
    32% 57%,
    2% 35%,
    39% 35%
  );
  z-index: 1;
  opacity: 0.8;
  transition: all 0.3s ease;
  will-change: transform, opacity;
}

.star1 {
  width: 50px;
  height: 50px;
  top: 20px;
  left: 30px;
}

.star2 {
  width: 35px;
  height: 35px;
  bottom: 40px;
  right: 60px;
}

.star3 {
  width: 25px;
  height: 25px;
  top: 60px;
  right: 100px;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
  pointer-events: none;
}

.circle1 {
  width: 80px;
  height: 80px;
  bottom: 30px;
  left: 40px;
  background: radial-gradient(
    circle,
    rgba(255, 193, 7, 0.15) 0%,
    rgba(255, 193, 7, 0) 70%
  );
  will-change: transform;
}

/* 优化滚动条样式 */
.result-container {
  scrollbar-width: thin;
  scrollbar-color: #ffd54f #fff3e0;
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #fff3e0;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ffd54f;
    border-radius: 3px;
    border: 1px solid #fff3e0;

    &:hover {
      background: #ffb300;
    }
  }
}

/* 添加平滑过渡效果 */
.result-card,
.celebration-section,
.score-item,
.plan-card {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

/* 优化动画性能 */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.8) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(5deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translate(0);
  }
  25% {
    transform: translate(-10px, -10px);
  }
  50% {
    transform: translate(0, -15px);
  }
  75% {
    transform: translate(10px, -10px);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-15px) translateX(15px);
  }
  50% {
    transform: translateY(0) translateX(30px);
  }
  75% {
    transform: translateY(15px) translateX(15px);
  }
}

.celebration-section {
  background: linear-gradient(135deg, #ffb74d, #ff9800);
  padding: 20px 15px;
  border-radius: 16px;
  margin: 15px 15px 10px;
  box-shadow: 0 8px 16px rgba(255, 152, 0, 0.3);
  position: relative;
  z-index: 2;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 16px;
  }

  .celebration-content {
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;

    .celebration-icon {
      font-size: 32px;
      margin: 0 12px;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .celebration-text {
      h1.wave-text {
        font-size: 28px;
        margin-bottom: 8px;
        font-weight: bold;
        background: linear-gradient(45deg, #fff, #ffe082);
        background-size: 200% auto;
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
      }

      .wave-text-sub {
        font-size: 16px;
        margin-bottom: 6px;
        color: rgba(255, 255, 255, 0.95);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        line-height: 1.4;

        &:last-of-type {
          margin-bottom: 12px;
        }
      }

      p.prompt-action {
        font-size: 18px;
        font-weight: bold;
        color: #fff3e0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        margin-top: 4px;
        position: relative;
        display: inline-block;
        padding: 4px 16px;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.1);
        
        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 10%;
          right: 10%;
          height: 2px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 2px;
        }
      }
    }
  }
}

@keyframes shine {
  0% {
    opacity: 0.5;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
    transform: translateX(100%);
  }
}

.result-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 15px;
  padding: 0 15px 15px;
  overflow-y: auto;
  position: relative;
  z-index: 2;
  flex: 1;
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 213, 79, 0.2);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ffd54f;
    border-radius: 4px;
    
    &:hover {
      background: #ffb300;
    }
  }
}

@keyframes wave {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.result-card {
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.15);
  border: 2px solid #ffe082;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffb74d, #ffd54f);
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(255, 193, 7, 0.2);
    border-color: #ffb74d;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, 
        transparent 0%, 
        #ffe082 20%, 
        #ffe082 80%, 
        transparent 100%
      );
    }

    .header-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      color: #f57c00;

      .el-icon {
        margin-right: 6px;
        font-size: 18px;
        color: #ffb74d;
        filter: drop-shadow(0 1px 2px rgba(255, 183, 77, 0.3));
      }
    }

    .test-time {
      display: flex;
      align-items: center;
      color: #ff9800;
      font-size: 12px;
      background: #fff3e0;
      padding: 4px 8px;
      border-radius: 12px;

      .el-icon {
        margin-right: 3px;
        color: #ffb74d;
      }
    }
  }
}

.score-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;

  .score-item {
    text-align: center;
    padding: 14px 10px;
    background: #fff8e1;
    border-radius: 12px;
    border: 2px solid #ffe082;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, transparent, rgba(255, 213, 79, 0.1), transparent);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    &:hover {
      transform: translateY(-3px);
      border-color: #ffb74d;
      box-shadow: 0 6px 12px rgba(255, 183, 77, 0.2);

      &::before {
        transform: translateX(100%);
      }
    }

    &.highlight {
      background: #fff3e0;
      border-color: #ffb74d;

      .score-value {
        color: #f57c00;
        text-shadow: 0 1px 2px rgba(245, 124, 0, 0.2);
      }
    }

    .score-icon {
      font-size: 24px;
      margin-bottom: 8px;
      filter: drop-shadow(0 2px 3px rgba(255, 183, 77, 0.3));
    }

    .score-value {
      font-size: 22px;
      font-weight: bold;
      color: #ff9800;
      margin-bottom: 4px;
      transition: all 0.3s ease;
    }

    .score-label {
      font-size: 13px;
      color: #ff9800;
      opacity: 0.8;
    }
  }
}

.vocabulary-info {
  background: #fff3e0;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  border: 2px solid #ffe082;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 213, 79, 0.1) 0%, transparent 70%);
  }

  .vocab-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    color: #f57c00;
    margin-bottom: 16px;
    position: relative;
    z-index: 1;

    .el-icon {
      margin-right: 8px;
      font-size: 24px;
      color: #ffb74d;
    }
  }

  .vocab-content {
    display: flex;
    align-items: baseline;
    position: relative;
    z-index: 1;

    .vocab-number {
      font-size: 40px;
      font-weight: bold;
      color: #f57c00;
      margin-right: 12px;
      text-shadow: 0 2px 4px rgba(245, 124, 0, 0.2);
    }

    .vocab-description {
      margin-right: 12px;
      font-size: 18px;
      color: #ff9800;
    }
  }
}

.level-badge {
  text-align: center;
  padding: 24px;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  }

  &.level-low {
    background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%);
    border-color: #ff9800;
  }

  &.level-medium {
    background: linear-gradient(135deg, #ffd54f 0%, #ffb300 100%);
    border-color: #ffb300;
  }

  &.level-high {
    background: linear-gradient(135deg, #ffca28 0%, #ffa000 100%);
    border-color: #ffa000;
  }

  .badge-icon {
    font-size: 48px;
    margin-bottom: 12px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  }

  .badge-text {
    color: white;
    font-size: 20px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.progress-items {
  .progress-item {
    margin-bottom: 16px;
    padding: 12px;
    background: #fff8e1;
    border-radius: 12px;
    border: 2px solid #ffe082;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      border-color: #ffb74d;
      transform: translateX(3px);
      box-shadow: 0 3px 8px rgba(255, 183, 77, 0.15);
    }

    .progress-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      color: #f57c00;

      .progress-value {
        font-weight: bold;
        color: #ff9800;
        background: #fff3e0;
        padding: 3px 10px;
        border-radius: 10px;
        border: 1px solid #ffb74d;
        font-size: 13px;
      }
    }

    :deep(.el-progress-bar__outer) {
      background-color: #fff3e0;
      border-radius: 6px;
      height: 10px !important;
    }

    :deep(.el-progress-bar__inner) {
      border-radius: 6px;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0) 100%
        );
      }
    }
  }
}

.progress-items {
  .progress-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #fff8e1;
    border-radius: 12px;
    border: 2px solid #ffe082;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      border-color: #ffb74d;
      transform: translateX(3px);
      box-shadow: 0 3px 8px rgba(255, 183, 77, 0.15);
    }

    .progress-item {
      margin-bottom: 16px;

      .progress-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
        color: #f57c00;

        .progress-value {
          font-weight: bold;
          color: #ff9800;
          background: #fff3e0;
          padding: 3px 10px;
          border-radius: 10px;
          border: 1px solid #ffb74d;
          font-size: 13px;
        }
      }

      :deep(.el-progress-bar__outer) {
        background-color: #fff3e0;
        border-radius: 6px;
        height: 10px !important;
      }

      :deep(.el-progress-bar__inner) {
        border-radius: 6px;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0) 100%
          );
        }
      }
    }

    .suggestion-content {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 8px;
      position: relative;

      .suggestion-icon {
        font-size: 20px;
        flex-shrink: 0;
        margin-top: 2px;
      }

      .suggestion-text {
        flex: 1;
        color: #f57c00;
        line-height: 1.6;
        font-size: 14px;
      }
    }
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .progress-items {
    .progress-section {
      padding: 12px;
      margin-bottom: 16px;

      .progress-item {
        margin-bottom: 12px;

        .progress-label {
          font-size: 13px;

          .progress-value {
            font-size: 12px;
            padding: 2px 8px;
          }
        }
      }

      .suggestion-content {
        padding: 10px;
        gap: 8px;

        .suggestion-icon {
          font-size: 18px;
        }

        .suggestion-text {
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .progress-items {
    .progress-section {
      padding: 10px;
      margin-bottom: 12px;

      .suggestion-content {
        padding: 8px;
        
        .suggestion-text {
          font-size: 12px;
        }
      }
    }
  }
}

.plan-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;

  .plan-card {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: #fff8e1;
    border-radius: 16px;
    border: 2px solid #ffe082;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #ffb74d;
      transform: translateY(-3px);
      box-shadow: 0 6px 16px rgba(255, 183, 77, 0.15);

      .plan-icon {
        transform: scale(1.1) rotate(5deg);
        background: #fff3e0;
      }
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0) 50%,
        rgba(255, 255, 255, 0.2) 100%
      );
      pointer-events: none;
    }

    .plan-icon {
      font-size: 28px;
      flex-shrink: 0;
      background: #fff3e0;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      border: 2px solid #ffb74d;
      box-shadow: 0 3px 6px rgba(255, 183, 77, 0.2);
      transition: all 0.3s ease;
    }

    .plan-info {
      flex: 1;

      .plan-title {
        font-weight: bold;
        color: #f57c00;
        margin-bottom: 10px;
        font-size: 16px;
        position: relative;
        display: inline-block;

        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          width: 100%;
          height: 2px;
          background: linear-gradient(90deg, #ffb74d, transparent);
          border-radius: 1px;
        }
      }

      .plan-text {
        color: #ff9800;
        line-height: 1.6;
        font-size: 14px;
        background: rgba(255, 255, 255, 0.5);
        padding: 10px;
        border-radius: 8px;
      }
    }
  }
}

// 下载卡片样式
.download-card {
  .download-buttons {
    display: flex;
    gap: 16px;
    margin-top: 16px;
    justify-content: center;

    .el-button {
      flex: 1;
      max-width: 200px;
      height: 48px;
      font-size: 16px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .plan-cards {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-top: 12px;
  }

  .plan-card {
    padding: 16px;
    gap: 12px;

    .plan-icon {
      font-size: 24px;
      width: 40px;
      height: 40px;
    }

    .plan-info {
      .plan-title {
        font-size: 15px;
        margin-bottom: 8px;
      }

      .plan-text {
        font-size: 13px;
        padding: 8px;
      }
    }
  }
}

@media (max-width: 480px) {
  .plan-cards {
    gap: 10px;
    margin-top: 10px;
  }

  .plan-card {
    padding: 12px;
    gap: 10px;

    .plan-icon {
      font-size: 20px;
      width: 36px;
      height: 36px;
    }

    .plan-info {
      .plan-title {
        font-size: 14px;
      }

      .plan-text {
        font-size: 12px;
        padding: 6px;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes bounce {
  from { transform: translateY(0); }
  to { transform: translateY(-10px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glowPulse {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(255, 183, 77, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(255, 183, 77, 0.8));
  }
}

@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 响应式布局调整 */
@media (max-width: 1024px) {
  .word-test-result {
    margin: 0;
    height: 100%;
  }

  .result-container {
    grid-template-columns: 1fr;
    padding: 0 12px 12px;
  }

  .celebration-section {
    padding: 16px 12px;
    margin: 12px;

    .celebration-content {
      flex-direction: column;
      gap: 8px;

      .celebration-text {
        h1.wave-text {
          font-size: 24px;
          margin-bottom: 6px;
        }

        .wave-text-sub {
          font-size: 14px;
          margin-bottom: 4px;
        }

        p.prompt-action {
          font-size: 16px;
          padding: 3px 12px;
        }
      }

      .celebration-icon {
        font-size: 28px;
        margin: 4px 0;
      }
    }
  }
}

@media (max-width: 768px) {
  .score-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;

    .score-item {
      padding: 12px;

      &.highlight {
        grid-column: span 2;
      }

      .score-icon {
        font-size: 24px;
      }

      .score-value {
        font-size: 20px;
      }

      .score-label {
        font-size: 12px;
      }
    }
  }

  .vocabulary-info {
    padding: 16px;

    .vocab-content {
      .vocab-number {
        font-size: 28px;
      }

      .vocab-description {
        font-size: 14px;
      }
    }
  }

  .suggestion-tabs {
    :deep(.el-tabs__item) {
      font-size: 13px;
      padding: 0 12px;
      height: 36px;
      line-height: 36px;
    }
  }

  .plan-cards {
    gap: 12px;

    .plan-card {
      padding: 12px;

      .plan-icon {
        font-size: 24px;
      }

      .plan-info {
        .plan-title {
          font-size: 13px;
        }

        .plan-text {
          font-size: 12px;
        }
      }
    }
  }

  .decoration-star {
    &.star1 {
      width: 32px;
      height: 32px;
    }

    &.star2 {
      width: 24px;
      height: 24px;
    }

    &.star3 {
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: 480px) {
  .celebration-section {
    padding: 12px 8px;
    margin: 8px;

    .celebration-content {
      .celebration-text {
        h1.wave-text {
          font-size: 20px;
          margin-bottom: 4px;
        }

        .wave-text-sub {
          font-size: 13px;
          margin-bottom: 3px;
        }

        p.prompt-action {
          font-size: 14px;
          padding: 2px 10px;
        }
      }

      .celebration-icon {
        font-size: 24px;
        margin: 3px 0;
      }
    }
  }

  .score-grid {
    grid-template-columns: 1fr;
    gap: 8px;

    .score-item {
      &.highlight {
        grid-column: span 1;
      }
    }
  }

  .result-container {
    padding: 0 8px 8px;
  }
}

/* 优化动画性能 */
@media (prefers-reduced-motion: reduce) {
  .celebration-section,
  .celebration-icon,
  .wave-text,
  .result-card,
  .score-item,
  .plan-card {
    animation: none !important;
    transform: none !important;
    transition: none !important;
  }
}

/* 添加触摸设备的特殊处理 */
@media (hover: none) {
  .result-card,
  .score-item,
  .plan-card {
    &:hover {
      transform: none;
    }
  }

  .suggestion-tabs {
    :deep(.el-tabs__item) {
      &:hover {
        color: inherit;
      }
    }
  }
}
</style>