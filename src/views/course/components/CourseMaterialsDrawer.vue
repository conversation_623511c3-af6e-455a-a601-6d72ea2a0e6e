<template>
  <el-drawer
    v-model="visible"
    title="正课资料"
    size="400px"
    :with-header="false"
    direction="rtl"
    :before-close="handleClose"
    style="padding: 0"
  >
    <div class="materials-container" v-loading="loading">
      <div class="materials-header">
        <div class="header-title">正课资料</div>
        <div style="height: calc(100vh - 89px - 60px)">
          <MaterialTree
            :openClass="true"
            :studentId="props.studentId"
            @selectUnit="selectUnit"
          />
        </div>

        <div class="materials-footer">
          <el-button
            type="primary"
            class="start-button"
            @click="startLearning"
            v-if="!generatePdfMode"
            >开始上课 (已选{{ selectedWordList.length }}个单词)</el-button
          >
          <el-button
            type="primary"
            class="start-button"
            @click="startLearning"
            v-else
            >打印讲义 (已选{{ selectedWordList.length }}个单词)</el-button
          >
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { MaterialTree } from "../../teacher/word-manager/components";
import { startCourseLearningApi, generatePdfApi } from "../../../api/course";
import { ElMessage } from "element-plus";
import { BookTree } from "../../../api/textbook";

interface Props {
  visible: boolean;
  studentId: string;
  courseId: string;
  generatePdf: boolean;
}

interface Unit {
  name: string;
  wordCount: number;
  selected: boolean;
}

interface CourseSection {
  title: string;
  units: Unit[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "close"): void;
  (e: "startLearning", courseInfo: any): void;
}>();

// 计算属性来确保响应式更新
const generatePdfMode = computed(() => props.generatePdf);

// 搜索关键字
const loading = ref(false);

const selectedWordList = ref<BookTree[]>([]);

const textbookIdSet = new Set<string>();
const unitIdSet = new Set<string>();
const wordIds = ref<string[]>([]);

const selectUnit = (unitIdArray: BookTree[]) => {
  // console.log("选中单元节点：" + unitIdArray);
  selectedWordList.value = unitIdArray;
};

// 关闭抽屉
const handleClose = () => {
  emit("update:visible", false);
  emit("close");
};

// 开始学习
const startLearning = () => {
  loading.value = true;
  textbookIdSet.clear();
  unitIdSet.clear();
  wordIds.value = [];
  let textbookItems: string[] = [];
  selectedWordList.value.forEach((nodeInfo) => {
    textbookIdSet.add(nodeInfo.textbookId);
    unitIdSet.add(nodeInfo.parentNodeId);
    wordIds.value.push(nodeInfo.wordId);
    textbookItems.push(nodeInfo.nodeId);
  });

  // if (textbookIdSet.size > 1) {
  //   loading.value = false;
  //   ElMessage.error("一次只能选择一本教材的单词");
  //   return;
  // }

  // if (unitIdSet.size > 1) {
  //   loading.value = false;
  //   ElMessage.error("一次只能选择一个单元的单词");
  //   return;
  // }

  if (generatePdfMode.value) {
    generatePdfApi(props.courseId, textbookItems).then((res) => {
      if (res.code === 200) {
        // 假设 res.data 是你接口返回的 data 字段
        const { body, headers } = res.data;

        const blob = base64ToBlob(body, headers["Content-Type"][0]);
        const fileName = getFileName(headers["Content-Disposition"][0]);

        // 3. 创建下载链接
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      }
    }).finally(() =>{
      loading.value = false;
    });
  } else {
    startCourseLearningApi(props.courseId, {
      textbookId: Array.from(textbookIdSet)[0],
      unitId: Array.from(unitIdSet)[0],
      wordIds: wordIds.value,
      textbookItemIds: textbookItems,
    })
      .then((res) => {
        if (res.code === 200) {
          ElMessage.success("开始学习成功");
          emit("startLearning", res.data);
          handleClose();
        } else {
          ElMessage.error("开始上课失败：" + res.message);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

// 1. Base64转Blob
const base64ToBlob = (base64, mime) => {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mime });
};

// 2. 获取文件名
const getFileName = (contentDisposition) => {
  const match = /filename="?([^"]+)"?/.exec(contentDisposition);
  return match ? decodeURIComponent(match[1]) : "download.pdf";
};

// 监听visible属性变化
const visible = computed({
  get: () => props.visible,
  set: (value) => {
    emit("update:visible", value);
  },
});
</script>

<style scoped>
.materials-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

.materials-header {
  /* padding: 20px; */
  height: 100%;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  /* color: #FF9800; */
  margin-bottom: 20px;
}

.search-box {
  margin-bottom: 20px;
}

.search-box .el-input {
  border-radius: 20px;
}

.search-box .el-input__inner {
}

.tab-buttons {
  margin-bottom: 20px;
}

.tab-buttons .el-radio-group {
  width: 100%;
}

.tab-buttons .el-radio-button {
  flex: 1;
}

.tab-buttons .el-radio-button__inner {
  color: #ff9800;
}

.tab-buttons .el-radio-button.is-active .el-radio-button__inner {
  border-color: #ff9800;
  color: white;
  box-shadow: none;
}

.materials-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  background-color: white;
}

.course-section {
  margin-bottom: 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  padding: 15px 20px;
  background: linear-gradient(135deg, #fff8e1, #ffecb3);
  font-weight: bold;
  color: #ff9800;
}

.unit-list {
  padding: 10px 20px;
}

.unit-item {
  padding: 12px 0;
  transition: all 0.3s;
}

.unit-item:hover {
  background-color: #fff3e0;
}

.unit-item:last-child {
  border-bottom: none;
}

.materials-footer {
  padding: 20px;
  background-color: white;
  text-align: center;
}

.start-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  font-weight: bold;
  background: linear-gradient(135deg, #ff9800, #ffc107);
  border: none;
  transition: all 0.3s;
}

.start-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}
</style>
