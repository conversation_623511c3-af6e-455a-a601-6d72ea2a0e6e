<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">{{ (stepIndex || 0) + 1 }}. 句子填空</div>
    </div>

    <div class="stage-content">
<!--      <div class="word-display">-->
<!--        <span class="word-display-text">{{ currentStepInfo?.wordInfo?.word }}</span>-->
<!--      </div>-->
      <div class="arrangement-card">
        <div class="chinese-sentence">{{ currentStepInfo?.wordInfo?.sentences?.sentenceCn }}</div>
        <div class="sentence-video-text">
          <span class="pronunciation uk-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUkUrl)" style="margin-right: 20px">英式 <el-icon><VideoPlay /></el-icon></span>
          <span class="pronunciation uk-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUsUrl)">美式 <el-icon><VideoPlay /></el-icon></span>
        </div>

        <div class="fill-blank-container">
          <div v-if="isMounted && showResult === false">
            <template v-for="(char, index) in currentStepInfo?.wordInfo?.sentences?.sentenceEn?.split(/\s+/)" :key="index">
              <div
                  v-if="shouldShowInput(index)"
                  class="click-zone"
                  :class="{
                    'click-zone-active': selectedPosition === index,
                    'click-zone-filled': droppedWords[index]
                  }"
                  @click="selectPosition(index)">
                <span v-if="droppedWords[index]" class="dropped-word">{{ droppedWords[index] }}</span>
                <span v-else class="drop-placeholder">点击选择此位置</span>
              </div>
              <!-- 其他字符渲染为 span -->
              <span v-else class="text">{{ char }}</span>
            </template>
          </div>
          <div v-if="isMounted && showResult === true">
            <template v-for="(char, index) in currentStepInfo?.wordInfo?.sentences?.sentenceEn?.split(/\s+/)" :key="index">
              <div
                  v-if="shouldShowInput(index)"
                  class="click-zone result-zone">
                <span class="dropped-word">{{ getDisplayWord(index) }}</span>
              </div>
              <!-- 其他字符渲染为 span -->
              <span v-else class="text">{{ char }}</span>
            </template>
          </div>
        </div>
        <div class="sentence-parts">
          <div v-for="(part, index) in currentStepInfo?.step?.sentenceOrder" :key="index"
               class="clickable-word"
               :class="{ 'word-used': usedWords.includes(index) }"
               @click="handleWordClick(part, index)">
            <span style="font-size: 20px;margin-left: 20px">{{ (index+1) }}. </span><span class="part-item">{{ part }}</span>
          </div>
        </div>
        <div class="action-buttons" v-if="showResult === false">
          <el-button type="warning" size="mini" class="reset-btn" @click="handleReset">
            重新开始
          </el-button>
          <el-button type="primary" size="mini" class="submit-btn" @click="handleSubmit">
            检查答案
          </el-button>
        </div>
      </div>
    </div>

    <!-- 答对时的星星特效 -->
    <div v-if="showStars" class="stars-container">
      <div v-for="n in 10" :key="n" class="star" :style="getRandomStarStyle()"></div>
    </div>

    <!-- 答对时的奖励徽章 -->
    <div v-if="showReward" class="reward-badge">
      <el-icon class="reward-icon">
        <Trophy />
      </el-icon>
      <span class="reward-text">答对啦</span>
    </div>

  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, onUnmounted, ref} from 'vue'
import {Trophy, VideoPlay} from '@element-plus/icons-vue'
import {
  COURSE_sessionStorage_INFO_KEY,
  CurrentStepInfo,
  getStepInfoByType,
  getWordStepResultByWordId,
  submitCourseStepApi
} from "@/api/course";
import {getRandomStarStyle, playAudioUtil, useAudioPreloader} from '@/api/course/util'
import {ElMessage, ElMessageBox} from "element-plus";

const emit = defineEmits(['complete'])
const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  },
  stepIndex: Number,
  sectionId: String
})
const selectedOption = ref(-1)
const showResult = ref(false)
const showStars = ref(false)
const showReward = ref(false)
const currentStepInfo = ref<CurrentStepInfo | null>(null);
const correctAnswer = ref(currentStepInfo?.step?.answer) // 当前单词的正确答案
const answerResult = ref(false)
const inputs = ref<number[]>([])
const studentAnswers = ref<number[]>([])
const answerFlag = ref<string | null>(null)
const isMounted = ref(false)

// 音频预加载相关
let audioPreloader: ReturnType<typeof useAudioPreloader> | null = null;

// 点击选择相关的状态
const droppedWords = ref<{[key: number]: string}>({}) // 已放置的单词
const usedWords = ref<number[]>([]) // 已使用的单词索引
const selectedPosition = ref<number>(-1) // 当前选中的位置
const fillHistory = ref<Array<{position: number, word: string, wordIndex: number}>>([]) // 填入历史记录

const finalText = computed(() => {
  if (droppedWords.value == null || !currentStepInfo.value?.wordInfo?.sentences?.sentenceEn) {
    return null;
  }
  
  // 获取原始句子并分割成单词数组
  const sentenceWords = currentStepInfo.value.wordInfo.sentences.sentenceEn.split(/\s+/);
  
  // 创建结果数组，初始为原始句子的单词
  const resultWords = [...sentenceWords];
  
  // 替换需要填空的位置
  Object.entries(droppedWords.value).forEach(([index, word]) => {
    const idx = parseInt(index);
    if (!isNaN(idx) && idx >= 0 && idx < resultWords.length) {
      resultWords[idx] = word;
    }
  });
  
  // 重新组合成完整句子
  return resultWords.join(' ');
})

// 点击选择相关方法
const selectPosition = (index: number) => {
  if (showResult.value) return

  // 如果点击的是已填入的位置，清空它
  if (droppedWords.value[index]) {
    clearPosition(index)
    return
  }

  // 选中位置
  selectedPosition.value = index
}

const handleWordClick = (word: string, wordIndex: number) => {
  if (showResult.value || usedWords.value.includes(wordIndex)) return

  let targetPosition = selectedPosition.value

  // 如果没有选中位置，自动选择第一个空白位置
  if (targetPosition === -1 || droppedWords.value[targetPosition]) {
    const blankIndices = currentStepInfo.value?.step?.options || []
    // 将字符串索引转换为数字并排序，然后找到第一个空白位置
    const sortedIndices = blankIndices
      .map(indexStr => Number(indexStr))
      .sort((a, b) => a - b)

    const firstEmptyPosition = sortedIndices.find(index => !droppedWords.value[index])

    if (firstEmptyPosition !== undefined) {
      targetPosition = firstEmptyPosition
    } else {
      return // 没有空白位置
    }
  }

  // 记录填入历史
  fillHistory.value.push({
    position: targetPosition,
    word: word,
    wordIndex: wordIndex
  })

  // 填入单词
  droppedWords.value[targetPosition] = word
  usedWords.value.push(wordIndex)
  inputs.value[targetPosition] = wordIndex + 1

  // 清除选中状态
  selectedPosition.value = -1
}

const clearPosition = (index: number) => {
  if (showResult.value || !droppedWords.value[index]) return

  const wordText = droppedWords.value[index]
  const wordIndex = currentStepInfo.value?.step?.sentenceOrder.findIndex(w => w === wordText)

  if (wordIndex !== undefined && wordIndex >= 0) {
    // 从已使用列表中移除
    const usedIndex = usedWords.value.indexOf(wordIndex)
    if (usedIndex > -1) {
      usedWords.value.splice(usedIndex, 1)
    }
  }

  // 清除位置
  delete droppedWords.value[index]
  delete inputs.value[index]

  // 从历史记录中移除相关记录
  const historyIndex = fillHistory.value.findIndex(h => h.position === index)
  if (historyIndex > -1) {
    fillHistory.value.splice(historyIndex, 1)
  }
}

const getDisplayWord = (index: number) => {
  if (showResult.value && studentAnswers.value[index]) {
    const answerIndex = studentAnswers.value[index] - 1
    return currentStepInfo.value?.step?.sentenceOrder[answerIndex] || ''
  }
  return droppedWords.value[index] || ''
}

const selectPart = (index: number) => {
  console.log('Selected part:', index)
}

const selectOption = (option: number) => {
  if (showResult.value) return // 如果已经显示结果，不允许再次选择
  selectedOption.value = option

  showResult.value = true

  if (currentStepInfo?.step?.options[option]?.text == correctAnswer.value) {
    showSuccessEffects()
  }
}

// 判断是否应该显示 input
const shouldShowInput = (index: number) => {
  return currentStepInfo.value?.step?.options.some(i => Number(i) === index)
}


const showSuccessEffects = () => {
  showStars.value = true
  showReward.value = true
  setTimeout(() => {
    showStars.value = false
    showReward.value = false
  }, 2000)
}

/**
 * 提交课程步骤
 */
const submitCourseStep = ():boolean => {
  // debugger
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成" || showResult.value === true) {
    return true;
  }
  if (inputs.value == null || inputs.value.length === 0)  {
    // ElMessageBox.alert("还未选择答案哦，请先选择您的答案吧");
    return false;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: answerFlag.value,
    studentAnswer: inputs.value.map(i => Number(i))?.join(', ')
  }
  submitCourseStepApi(props.courseId, props.sectionId, wordId, submitResult, true)
  return true;
}

const handleReset = () => {
  // 清空所有已放置的单词
  droppedWords.value = {}
  // 清空已使用的单词索引
  usedWords.value = []
  // 清空输入数组
  inputs.value = []
  // 重置选择状态
  selectedPosition.value = -1
  // 清空历史记录
  fillHistory.value = []
}

const handleSubmit = () => {
  // console.log(finalText.value);
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return true;
  }
  // 用户输入的结果
  if (inputs.value == null || inputs.value.length === 0)  {
    answerFlag.value = "错误"
    ElMessage({
      message: '请填写您的答案哦！',
      type: 'error',
      duration: 2000
    })
    return;
  }
  // let studentAnswer = inputs.value.map(i => (Number(i) - 1))
  // 如果作答的 不是题目中的一部分
  if(currentStepInfo.value?.wordInfo?.sentences?.sentenceEn?.trim().includes(finalText.value?.trim()) === false) {
    answerFlag.value = "错误"
    ElMessage({
      message: '答案不正确，请再试一次！',
      type: 'error',
      duration: 2000
    })
    return;
  }
  answerFlag.value = "正确"
  showSuccessEffects()
  // 播放例句发音
  playAudioUtil(currentStepInfo.value?.wordInfo?.sentences?.audioUkUrl)
}

onMounted(async () => {
  // debugger
  // 使用props传入的courseId
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      isMounted.value = true
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '句子填空', props.selectWordText)
      
      // 预加载音频文件
      if (currentStepInfo.value?.wordInfo?.sentences) {
        const audioUrls = [
          currentStepInfo.value.wordInfo.sentences.audioUkUrl,
          currentStepInfo.value.wordInfo.sentences.audioUsUrl
        ].filter(url => url && url.trim() !== '');
        
        if (audioUrls.length > 0) {
          console.log('开始预加载句子填空音频文件:', audioUrls);
          audioPreloader = useAudioPreloader(audioUrls);
          await audioPreloader.preloadAudios();
          console.log('句子填空音频预加载完成');
        }
      }
      
      if(currentStepInfo.value?.status == '待开始') {
        return ;
      }
      correctAnswer.value = currentStepInfo.value?.step?.answer
      // 获取当前阶段的提交结果
      let submitCourseStepApiParam = getWordStepResultByWordId(props.courseId, props.sectionId, currentStepInfo.value.wordId, courseInfo) || {}
      // 如果本地存在提交结果，直接渲染出结果
      submitCourseStepApiParam?.params?.find((item) => {
        if (item.stepId == currentStepInfo.value.step.id && (item.studentAnswer != null && item.studentAnswer != '自动结束')) {
          showResult.value = true
          studentAnswers.value = item.studentAnswer?.split(', ')?.map(i => Number(i))  // 学生的回答
        }
      })
      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

// 不再需要在组件卸载时清除缓存，因为已在切换单词时清理
onUnmounted(() => {
  // 缓存清理已移至index.vue中的goToNextWord和changeCurrentStageType函数
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  overflow-y: hidden; /* 默认不显示滚动条，优先缩小内容 */
}

.stage-header {
  width: 100%;
  margin-bottom: 20px;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.arrangement-card {
  width: 100%;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}

.word-display {
  margin-bottom: 10px;
  font-size: 70px;
  font-weight: bold;
  color: #5d4037;
}

.word-display-text {
  font-family: "Comic Sans MS", cursive, sans-serif;
  margin-right: 40px;
}

.chinese-sentence {
  font-size: 22px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.instructions {
  color: #FF9800;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}

.sentence-parts {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
}

.part-item {
  padding: 10px 15px;
  background: #fbd4a2;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #eee;
  font-size: 20px;
}

.part-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.arrangement-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.option-item {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 2px solid #f2ad47;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-item:hover:not(.correct, .wrong) {
  background-color: #c9e3ff;
  transform: translateY(-2px);
}

/* 正确答案样式 */
.option-item.correct {
  background-color: #c6f6d5;
  border-color: #48bb78;
  color: #276749;
}

/* 错误答案样式 */
.option-item.wrong {
  background-color: #fed7d7;
  border-color: #f56565;
  color: #c53030;
}

/* 结果图标样式 */
.result-icon {
  font-size: 20px;
}

.result-icon.correct-icon {
  color: #48bb78;
}

.result-icon.wrong-icon {
  color: #f56565;
}


/* 动画关键帧 */
@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(15deg);
  }

  75% {
    transform: rotate(-15deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

/* 星星特效样式 */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.star {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
}

@keyframes starAnimation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0;
  }
}

/* 奖励徽章样式 */
.reward-badge {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background-color: #ffd700;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  z-index: 101;
  pointer-events: none;
}

.reward-icon {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 4px;
}

.reward-text {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

@keyframes badgeAnimation {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }

  70% {
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}
.fill-blank-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
}

.fill-blank-container > div {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  max-width: 100%;
}

.fill-blank-container .text {
  font-size: 26px;
  color: #5d4037;
  font-family: "Comic Sans MS", cursive, sans-serif;
  padding: 10px;
  display: inline-flex;
  align-items: center;
  margin: 5px 0;
}

.letter-input {
  width: 80px !important;
}

.letter-input :deep(.el-input__inner) {
  font-size: 26px;
  height: 80px;
  text-align: center;
  padding: 0;
  border-radius: 8px;
  border: 2px solid #FF9800;
  transition: all 0.3s ease;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.letter-input :deep(.el-input__inner):focus {
  border-color: #f2ad47;
  box-shadow: 0 0 0 2px rgba(242, 173, 71, 0.2);
}
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 30px;
  margin-bottom: 10px; /* 添加底部边距，确保在小屏幕上有足够空间 */
  flex-wrap: wrap;
}

.submit-btn {
  transition: all 0.3s ease;
}

.reset-btn {
  background-color: #f39c12;
  border-color: #f39c12;
  transition: all 0.3s ease;
}

.submit-btn:hover, .reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.submit-btn:hover {
  background-color: #f2ad47;
  border-color: #f2ad47;
}

.reset-btn:hover {
  background-color: #e67e22;
  border-color: #e67e22;
}

.submit-btn:active, .reset-btn:active {
  transform: translateY(0);
}

.sentence-video-text {
  font-size: 26px;
  margin: 10px 0 15px 0;
  cursor: pointer;
  justify-content: center;
  display: flex;
  align-items: center;
}

/* 点击选择区域样式 */
.click-zone {
  min-width: 120px;
  max-width: 300px;
  min-height: 60px;
  border: 2px dashed #FF9800;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff8e1;
  transition: all 0.3s ease;
  margin: 5px;
  padding: 10px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  text-align: center;
  cursor: pointer;
}

.click-zone:hover {
  border-color: #f2ad47;
  background-color: #fff3c4;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(242, 173, 71, 0.3);
}

.click-zone-active {
  border-color: #2196F3;
  background-color: #e3f2fd;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.click-zone-filled {
  border-color: #4CAF50;
  background-color: #e8f5e8;
  border-style: solid;
}

.click-zone-filled:hover {
  border-color: #f44336;
  background-color: #ffebee;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.dropped-word {
  font-size: 24px;
  color: #5d4037;
  font-weight: bold;
  font-family: "Comic Sans MS", cursive, sans-serif;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  line-height: 1.3;
}

.drop-placeholder {
  font-size: 14px;
  color: #999;
  text-align: center;
  padding: 10px;
}

.result-zone {
  border: 2px solid #4caf50;
  background-color: #e8f5e8;
}

/* 可点击单词样式 */
.clickable-word {
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.clickable-word:active:not(.word-used) {
  transform: scale(0.95);
}

.clickable-word:hover:not(.word-used) {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.clickable-word:hover:not(.word-used) .part-item {
  background: #f8d7b0;
  border-color: #FF9800;
}

.word-used {
  opacity: 0.5;
  cursor: not-allowed;
}

.word-used .part-item {
  background-color: #e0e0e0;
  color: #999;
}

/* 响应式设计 - 基于屏幕宽度 */
@media screen and (max-width: 768px) {
  .stage-container {
    padding: 20px 15px;
  }
  
  .stage-title {
    font-size: 20px;
  }
  
  .arrangement-card {
    padding: 15px;
  }
  
  .chinese-sentence {
    font-size: 20px;
  }
  
  .sentence-video-text {
    font-size: 18px;
  }
  
  .fill-blank-container {
    padding: 15px 10px;
  }
  
  .fill-blank-container .text {
    font-size: 22px;
    padding: 8px;
  }
  
  .drop-zone {
    min-width: 100px;
    min-height: 50px;
    padding: 8px;
  }
  
  .dropped-word {
    font-size: 20px;
  }
  
  .part-item {
    padding: 8px 12px;
    font-size: 18px;
  }
  
  .action-buttons {
    margin-top: 20px;
    gap: 12px;
  }
}

@media screen and (max-width: 480px) {
  .stage-container {
    padding: 15px 10px;
  }
  
  .stage-title {
    font-size: 18px;
  }
  
  .arrangement-card {
    padding: 10px;
  }
  
  .chinese-sentence {
    font-size: 18px;
    margin-bottom: 15px;
  }
  
  .sentence-video-text {
    font-size: 16px;
    margin: 8px 0 12px 0;
  }
  
  .fill-blank-container {
    padding: 10px 5px;
  }
  
  .fill-blank-container .text {
    font-size: 18px;
    padding: 5px;
  }
  
  .drop-zone {
    min-width: 80px;
    min-height: 40px;
    padding: 5px;
    margin: 3px;
  }
  
  .dropped-word {
    font-size: 16px;
  }
  
  .drop-placeholder {
    font-size: 12px;
    padding: 5px;
  }
  
  .part-item {
    padding: 6px 10px;
    font-size: 16px;
  }
  
  .draggable-word span:first-child {
    font-size: 16px;
    margin-left: 10px;
  }
  
  .action-buttons {
    margin-top: 15px;
    gap: 8px;
  }
  
  .submit-btn, .reset-btn {
    padding: 15px 25px;
    font-size: 14px;
  }
  
  .reward-badge {
    width: 60px;
    height: 60px;
  }
  
  .reward-icon {
    font-size: 24px;
  }
  
  .reward-text {
    font-size: 12px;
  }
}

/* 响应式设计 - 基于屏幕高度 */
@media screen and (max-height: 700px) {
  .stage-container {
    padding: 20px 15px;
  }
  
  .stage-title {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  .chinese-sentence {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  .sentence-video-text {
    margin: 5px 0 10px 0;
  }
  
  .fill-blank-container {
    padding: 10px 5px;
  }
  
  .sentence-parts {
    margin-bottom: 15px;
    gap: 8px;
  }
  
  .part-item {
    padding: 8px 12px;
  }
  
  .action-buttons {
    margin-top: 15px;
  }
}

@media screen and (max-height: 500px) {
  .stage-container {
    padding: 10px;
  }
  
  .stage-title {
    font-size: 18px;
    margin-bottom: 5px;
  }
  
  .stage-header {
    margin-bottom: 10px;
  }
  
  .chinese-sentence {
    font-size: 18px;
    margin-bottom: 5px;
  }
  
  .sentence-video-text {
    font-size: 16px;
    margin: 3px 0 8px 0;
  }
  
  .fill-blank-container {
    padding: 5px;
  }
  
  .fill-blank-container .text {
    font-size: 16px;
    padding: 3px;
  }
  
  .drop-zone {
    min-height: 35px;
    padding: 3px;
  }
  
  .sentence-parts {
    margin-bottom: 10px;
    gap: 5px;
  }
  
  .part-item {
    padding: 5px 8px;
    font-size: 16px;
  }
  
  .draggable-word span:first-child {
    font-size: 14px;
    margin-left: 5px;
  }
  
  .action-buttons {
    margin-top: 10px;
    gap: 5px;
  }
  
  .submit-btn, .reset-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* 极小屏幕高度 - 启用滚动条 */
@media screen and (max-height: 400px) {
  .stage-container {
    padding: 8px 5px;
    overflow-y: auto; /* 在极小屏幕下启用滚动条 */
  }
  
  .stage-title {
    font-size: 16px;
    margin-bottom: 3px;
  }
  
  .stage-header {
    margin-bottom: 5px;
  }
  
  .chinese-sentence {
    font-size: 16px;
    margin-bottom: 3px;
  }
  
  .sentence-video-text {
    font-size: 14px;
    margin: 2px 0 5px 0;
  }
  
  .fill-blank-container {
    padding: 3px;
  }
  
  .fill-blank-container .text {
    font-size: 14px;
    padding: 2px;
    margin: 3px 0;
  }
  
  .drop-zone {
    min-height: 30px;
    padding: 2px;
  }
  
  .sentence-parts {
    margin-bottom: 5px;
    gap: 3px;
  }
  
  .part-item {
    padding: 3px 6px;
    font-size: 14px;
  }
  
  .draggable-word span:first-child {
    font-size: 12px;
    margin-left: 3px;
  }
  
  .action-buttons {
    margin-top: 5px;
    gap: 3px;
    margin-bottom: 5px;
  }
  
  .submit-btn, .reset-btn {
    padding: 8px 15px;
    font-size: 12px;
  }
  
  .letter-input {
    width: 60px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 20px;
    height: 60px;
  }
  
  .dropped-word {
    font-size: 18px;
  }
}
.fill-blank-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
}

.fill-blank-container > div {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  max-width: 100%;
}

.fill-blank-container .text {
  font-size: 26px;
  color: #5d4037;
  font-family: "Comic Sans MS", cursive, sans-serif;
  padding: 10px;
  display: inline-flex;
  align-items: center;
  margin: 5px 0;
}

.letter-input {
  width: 80px !important;
}

.letter-input :deep(.el-input__inner) {
  font-size: 26px;
  height: 80px;
  text-align: center;
  padding: 0;
  border-radius: 8px;
  border: 2px solid #FF9800;
  transition: all 0.3s ease;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.letter-input :deep(.el-input__inner):focus {
  border-color: #f2ad47;
  box-shadow: 0 0 0 2px rgba(242, 173, 71, 0.2);
}
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 30px;
  margin-bottom: 10px; /* 添加底部边距，确保在小屏幕上有足够空间 */
  flex-wrap: wrap;
}

.submit-btn {
  transition: all 0.3s ease;
}

.reset-btn {
  background-color: #f39c12;
  border-color: #f39c12;
  transition: all 0.3s ease;
}

.submit-btn:hover, .reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.submit-btn:hover {
  background-color: #f2ad47;
  border-color: #f2ad47;
}

.reset-btn:hover {
  background-color: #e67e22;
  border-color: #e67e22;
}

.submit-btn:active, .reset-btn:active {
  transform: translateY(0);
}

@media (max-width: 480px) {
  .submit-btn, .reset-btn {
    padding: 15px 25px;
    font-size: 14px;
  }
  
  .action-buttons {
    gap: 10px;
  }
}

.sentence-video-text {
  font-size: 20px;
  margin: 10px 0 15px 0;
  cursor: pointer;
  justify-content: center;
  display: flex;
}

/* 拖拽区域样式 */
.drop-zone {
  min-width: 120px;
  max-width: 300px;
  min-height: 60px;
  border: 2px dashed #FF9800;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff8e1;
  transition: all 0.3s ease;
  margin: 5px;
  padding: 10px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  text-align: center;
}

.dropped-word {
  font-size: 24px;
  color: #5d4037;
  font-weight: bold;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.result-zone {
  border: 2px solid #4caf50;
  background-color: #e8f5e8;
}

/* 可拖拽单词样式 */
.draggable-word {
  cursor: grab;
  transition: all 0.3s ease;
  user-select: none;
}

.draggable-word:active {
  cursor: grabbing;
}

.draggable-word:hover:not(.word-used) {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.word-used {
  opacity: 0.5;
  cursor: not-allowed;
}

.word-used .part-item {
  background-color: #e0e0e0;
  color: #999;
}

/* 动态字体缩放以避免滚动 */
@media (max-height: 800px) {
  .stage-container {
    font-size: 14px;
    padding: 18px;
  }
  
  .stage-title {
    font-size: 22px !important;
  }
  
  .word-display {
    font-size: 65px !important;
  }
  
  .chinese-sentence {
    font-size: 20px !important;
  }
  
  .part-item {
    font-size: 18px !important;
    padding: 8px 12px !important;
  }
  
  .option-item {
    font-size: 16px !important;
    padding: 18px !important;
  }
  
  .dropped-word {
    font-size: 22px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 24px !important;
    height: 75px !important;
  }
  
  .reward-badge {
    width: 75px !important;
    height: 75px !important;
  }
  
  .reward-icon {
    font-size: 30px !important;
  }
  
  .reward-text {
    font-size: 15px !important;
  }
}

@media (max-height: 700px) {
  .stage-container {
    font-size: 12px;
    padding: 15px;
  }
  
  .stage-title {
    font-size: 20px !important;
  }
  
  .word-display {
    font-size: 55px !important;
  }
  
  .chinese-sentence {
    font-size: 18px !important;
  }
  
  .part-item {
    font-size: 16px !important;
    padding: 6px 10px !important;
  }
  
  .option-item {
    font-size: 14px !important;
    padding: 15px !important;
  }
  
  .dropped-word {
    font-size: 20px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 22px !important;
    height: 65px !important;
  }
  
  .arrangement-card {
    padding: 15px !important;
  }
  
  .reward-badge {
    width: 70px !important;
    height: 70px !important;
  }
  
  .reward-icon {
    font-size: 28px !important;
  }
  
  .reward-text {
    font-size: 14px !important;
  }
}

@media (max-height: 600px) {
  .stage-container {
    font-size: 11px;
    padding: 12px;
  }
  
  .stage-title {
    font-size: 18px !important;
  }
  
  .word-display {
    font-size: 45px !important;
  }
  
  .chinese-sentence {
    font-size: 16px !important;
  }
  
  .part-item {
    font-size: 14px !important;
    padding: 5px 8px !important;
  }
  
  .option-item {
    font-size: 12px !important;
    padding: 12px !important;
  }
  
  .dropped-word {
    font-size: 18px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 20px !important;
    height: 55px !important;
  }
  
  .arrangement-card {
    padding: 12px !important;
  }
  
  .reward-badge {
    width: 60px !important;
    height: 60px !important;
  }
  
  .reward-icon {
    font-size: 24px !important;
  }
  
  .reward-text {
    font-size: 12px !important;
  }
}

@media (max-height: 500px) {
  .stage-container {
    font-size: 10px;
    padding: 10px;
  }
  
  .stage-title {
    font-size: 16px !important;
  }
  
  .word-display {
    font-size: 35px !important;
  }
  
  .chinese-sentence {
    font-size: 14px !important;
  }
  
  .part-item {
    font-size: 12px !important;
    padding: 4px 6px !important;
  }
  
  .option-item {
    font-size: 10px !important;
    padding: 10px !important;
  }
  
  .dropped-word {
    font-size: 16px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 18px !important;
    height: 45px !important;
  }
  
  .arrangement-card {
    padding: 10px !important;
  }
  
  .reward-badge {
    width: 50px !important;
    height: 50px !important;
  }
  
  .reward-icon {
    font-size: 20px !important;
  }
  
  .reward-text {
    font-size: 10px !important;
  }
}
.pronunciation .el-icon {
  margin-left: 8px;
  color: #FF9800;
  font-size: 26px;
  display: inline-flex;
  align-items: center;
}
.pronunciation:hover {
  color: #FF9800;
  transform: translateY(-2px);
}
</style>