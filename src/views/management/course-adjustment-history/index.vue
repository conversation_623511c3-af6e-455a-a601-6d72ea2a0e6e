<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>课时调整历史查询</span>
        </div>
      </template>

      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
        <el-form-item label="学生姓名" prop="studentName">
          <el-input
            v-model="queryParams.studentName"
            placeholder="请输入学生姓名"
            clearable
            :disabled="isStudentLocked"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学生手机" prop="studentPhone">
          <el-input
            v-model="queryParams.studentPhone"
            placeholder="请输入学生手机号"
            clearable
            :disabled="isStudentLocked"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select
            v-model="queryParams.subject"
            placeholder="请选择学科"
            clearable
            :disabled="isCourseLocked"
            style="width: 150px"
            @change="handleSubjectChange"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        <el-form-item label="课型" prop="specification">
          <el-select
            v-model="queryParams.specification"
            placeholder="请选择课型"
            clearable
            :disabled="isCourseLocked"
            style="width: 150px"
          >
            <el-option
              v-for="option in availableSpecifications"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="操作人" prop="operatorName">
          <el-input
            v-model="queryParams.operatorName"
            placeholder="请输入操作人"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="调整时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 锁定信息提示 -->
      <el-alert
        v-if="lockedInfo"
        :title="lockedInfo"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="historyList" border>
        <el-table-column label="学生姓名" prop="studentName" width="100" />
        <el-table-column label="学生手机" prop="studentPhone" width="120" />
        <el-table-column label="学科" prop="subject" width="80" />
        <el-table-column label="课型" prop="specification" width="100" />
        <el-table-column label="性质" prop="nature" width="80" />
        <el-table-column label="调整类型" prop="adjustmentType" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.adjustmentType === 'increase' ? 'success' : 'warning'">
              {{ scope.row.adjustmentType === 'increase' ? '增加' : '减少' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="总调整" prop="adjustmentHours" width="100" align="right">
          <template #default="scope">
            <span :class="scope.row.adjustmentType === 'increase' ? 'text-success' : 'text-warning'">
              {{ scope.row.adjustmentType === 'increase' ? '+' : '-' }}{{ scope.row.adjustmentHours }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="调整详情" width="200" align="center">
          <template #default="scope">
            <div class="adjustment-details">
              <div v-if="scope.row.purchasedHoursAdjustment !== 0" class="detail-item">
                <span class="detail-label">购买:</span>
                <span :class="scope.row.purchasedHoursAdjustment > 0 ? 'text-success' : 'text-warning'">
                  {{ scope.row.purchasedHoursAdjustment > 0 ? '+' : '' }}{{ scope.row.purchasedHoursAdjustment }}
                </span>
              </div>
              <div v-if="scope.row.giftHoursAdjustment !== 0" class="detail-item">
                <span class="detail-label">赠送:</span>
                <span :class="scope.row.giftHoursAdjustment > 0 ? 'text-success' : 'text-warning'">
                  {{ scope.row.giftHoursAdjustment > 0 ? '+' : '' }}{{ scope.row.giftHoursAdjustment }}
                </span>
              </div>
              <div v-if="scope.row.purchasedHoursAdjustment === 0 && scope.row.giftHoursAdjustment === 0" class="no-detail">
                <span class="text-muted">无详情</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="调整前" width="150" align="center">
          <template #default="scope">
            <div class="before-after-info">
              <div class="info-item">
                <span class="info-label">总:</span>
                <span>{{ scope.row.beforeTotalHours }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">剩余:</span>
                <span>{{ scope.row.beforeRemainingHours }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">购买:</span>
                <span>{{ scope.row.beforePurchasedHours || 0 }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">赠送:</span>
                <span>{{ scope.row.beforeGiftHours || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="调整后" width="150" align="center">
          <template #default="scope">
            <div class="before-after-info">
              <div class="info-item">
                <span class="info-label">总:</span>
                <span>{{ scope.row.afterTotalHours }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">剩余:</span>
                <span>{{ scope.row.afterRemainingHours }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">购买:</span>
                <span>{{ scope.row.afterPurchasedHours || 0 }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">赠送:</span>
                <span>{{ scope.row.afterGiftHours || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="调整原因" prop="adjustmentReason" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作人" prop="operatorName" width="100" />
        <el-table-column label="调整时间" prop="adjustmentTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.adjustmentTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="CourseAdjustmentHistoryQuery">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { parseTime } from '@/utils/ruoyi'
import { listAdjustmentHistory } from '@/api/management/studentCourseHours'
import { useRoute } from 'vue-router'

// 接收props参数（对话框模式）
const props = defineProps({
  courseHoursId: {
    type: String,
    default: null
  },
  studentId: {
    type: [String, Number],
    default: null
  },
  studentName: {
    type: String,
    default: ''
  },
  studentPhone: {
    type: String,
    default: ''
  },
  subject: {
    type: String,
    default: ''
  },
  specification: {
    type: String,
    default: ''
  },
  isDialogMode: {
    type: Boolean,
    default: false
  }
});

// 获取路由参数（独立页面模式）
const route = useRoute()
const routeParams = {
  courseHoursId: route.query.courseHoursId || null,
  studentId: route.query.studentId || null,
  studentName: route.query.studentName || '',
  studentPhone: route.query.studentPhone || '',
  subject: route.query.subject || '',
  specification: route.query.specification || ''
}

// 根据模式选择参数来源
const effectiveParams = computed(() => {
  if (props.isDialogMode) {
    return {
      courseHoursId: props.courseHoursId,
      studentId: props.studentId,
      studentName: props.studentName,
      studentPhone: props.studentPhone,
      subject: props.subject,
      specification: props.specification
    };
  }
  return routeParams;
})

// 响应式数据
const loading = ref(true)
const historyList = ref([])
const total = ref(0)
const dateRange = ref([])

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  queryParams.value.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  courseHoursId: null,
  studentId: null,
  studentName: '',
  studentPhone: '',
  subject: '',
  specification: '',
  operatorName: '',
  startTime: '',
  endTime: ''
})

// 查询表单引用
const queryRef = ref()

// 计算属性
const isStudentLocked = computed(() => !!effectiveParams.value.studentId)
const isCourseLocked = computed(() => !!(effectiveParams.value.subject && effectiveParams.value.specification))

const lockedInfo = computed(() => {
  if (isCourseLocked.value) {
    return `已锁定查询条件：学生 ${effectiveParams.value.studentName}，课程 ${effectiveParams.value.subject} - ${effectiveParams.value.specification}`
  } else if (isStudentLocked.value) {
    return `已锁定查询条件：学生 ${effectiveParams.value.studentName}`
  }
  return null
})

// 初始化查询参数
const initQueryParams = () => {
  queryParams.value.courseHoursId = effectiveParams.value.courseHoursId
  queryParams.value.studentId = effectiveParams.value.studentId
  queryParams.value.studentName = effectiveParams.value.studentName
  queryParams.value.studentPhone = effectiveParams.value.studentPhone
  queryParams.value.subject = effectiveParams.value.subject
  queryParams.value.specification = effectiveParams.value.specification
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  
  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.value.startTime = dateRange.value[0]
    queryParams.value.endTime = dateRange.value[1]
  } else {
    queryParams.value.startTime = ''
    queryParams.value.endTime = ''
  }
  
  try {
    const response = await listAdjustmentHistory(queryParams.value)
    historyList.value = response.rows
    total.value = response.total
  } catch (error) {
    console.error('获取调整历史失败:', error)
    ElMessage.error('获取调整历史失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryRef.value?.resetFields()
  dateRange.value = []

  // 重置时保持锁定的字段
  initQueryParams()

  handleQuery()
}

// 监听有效参数变化，重新初始化查询参数并刷新数据
watch(() => effectiveParams.value, (newParams, oldParams) => {
  // 检查关键参数是否发生变化
  const keyChanged =
    newParams.studentId !== oldParams?.studentId ||
    newParams.courseHoursId !== oldParams?.courseHoursId ||
    newParams.subject !== oldParams?.subject ||
    newParams.specification !== oldParams?.specification;

  if (keyChanged) {
    initQueryParams()
    getList()
  }
}, { deep: true, immediate: false })

// 初始化
onMounted(() => {
  initQueryParams()
  getList()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.adjustment-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.detail-label {
  color: #606266;
  margin-right: 4px;
  min-width: 30px;
}

.no-detail {
  text-align: center;
}

.before-after-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.info-label {
  color: #606266;
  margin-right: 4px;
  min-width: 30px;
}
</style>
