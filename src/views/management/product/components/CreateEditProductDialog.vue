<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    width="800px"
    append-to-body
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="产品名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入产品名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学科" prop="subject">
            <el-select v-model="form.subject" placeholder="请选择学科" style="width: 100%" 
              @change="handleSubjectChange">
              <el-option label="英语" value="英语" />
              <el-option label="数学" value="数学" />
              <el-option label="语文" value="语文" />
              <el-option label="物理" value="物理" />
              <el-option label="化学" value="化学" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
        <el-form-item label="课型" prop="courseType">
          <el-select
            v-model="form.courseType"
            placeholder="请选择课型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="option in availableSpecifications"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="上架">上架</el-radio>
              <el-radio label="下架">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="有效状态" prop="validStatus">
            <el-radio-group v-model="form.validStatus">
              <el-radio label="有效">有效</el-radio>
              <el-radio label="无效">无效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户类型" prop="customerType">
            <el-select v-model="form.customerType" placeholder="请选择客户类型" style="width: 100%">
              <el-option label="通用" value="通用" />
              <el-option label="仅老客户可用" value="仅老客户可用" />
              <el-option label="仅新客户可用" value="仅新客户可用" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="form.validStatus === '无效'">
        <el-col :span="24">
          <el-form-item label="无效原因" prop="invalidReason">
            <el-input v-model="form.invalidReason" placeholder="请输入无效原因" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="适用年级" prop="applicableGrades">
        <el-select v-model="form.applicableGrades" multiple placeholder="请选择适用年级" style="width: 100%">
          <el-option v-for="grade in GRADE_OPTIONS" 
                        :key="grade.label"
                        :label="grade.label"
                        :value="grade.label" />
        </el-select>
      </el-form-item>

      <el-form-item label="产品描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入产品描述" />
      </el-form-item>

      <!-- 价格信息 -->
      <el-divider content-position="left">价格信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单价(元)" prop="unitPrice">
            <el-input-number
              v-model="unitPriceInYuan"
              :precision="2"
              :min="0"
              placeholder="请输入单价"
              @change="calculateOriginalPrice"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="课时" prop="quantity">
            <el-input-number
              v-model="form.quantity"
              :min="1"
              placeholder="请输入课时"
              @change="calculateOriginalPrice"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="课时原价(元)">
            <el-input-number
                v-model="classPriceInYuan"
                :precision="2"
                :min="0" disabled
                placeholder="课时原价"
                style="width: 100%"
            />
            <div style="color: #999; font-size: 12px; margin-top: 5px;">
              自动计算：单价 × 课时
            </div>
          </el-form-item>
        </el-col>
<!--        <el-col :span="8">-->
<!--          <el-form-item label="教材费(元)" v-if="form.hasMaterialFee">-->
<!--            <el-input-number-->
<!--                v-model="materialFeeInYuan"-->
<!--                :precision="2"-->
<!--                :min="0" disabled-->
<!--                placeholder="教材费"-->
<!--                style="width: 100%"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <el-form-item label="合计原价(元)">-->
<!--            <el-input-number-->
<!--                v-model="originalPriceInYuanDisplay"-->
<!--                :precision="2"-->
<!--                :min="0" disabled-->
<!--                placeholder="合计原价"-->
<!--                style="width: 100%"-->
<!--            />-->
<!--            <div style="color: #999; font-size: 12px; margin-top: 5px;">-->
<!--              课时原价 + 教材费-->
<!--            </div>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
      </el-row>

      <!-- 附加选项 -->
      <el-divider content-position="left">附加选项</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="赠送课时">
            <el-checkbox v-model="form.hasBonusHours" @change="onBonusHoursChange">
              是否有赠送课时
            </el-checkbox>
          </el-form-item>
          <el-form-item  label="赠送课时" prop="bonusHoursQuantity">
            <el-input-number
              v-model="form.bonusHoursQuantity"
              :min="0"
              :disabled="!form.hasBonusHours"
              placeholder="请输入赠送课时"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="教材费">
            <el-checkbox v-model="form.hasMaterialFee" @change="onMaterialFeeChange">
              是否包含教材费
            </el-checkbox>
          </el-form-item>
          <el-form-item label="教材费(元)" prop="materialFee">
            <el-input-number
              v-model="materialFeeInYuan"
              :disabled="!form.hasMaterialFee"
              :precision="2"
              :min="0"
              placeholder="请输入教材费"
              @change="calculateOriginalPrice"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

<!--      <el-row :gutter="20">-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="排序权重" prop="sortOrder">-->
<!--            <el-input-number v-model="form.sortOrder" :min="0" placeholder="请输入排序权重" style="width: 100%" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->

      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <div class="price-summary">
          <div class="original-price-prominent">
            <span class="price-label-large">原价：</span>
            <span class="price-value-large">¥{{ originalPriceInYuan.toFixed(2) }}</span>
            <div style="color: #999; font-size: 12px; margin-top: 5px;">
              课时原价 + 教材费
            </div>
          </div>
          <div class="selling-price-section">
            <span class="price-label">销售价：</span>
            <el-input-number 
              v-model="sellingPriceInYuan" 
              :precision="2" 
              :min="0" 
              controls-position="right"
              style="width: 120px;"
            />
          </div>
        </div>
        <div class="button-group">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确 定
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="CreateEditProductDialog">
import {computed, reactive, ref, watch} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {createProductApi, updateProductApi,} from '@/api/management/product'
import {GRADE_OPTIONS} from '@/utils/gradeUtils'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

const form = reactive({
  id: undefined,
  name: '',
  description: '',
  subject: '',
  courseType: '',
  applicableGrades: [],
  unitPrice: 0,
  quantity: 1,
  hasBonusHours: false,
  bonusHoursQuantity: 0,
  hasMaterialFee: false,
  materialFee: 0,
  originalPrice: 0,
  sellingPrice: 0,
  status: '下架',
  validStatus: '有效',
  invalidReason: '',
  customerType: '通用',
  sortOrder: 0,
  remark: ''
})

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '产品名称不能为空', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '学科不能为空', trigger: 'change' }
  ],
  courseType: [
    { required: true, message: '课型不能为空', trigger: 'change' }
  ],
  applicableGrades: [
    { required: true, message: '适用年级不能为空', trigger: 'change' }
  ],
  unitPrice: [
    { required: true, message: '单价不能为空', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '数量不能为空', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '状态不能为空', trigger: 'change' }
  ],
  validStatus: [
    { required: true, message: '有效状态不能为空', trigger: 'change' }
  ],
  customerType: [
    { required: true, message: '客户类型不能为空', trigger: 'change' }
  ],
  invalidReason: [
    {
      validator: (rule, value, callback) => {
        if (form.validStatus === '无效' && !value) {
          callback(new Error('无效原因不能为空'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const title = computed(() => {
  return form.id ? '修改产品' : '添加产品'
})

const unitPriceInYuan = computed({
  get: () => form.unitPrice ? form.unitPrice / 100 : 0,
  set: (value) => {
    form.unitPrice = Math.round(value * 100)
    calculateOriginalPrice()
  }
})

const materialFeeInYuan = computed({
  get: () => form.materialFee ? form.materialFee / 100 : 0,
  set: (value) => {
    form.materialFee = Math.round(value * 100)
    calculateOriginalPrice()
  }
})

const classPriceInYuan = computed(() => {
  if (form.unitPrice && form.quantity) {
    return (form.unitPrice * form.quantity / 100)
  }
  return 0
})

const originalPriceInYuanDisplay = computed(() => {
  if (form.unitPrice && form.quantity) {
    const basePrice = form.unitPrice * form.quantity / 100 // 课时原价
    const materialFee = form.hasMaterialFee ? (form.materialFee / 100) : 0 // 教材费
    return basePrice + materialFee
  }
  return 0
})

// 原价计算（课时原价 + 教材费）
const originalPriceInYuan = computed(() => {
  if (form.unitPrice && form.quantity) {
    const basePrice = form.unitPrice * form.quantity / 100 // 课时原价
    const materialFee = form.hasMaterialFee ? (form.materialFee / 100) : 0 // 教材费
    return basePrice + materialFee
  }
  return 0
})

// 添加一个标记来跟踪销售价是否被用户手动修改过
const isSellingPriceManuallySet = ref(false)
// 添加一个标记来跟踪原价是否发生了变化
const hasOriginalPriceChanged = ref(false)
// 保存初始的原价，用于比较是否发生变化
const initialOriginalPrice = ref(0)
// 保存初始的销售价
const initialSellingPrice = ref(0)

// 销售价（编辑时初始显示原本销售价，原价变化后跟随原价）
const sellingPriceInYuan = computed({
  get: () => {
    // 如果用户手动设置过销售价，优先使用设置的值
    //if (isSellingPriceManuallySet.value) {
    //  return form.sellingPrice / 100
    //}
    // 如果是编辑模式且原价没有变化，显示原本的销售价
    if (form.id && !hasOriginalPriceChanged.value && initialSellingPrice.value > 0) {
      return initialSellingPrice.value / 100
    }
    // 如果原价发生了变化，或者是新增模式，跟随原价
    return originalPriceInYuan.value
  },
  set: (value) => {
    form.sellingPrice = Math.round(value * 100) // 确保是整数
    isSellingPriceManuallySet.value = true // 标记为用户手动设置
  }
})

// 方法
const calculateOriginalPrice = () => {
  if (form.unitPrice && form.quantity) {
    const basePrice = form.unitPrice * form.quantity
    const materialFee = form.hasMaterialFee ? form.materialFee : 0
    const newOriginalPrice = basePrice + materialFee

    // 检查原价是否发生变化
    if (form.id && initialOriginalPrice.value > 0 && newOriginalPrice !== initialOriginalPrice.value) {
      hasOriginalPriceChanged.value = true
      // 如果用户没有手动设置销售价，销售价跟随原价变化
      if (!isSellingPriceManuallySet.value) {
        form.sellingPrice = newOriginalPrice
      }
    }

    form.originalPrice = newOriginalPrice
    
    // 新增模式下，销售价跟随原价
    if (!form.id) {
      form.sellingPrice = newOriginalPrice
    }
  } else {
    form.originalPrice = 0
    // 只有在新增模式下才重置销售价
    if (!form.id) {
      form.sellingPrice = 0
    }
  }
}

// 学科变化处理
const handleSubjectChange = (subject: string) => {
  form.courseType = ''
  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

const onBonusHoursChange = (value) => {
  if (!value) {
    form.bonusHoursQuantity = 0
  }
}

const onMaterialFeeChange = (value) => {
  if (!value) {
    form.materialFee = 0
  }
  calculateOriginalPrice()
}

const resetForm = () => {
  Object.assign(form, {
    id: undefined,
    name: '',
    description: '',
    subject: '',
    courseType: '',
    applicableGrades: [],
    unitPrice: 0,
    quantity: 1,
    hasBonusHours: false,
    bonusHoursQuantity: 0,
    hasMaterialFee: false,
    materialFee: 0,
    originalPrice: 0,
    sellingPrice: 0,
    status: '下架',
    sortOrder: 0,
    remark: ''
  })
  // 重置所有标记
  isSellingPriceManuallySet.value = false
  hasOriginalPriceChanged.value = false
  initialOriginalPrice.value = 0
  initialSellingPrice.value = 0
  formRef.value?.clearValidate()
}

// 监听产品数据变化
watch(() => props.product, (newProduct) => {
  if (newProduct) {
    Object.assign(form, newProduct)
    // 重置所有标记
    isSellingPriceManuallySet.value = false
    hasOriginalPriceChanged.value = false
    // 保存初始原价和销售价
    initialOriginalPrice.value = newProduct.originalPrice || 0
    initialSellingPrice.value = newProduct.sellingPrice || 0
    // 确保销售价正确初始化
    if (newProduct.sellingPrice !== undefined) {
      form.sellingPrice = newProduct.sellingPrice
    }
  } else {
    resetForm()
  }
}, { immediate: true })

const handleCancel = () => {
  dialogVisible.value = false
}

const handleClose = () => {
  resetForm()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    // 调试信息
    console.log('提交前的数据:', {
      displaySellingPrice: sellingPriceInYuan.value,
      formSellingPrice: form.sellingPrice,
      formSellingPriceInYuan: form.sellingPrice / 100,
      originalPrice: originalPriceInYuan.value,
      isManuallySet: isSellingPriceManuallySet.value,
      hasOriginalPriceChanged: hasOriginalPriceChanged.value
    })

    // 确认对话框
    await ElMessageBox.confirm(
      `提交后售价不可更改，确认是否提交？\n当前销售价：¥${(form.sellingPrice / 100).toFixed(2)}`,
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    if (form.id) {
      await updateProductApi(form)
      ElMessage.success('修改成功')
    } else {
      await createProductApi(form)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    emit('success')
  } catch (error) {
    if (error !== 'cancel' && error !== false) { // 不是取消操作或表单验证错误
      ElMessage.error(form.id ? '修改失败' : '新增失败')
      console.error('提交失败:', error)
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.price-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eee;
}

.original-price-prominent {
  display: flex;
  align-items: center;
  gap: 12px;
}

.price-label-large {
  font-weight: bold;
  font-size: 16px;
  color: #333;
}

.price-value-large {
  font-weight: bold;
  font-size: 24px;
  color: #ff4d4f; // 更醒目的红色
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.selling-price-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-label {
  font-weight: bold;
  font-size: 14px;
  color: #606266;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}
</style>
