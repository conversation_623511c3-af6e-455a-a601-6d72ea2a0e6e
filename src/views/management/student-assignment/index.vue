<template>
  <div class="student-assignment-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>学生分配管理</h2>
        <p>管理学生与销售人员的分配关系</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleAutoAssign" v-hasPermi="['student:assignment:auto']">
          <el-icon><Setting /></el-icon>
          智能分配
        </el-button>
        <el-button @click="handleViewHistory" v-hasPermi="['student:assignment:history']">
          <el-icon><Clock /></el-icon>
          分配历史
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalStudents || 0 }}</div>
            <div class="stat-label">学生总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card unassigned">
          <div class="stat-item">
            <div class="stat-value">{{ stats.unassignedStudents || 0 }}</div>
            <div class="stat-label">未分配学生</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card assigned">
          <div class="stat-item">
            <div class="stat-value">{{ stats.assignedStudents || 0 }}</div>
            <div class="stat-label">已分配学生</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card balanced">
          <div class="stat-item">
            <div class="stat-value">{{ stats.balanceScore || 0 }}%</div>
            <div class="stat-label">负载均衡度</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card shadow="never" class="search-card">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="学生姓名">
          <el-input
            v-model="searchForm.studentName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="销售组">
          <el-select
            v-model="searchForm.salesGroupId"
            placeholder="请选择销售组"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="group in salesGroupOptions"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分配状态">
          <el-select
            v-model="searchForm.assignmentStatus"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="未分配" value="unassigned" />
            <el-option label="已分配" value="assigned" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 可分配学生列表 -->
    <el-card shadow="never" class="table-card">
      <template #header>
        <div class="card-header">
          <span>可分配学生列表</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              :disabled="selectedStudents.length === 0"
              @click="handleBatchAssign"
            >
              批量分配 ({{ selectedStudents.length }})
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="studentList"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="学生姓名" width="120" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="grade" label="年级" width="80" />
        <el-table-column prop="school" label="学校" min-width="150" show-overflow-tooltip />
        <el-table-column prop="parentName" label="家长姓名" width="120" />
        <el-table-column prop="parentPhone" label="家长手机" width="130" />
        <el-table-column label="当前销售" width="120">
          <template #default="{ row }">
            <span v-if="row.salesName">{{ row.salesName }}</span>
            <el-tag v-else type="warning" size="small">未分配</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="销售组" width="120">
          <template #default="{ row }">
            <span v-if="row.salesGroupName">{{ row.salesGroupName }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleAssignSingle(row)"
              v-hasPermi="['student:assignment:assign']"
            >
              {{ row.salesId ? '重新分配' : '分配' }}
            </el-button>
            <el-button
              v-if="row.salesId"
              type="warning"
              link
              @click="handleUnassign(row)"
              v-hasPermi="['student:assignment:unassign']"
            >
              取消分配
            </el-button>
            <el-button
              type="info"
              link
              @click="handleViewHistory(row)"
              v-hasPermi="['student:assignment:history']"
            >
              历史记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="searchForm.pageNum"
          v-model:page-size="searchForm.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </el-card>

    <!-- 分配对话框 -->
    <AssignmentDialog
      v-model="showAssignDialog"
      :students="assignmentStudents"
      @success="handleAssignSuccess"
    />

    <!-- 分配历史对话框 -->
    <AssignmentHistoryDialog
      v-model="showHistoryDialog"
      :student="selectedStudent"
    />

    <!-- 智能分配对话框 -->
    <AutoAssignDialog
      v-model="showAutoAssignDialog"
      @success="handleAutoAssignSuccess"
    />
  </div>
</template>

<script setup name="student-assignment">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Setting, Clock
} from '@element-plus/icons-vue'
import { 
  getAvailableStudentsApi,
  getAssignmentStatsApi,
  unassignStudentApi,
  getSalesGroupOptionsApi
} from '@/api/management/studentAssignment'
import { parseTime } from '@/utils/ruoyi'
import AssignmentDialog from './components/AssignmentDialog.vue'
import AssignmentHistoryDialog from './components/AssignmentHistoryDialog.vue'
import AutoAssignDialog from './components/AutoAssignDialog.vue'

// 响应式数据
const loading = ref(false)
const studentList = ref([])
const total = ref(0)
const selectedStudents = ref([])
const stats = ref({})
const salesGroupOptions = ref([])

// 对话框状态
const showAssignDialog = ref(false)
const showHistoryDialog = ref(false)
const showAutoAssignDialog = ref(false)
const assignmentStudents = ref([])
const selectedStudent = ref(null)

// 搜索表单
const searchForm = reactive({
  studentName: '',
  salesGroupId: '',
  assignmentStatus: '',
  pageNum: 1,
  pageSize: 20
})

// 页面初始化
onMounted(() => {
  handleSearch()
  loadStats()
  loadSalesGroupOptions()
})

// 搜索
const handleSearch = async () => {
  loading.value = true
  try {
    const response = await getAvailableStudentsApi(searchForm)
    studentList.value = response.data?.records || []
    total.value = response.data?.total || 0
  } catch (error) {
    ElMessage.error('获取学生列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    studentName: '',
    salesGroupId: '',
    assignmentStatus: '',
    pageNum: 1,
    pageSize: 20
  })
  handleSearch()
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await getAssignmentStatsApi()
    stats.value = response.data || {}
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 加载销售组选项
const loadSalesGroupOptions = async () => {
  try {
    const response = await getSalesGroupOptionsApi()
    salesGroupOptions.value = response.data || []
  } catch (error) {
    console.error('获取销售组选项失败:', error)
  }
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedStudents.value = selection
}

// 单个分配
const handleAssignSingle = (row) => {
  assignmentStudents.value = [row]
  showAssignDialog.value = true
}

// 批量分配
const handleBatchAssign = () => {
  if (selectedStudents.value.length === 0) {
    ElMessage.warning('请选择要分配的学生')
    return
  }
  assignmentStudents.value = [...selectedStudents.value]
  showAssignDialog.value = true
}

// 取消分配
const handleUnassign = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消学生"${row.name}"的分配吗？`,
      '取消分配确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await unassignStudentApi(row.id)
    ElMessage.success('取消分配成功')
    handleSearch()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消分配失败')
    }
  }
}

// 查看历史记录
const handleViewHistory = (row) => {
  selectedStudent.value = row
  showHistoryDialog.value = true
}

// 智能分配
const handleAutoAssign = () => {
  showAutoAssignDialog.value = true
}

// 分配成功回调
const handleAssignSuccess = () => {
  showAssignDialog.value = false
  selectedStudents.value = []
  handleSearch()
  loadStats()
}

// 智能分配成功回调
const handleAutoAssignSuccess = () => {
  showAutoAssignDialog.value = false
  handleSearch()
  loadStats()
}
</script>

<style scoped>
.student-assignment-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-title h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-card.unassigned {
  border-left: 4px solid #E6A23C;
}

.stat-card.assigned {
  border-left: 4px solid #67C23A;
}

.stat-card.balanced {
  border-left: 4px solid #409EFF;
}

.stat-item {
  padding: 20px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #909399;
}
</style>
