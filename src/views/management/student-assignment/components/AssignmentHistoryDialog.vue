<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`分配历史 - ${student?.name || ''}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="history-container">
      <el-table
        :data="historyList"
        stripe
        border
        max-height="400"
      >
        <el-table-column prop="fromSalesName" label="原销售" width="120">
          <template #default="{ row }">
            <span v-if="row.fromSalesName">{{ row.fromSalesName }}</span>
            <el-tag v-else type="info" size="small">新分配</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="toSalesName" label="新销售" width="120" />
        <el-table-column prop="operatorName" label="操作人" width="120" />
        <el-table-column label="分配类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getAssignmentTypeTag(row.assignmentType)" size="small">
              {{ getAssignmentTypeText(row.assignmentType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignmentTime" label="分配时间" width="180" />
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="searchForm.pageNum"
          v-model:page-size="searchForm.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="loadHistory"
          @current-change="loadHistory"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getAssignmentHistoryApi } from '@/api/management/studentAssignment'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  student: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const historyList = ref([])
const total = ref(0)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  if (newValue && props.student?.id) {
    loadHistory()
  }
})

// 加载分配历史
const loadHistory = async () => {
  if (!props.student?.id) return

  loading.value = true
  try {
    const response = await getAssignmentHistoryApi({
      studentId: props.student.id,
      ...searchForm
    })
    historyList.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    ElMessage.error('获取分配历史失败')
  } finally {
    loading.value = false
  }
}

// 获取分配类型标签
const getAssignmentTypeTag = (type) => {
  const typeMap = {
    'manual': 'primary',
    'auto': 'success',
    'reassign': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取分配类型文本
const getAssignmentTypeText = (type) => {
  const typeMap = {
    'manual': '手动分配',
    'auto': '自动分配',
    'reassign': '重新分配'
  }
  return typeMap[type] || '未知'
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  historyList.value = []
  total.value = 0
  searchForm.pageNum = 1
}
</script>

<style scoped>
.history-container {
  padding: 10px 0;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
