<template>
  <el-dialog
    v-model="visible"
    :title="reviewType === 'approve' ? '审核通过' : '审核拒绝'"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="review-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 申请信息标签页 -->
        <el-tab-pane label="申请信息" name="application">
          <div class="application-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="学生姓名">
                {{ application.studentName }}
              </el-descriptions-item>
              <el-descriptions-item label="学科">
                {{ application.subject }}
              </el-descriptions-item>
              <el-descriptions-item label="课型">
                {{ application.specification }}
              </el-descriptions-item>
              <el-descriptions-item label="销售组">
                {{ application.salesGroupName || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="销售人员">
                {{ application.salesName || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="申请时间">
                {{ formatDateTime(application.createTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="试听课时间" :span="2">
                <div v-if="application.trialClassTime" class="trial-time-info">
                  <el-tag type="success" size="small">
                    {{ formatTrialDate(application.trialClassTime.date) }}
                    {{ application.trialClassTime.startTime }} - {{ application.trialClassTime.endTime }}
                  </el-tag>
                </div>
                <span v-else class="text-muted">未设置</span>
              </el-descriptions-item>
              <el-descriptions-item label="正课时间" :span="2">
                <div v-if="application.preferredTimeSlots && application.preferredTimeSlots.length > 0" class="preferred-time-slots">
                  <el-tag
                    v-for="(slot, index) in application.preferredTimeSlots"
                    :key="index"
                    type="info"
                    size="small"
                    class="time-slot-tag"
                  >
                    {{ getWeekdayText(slot.weekday) }} {{ slot.startTime }}-{{ slot.endTime }}
                    <span v-if="slot.priority" class="priority-text">（优先级{{ slot.priority }}）</span>
              </el-tag>
            </div>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
          <el-descriptions-item label="申请说明" :span="2">
            <div class="application-reason">
              <el-input
                v-model="application.applicationReason"
                type="textarea"
                :rows="3"
                readonly
                resize="none"
                placeholder="无申请说明"
                class="readonly-textarea"
              />
            </div>
          </el-descriptions-item>
        </el-descriptions>

          </div>

          <!-- 审核表单 -->
          <div class="review-form">
            <h4>{{ reviewType === "approve" ? "审核通过" : "审核拒绝" }}</h4>
            <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
              <!-- 通过时的分配信息 -->
              <template v-if="reviewType === 'approve'">
                <el-form-item label="分配教师" prop="assignedTeacherId">
                  <el-select
                    v-model="form.assignedTeacherId"
                    placeholder="请选择分配的教师"
                    style="width: 100%"
                    @change="handleTeacherChange"
                  >
                    <el-option
                      v-for="teacher in availableTeachers"
                      :key="teacher.teacherId"
                      :label="teacher.teacherName"
                      :value="teacher.teacherId"
                    >
                      <div class="teacher-option">
                        <span>{{ teacher.teacherName }}</span>
                        <span class="teacher-phone">{{ teacher.teacherPhone }}</span>
                        <el-tag
                          :type="teacher.isAvailable ? 'success' : 'danger'"
                          size="small"
                        >
                          {{ teacher.isAvailable ? "可用" : "不可用" }}
                        </el-tag>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="试听课时间" prop="assignedTrialTimeSlot">
                  <el-select
                    v-model="form.assignedTrialTimeSlot"
                    placeholder="请选择试听课时间段（必选）"
                    style="width: 100%"
                    clearable
                    :disabled="!form.assignedTeacherId"
                  >
                    <el-option
                      v-for="timeSlot in trialTimeSlots"
                      :key="`${timeSlot.date}-${timeSlot.startTime}`"
                      :label="getTrialTimeSlotText(timeSlot)"
                      :value="timeSlot"
                      :disabled="timeSlot.disabled"
                      :class="{ 'disabled-option': timeSlot.disabled }"
                    />
                  </el-select>
                  <div class="form-tip">
                    <span style="color: #f56c6c;">*</span> 必须选择试听课时间段，选择后将自动排课，灰色选项表示老师在该时间段不可用
                  </div>
                </el-form-item>
              </template>

              <!-- 拒绝时的原因 -->
              <template v-if="reviewType === 'reject'">
                <el-form-item label="拒绝原因" prop="rejectionReason">
                  <el-select
                    v-model="form.rejectionReason"
                    placeholder="请选择拒绝原因"
                    style="width: 100%"
                    allow-create
                    filterable
                  >
                    <el-option label="教师时间冲突" value="教师时间冲突" />
                    <el-option label="课程安排已满" value="课程安排已满" />
                    <el-option label="学生信息不完整" value="学生信息不完整" />
                    <el-option label="课时余额不足" value="课时余额不足" />
                    <el-option label="不符合课程要求" value="不符合课程要求" />
                    <el-option label="其他原因" value="其他原因" />
                  </el-select>
                </el-form-item>
              </template>

              <el-form-item label="审核意见" prop="reviewComment">
                <el-input
                  v-model="form.reviewComment"
                  type="textarea"
                  :rows="4"
                  :placeholder="
                    reviewType === 'approve' ? '请输入审核通过的意见' : '请详细说明拒绝原因'
                  "
                />
              </el-form-item>

              <el-form-item label="建议方案" v-if="reviewType === 'reject'">
                <el-input
                  v-model="form.suggestion"
                  type="textarea"
                  :rows="3"
                  placeholder="请提供替代方案或建议（可选）"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 学生信息标签页 -->
        <el-tab-pane label="学生信息" name="student">
          <div v-loading="studentLoading" class="student-info">
            <el-descriptions :column="2" border v-if="studentDetail">
              <el-descriptions-item label="学生姓名">
                <span>{{ studentDetail.name }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="手机号码">
                <span>{{ studentDetail.phone }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="性别">
                <span>{{ getGenderText(studentDetail.gender) }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="年级">
                <span>{{ getGradeText(studentDetail.grade) }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="学校">
                <span>{{ studentDetail.school || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="班级">
                <span>{{ studentDetail.className || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="家长姓名">
                <span>{{ studentDetail.parentName || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="家长手机">
                <span>{{ studentDetail.parentPhone || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="学习目标" :span="2">
                <div class="learning-goals">
                  <el-input
                    v-model="studentDetail.learningGoals"
                    type="textarea"
                    :rows="2"
                    readonly
                    resize="none"
                    placeholder="暂无学习目标"
                    class="readonly-textarea"
                  />
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">
                <div class="student-remarks">
                  <el-input
                    v-model="studentDetail.remarks"
                    type="textarea"
                    :rows="2"
                    readonly
                    resize="none"
                    placeholder="暂无备注"
                    class="readonly-textarea"
                  />
                </div>
              </el-descriptions-item>
            </el-descriptions>
            <div v-else class="no-student-data">
              <el-empty description="暂无学生信息" />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          :type="reviewType === 'approve' ? 'primary' : 'danger'"
          @click="handleConfirm"
          :loading="submitting"
        >
          {{ reviewType === "approve" ? "确认通过" : "确认拒绝" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import {
  reviewApplicationApi,
  getAppliedTeachersApi,
  getTeacherAvailableSlotsApi,
} from "@/api/teaching-group-leader/courseReview";
import { getStudentDetailApi } from '@/api/management/student'
import { getGradeText } from '@/utils/gradeUtils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  application: {
    type: Object,
    default: () => ({}),
  },
  reviewType: {
    type: String,
    default: "approve", // approve, reject
  },
});

const emit = defineEmits(["update:modelValue", "success"]);

const visible = ref(false);
const loading = ref(false);
const submitting = ref(false);
const studentLoading = ref(false);
const formRef = ref();
const availableTeachers = ref([]);
const trialTimeSlots = ref([]);
const studentDetail = ref(null);
const activeTab = ref('application');

const form = reactive({
  assignedTeacherId: "",
  assignedTrialTimeSlot: null,
  rejectionReason: "",
  reviewComment: "",
  suggestion: "",
});

const rules = computed(() => {
  const baseRules = {
    reviewComment: [{ required: true, message: "请输入审核意见", trigger: "blur" }],
  };

  if (props.reviewType === "approve") {
    baseRules.assignedTeacherId = [
      { required: true, message: "请选择分配的教师", trigger: "change" },
    ];
    // 试听课时间改为必填
    baseRules.assignedTrialTimeSlot = [
      { required: true, message: "请选择试听课时间", trigger: "change" },
    ];
  } else if (props.reviewType === "reject") {
    baseRules.rejectionReason = [
      { required: true, message: "请选择拒绝原因", trigger: "change" },
    ];
  }

  return baseRules;
});

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val) {
      resetForm();
      if (props.reviewType === "approve") {
        loadAvailableTeachers();
        loadTrialTimeSlots();
      }
      // 获取学生信息
      if (props.application && props.application.studentId) {
        getStudentDetail(props.application.studentId);
      }
    }
  }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

// 重置表单
const resetForm = () => {
  form.assignedTeacherId = "";
  form.assignedTrialTimeSlot = null;
  form.rejectionReason = "";
  form.reviewComment = "";
  form.suggestion = "";

  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 获取学生详情
const getStudentDetail = async (studentId) => {
  if (!studentId) return

  studentLoading.value = true
  try {
    const response = await getStudentDetailApi(studentId)
    studentDetail.value = response.data || null
  } catch (error) {
    console.error('获取学生详情失败:', error)
    studentDetail.value = null
  } finally {
    studentLoading.value = false
  }
};

// 加载可用教师（获取当前申请的候选老师）
const loadAvailableTeachers = async () => {
  try {
    if (!props.application || !props.application.id) {
      availableTeachers.value = [];
      return;
    }

    const response = await getAppliedTeachersApi(props.application.id);
    availableTeachers.value = response.data || [];
  } catch (error) {
    console.error("加载可用教师失败:", error);
    availableTeachers.value = [];
  }
};

// 加载试听课时间段
const loadTrialTimeSlots = () => {
  if (props.application && props.application.trialClassTime) {
    const trialTime = props.application.trialClassTime;

    // 生成试听课时间段选项（每15分钟一个时间段）
    const timeSlots = generateTrialTimeSlots(trialTime);
    trialTimeSlots.value = timeSlots;
  } else {
    trialTimeSlots.value = [];
  }
};

// 处理教师选择变化
const handleTeacherChange = async (teacherId) => {
  if (!teacherId) {
    form.assignedTrialTimeSlot = null;
    trialTimeSlots.value = [];
  } else {
    // 重新加载试听课时间段，并检查老师可用性
    await loadTrialTimeSlotsWithAvailability(teacherId);
  }
};

// 生成试听课时间段选项
const generateTrialTimeSlots = (trialTime) => {
  const timeSlots = [];
  const startTime = new Date(`2000-01-01 ${trialTime.startTime}`);
  const endTime = new Date(`2000-01-01 ${trialTime.endTime}`);

  // 每15分钟生成一个时间段，每个时间段持续1小时
  const current = new Date(startTime);
  while (current.getTime() + 60 * 60 * 1000 <= endTime.getTime()) {
    const slotStart = current.toTimeString().slice(0, 5);
    const slotEnd = new Date(current.getTime() + 60 * 60 * 1000).toTimeString().slice(0, 5);

    timeSlots.push({
      date: trialTime.date,
      startTime: slotStart,
      endTime: slotEnd,
      disabled: false // 初始状态不禁用，后续根据老师可用性设置
    });

    // 增加15分钟
    current.setTime(current.getTime() + 15 * 60 * 1000);
  }

  return timeSlots;
};

// 加载试听课时间段并检查老师可用性
const loadTrialTimeSlotsWithAvailability = async (teacherId) => {
  if (!props.application || !props.application.trialClassTime) {
    trialTimeSlots.value = [];
    return;
  }

  const trialTime = props.application.trialClassTime;
  const timeSlots = generateTrialTimeSlots(trialTime);

  if (teacherId && props.application?.id) {
    try {
      // 调用API获取该申请和老师的可选时间段
      const response = await getTeacherAvailableSlotsApi(teacherId, props.application.id);

      const availableTimeSlots = response.data || [];

      console.log('后端返回的可选时间段:', availableTimeSlots);
      console.log('前端生成的时间段:', timeSlots);

      // 直接使用后端返回的时间段数据，因为后端已经计算好了可用性
      trialTimeSlots.value = availableTimeSlots.map(slot => ({
        ...slot,
        disabled: !slot.available // 根据后端返回的available字段设置disabled
      }));

      return; // 直接返回，不需要继续处理前端生成的timeSlots
    } catch (error) {
      console.error('检查老师可用性失败:', error);
      // 如果检查失败，使用前端生成的时间段，但都设为可选
      timeSlots.forEach(slot => {
        slot.disabled = false;
      });
    }
  }

  trialTimeSlots.value = timeSlots;
};

// 获取试听课时间段文本
const getTrialTimeSlotText = (timeSlot) => {
  const date = new Date(timeSlot.date);
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[date.getDay()];
  const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`;

  return `${dateStr} ${weekday} ${timeSlot.startTime}-${timeSlot.endTime}`;
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化试听课日期
const formatTrialDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[d.getDay()];
  const dateStr = `${d.getMonth() + 1}月${d.getDate()}日`;
  return `${dateStr} ${weekday}`;
};

// 获取星期几文本
const getWeekdayText = (weekday) => {
  const weekdayMap = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日'
  };
  return weekdayMap[weekday] || `周${weekday}`;
};

// 确认审核
const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    submitting.value = true;

    const data = {
      applicationId: props.application.id,
      reviewResult: props.reviewType === "approve" ? "已通过" : "已拒绝",
      reviewComment: form.reviewComment,
    };

    if (props.reviewType === "approve") {
      data.assignedTeacherId = form.assignedTeacherId;
      data.assignedTrialTimeSlot = form.assignedTrialTimeSlot;
      // 根据是否选择试听课时间段决定是否自动排课
      data.autoSchedule = !!form.assignedTrialTimeSlot;
      if(data.autoSchedule==''){
        data.autoSchedule = null;
      }
    } else {
      data.rejectionReason = form.rejectionReason;
      data.suggestion = form.suggestion;
    }

    await reviewApplicationApi(data);

    ElMessage.success("审核成功");
    emit("success");
    handleClose();
  } catch (error) {
    if (error !== false) {
      ElMessage.error("审核失败: " + (error.message || "未知错误"));
    }
  } finally {
    submitting.value = false;
  }
};

// 获取性别文本
const getGenderText = (gender) => {
  const genderMap = {
    '0': '男',
    '1': '女',
    '2': '未知'
  }
  return genderMap[gender] || '未知'
}

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  // 重置数据
  studentDetail.value = null;
  activeTab.value = 'application';
};
</script>

<style scoped>
.review-content {
  max-height: 600px;
  overflow-y: auto;
}

.application-info {
  margin-bottom: 20px;
}

.application-info h4,
.review-form h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.teacher-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.teacher-phone {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

/* 禁用的时间段选项样式 */
:deep(.disabled-option) {
  color: #C0C4CC !important;
  background-color: #F5F7FA !important;
  cursor: not-allowed !important;
}

:deep(.disabled-option:hover) {
  background-color: #F5F7FA !important;
}

/* 申请信息样式 */
.trial-time-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preferred-time-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.time-slot-tag {
  margin: 0;
}

.priority-text {
  font-size: 11px;
  opacity: 0.8;
}

.text-muted {
  color: #909399;
  font-style: italic;
}

.application-reason {
  width: 100%;
}

.readonly-textarea :deep(.el-textarea__inner) {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  color: #606266;
  cursor: default;
}

.readonly-textarea :deep(.el-textarea__inner):focus {
  border-color: #e4e7ed;
  box-shadow: none;
}
</style>
