<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`学生详情 - ${student?.name || ''}`"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="student" class="student-detail-container">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" size="small" @click="handleEdit">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>学生姓名：</label>
              <span>{{ student.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>手机号码：</label>
              <span>{{ student.phone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>性别：</label>
              <span>{{ getGenderText(student.gender) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>年级：</label>
              <span>{{ getGradeText(student.grade) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>学校：</label>
              <span>{{ student.school || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>班级：</label>
              <span>{{ student.className || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>家长姓名：</label>
              <span>{{ student.parentName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>家长电话：</label>
              <span>{{ student.parentPhone || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>状态：</label>
              <el-tag :type="getStatusTagType(student.status)" size="small">
                {{ getStatusText(student.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 学习信息 -->
      <el-card class="info-card">
        <template #header>
          <span>学习信息</span>
        </template>
        
        <el-row :gutter="20">
          <!-- <el-col :span="8">
            <div class="info-item">
              <label>任课教师：</label>
              <span>{{ student.teacherName || '-' }}</span>
            </div>
          </el-col> -->
          <el-col :span="8">
            <div class="info-item">
              <label>总课时：</label>
              <span>{{ student.totalHours || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>已消课时：</label>
              <span>{{ student.consumedHours || 0 }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>剩余课时：</label>
              <el-tag 
                :type="getRemainingHoursTagType(student.remainingHours)" 
                size="small"
              >
                {{ student.remainingHours || 0 }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(student.createTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>最后更新：</label>
              <span>{{ formatDateTime(student.updateTime) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row v-if="student.learningGoals" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>学习目标：</label>
              <div class="text-content">{{ student.learningGoals }}</div>
            </div>
          </el-col>
        </el-row>
        
        <el-row v-if="student.remarks" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>备注：</label>
              <div class="text-content">{{ student.remarks }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 课程统计 -->
      <el-card class="info-card">
        <template #header>
          <span>课程统计</span>
        </template>
        
        <div class="stats-grid">
          <div class="stats-item">
            <div class="stats-number">{{ courseStats.totalCourses }}</div>
            <div class="stats-label">总课程数</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">{{ courseStats.completedCourses }}</div>
            <div class="stats-label">已完成</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">{{ courseStats.scheduledCourses }}</div>
            <div class="stats-label">已排课</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">{{ courseStats.cancelledCourses }}</div>
            <div class="stats-label">已取消</div>
          </div>
        </div>
      </el-card>

      <!-- 最近课程 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>最近课程</span>
            <el-button type="primary" size="small" @click="handleViewAllCourses">
              查看全部
            </el-button>
          </div>
        </template>
        
        <el-table :data="recentCourses" stripe>
          <el-table-column prop="date" label="日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.date) }}
            </template>
          </el-table-column>
          <el-table-column prop="time" label="时间" width="150">
            <template #default="{ row }">
              {{ row.startTime }} - {{ row.endTime }}
            </template>
          </el-table-column>
          <el-table-column prop="subject" label="科目" width="100" />
          <el-table-column prop="teacherName" label="教师" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getCourseStatusTagType(row.status)" size="small">
                {{ getCourseStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remarks" label="备注" show-overflow-tooltip />
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Edit } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import { getGradeText } from '@/utils/gradeUtils'
import { useStudentStore } from '@/stores/student'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  student: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit'])

// Store
const studentStore = useStudentStore()

// 响应式数据
const courseStats = ref({
  totalCourses: 0,
  completedCourses: 0,
  scheduledCourses: 0,
  cancelledCourses: 0
})

const recentCourses = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleEdit = () => {
  emit('edit', props.student)
  handleClose()
}

const handleViewAllCourses = () => {
  // 这里可以跳转到课程管理页面或打开课程列表弹窗
  console.log('查看全部课程')
}

const getGenderText = (gender) => {
  const genderMap = {
    '0': '男',
    '1': '女',
    '2': '未知'
  }
  return genderMap[gender] || '未知'
}

// getGradeText 函数已移至 @/utils/gradeUtils

const getStatusTagType = (status) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'warning',
    'graduated': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '在读',
    'inactive': '暂停',
    'graduated': '毕业'
  }
  return statusMap[status] || status
}

const getRemainingHoursTagType = (hours) => {
  if (hours <= 5) return 'danger'
  if (hours <= 10) return 'warning'
  return 'success'
}

const getCourseStatusTagType = (status) => {
  const statusMap = {
    'completed': 'success',
    'scheduled': 'primary',
    'cancelled': 'danger',
    'in_progress': 'warning'
  }
  return statusMap[status] || 'info'
}

const getCourseStatusText = (status) => {
  const statusMap = {
    'completed': '已完成',
    'scheduled': '已排课',
    'cancelled': '已取消',
    'in_progress': '进行中'
  }
  return statusMap[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const fetchCourseData = async () => {
  if (!props.student?.id) return

  try {
    // 获取课程统计数据
    const stats = await studentStore.fetchStudentCourseStats(props.student.id)
    if (stats) {
      courseStats.value = {
        totalCourses: stats.totalCourses || 0,
        completedCourses: stats.completedCourses || 0,
        scheduledCourses: stats.scheduledCourses || 0,
        cancelledCourses: stats.cancelledCourses || 0
      }
    }

    // 获取最近课程数据
    const courses = await studentStore.fetchStudentRecentCourses(props.student.id, 5)
    recentCourses.value = courses || []
  } catch (error) {
    console.error('获取课程数据失败:', error)
  }
}



// 监听器
watch(() => props.student, (newStudent) => {
  if (newStudent && props.modelValue) {
    fetchCourseData()
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && props.student) {
    fetchCourseData()
  }
})
</script>

<style lang="scss" scoped>
.student-detail-container {
  .info-card {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .info-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-weight: 600;
        color: #606266;
        margin-right: 8px;
      }

      .text-content {
        margin-top: 4px;
        padding: 8px 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #303133;
        line-height: 1.5;
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      .stats-item {
        text-align: center;
        padding: 16px;
        background-color: #f5f7fa;
        border-radius: 8px;

        .stats-number {
          font-size: 24px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 4px;
        }

        .stats-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
