<template>
  <el-dialog
    v-model="dialogVisible"
    title="学生详情"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      label-width="100px"
      class="view-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学生姓名">
            <div class="form-text">{{ formData.name || '-' }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号码">
            <div class="form-text">{{ formData.phone || '-' }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别">
            <div class="form-text">{{ getGenderText(formData.gender) }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年级">
            <div class="form-text">{{ getGradeText(formData.grade) }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学校">
            <div class="form-text">{{ formData.school || '-' }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班级">
            <div class="form-text">{{ formData.className || '-' }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="家长姓名">
            <div class="form-text">{{ formData.parentName || '-' }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="家长电话">
            <div class="form-text">{{ formData.parentPhone || '-' }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态">
            <div class="form-text">
              <el-tag :type="getStatusTagType(formData.status)">
                {{ getStatusText(formData.status) }}
              </el-tag>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="学习目标">
        <div class="form-text form-textarea">
          {{ formData.learningGoals || '-' }}
        </div>
      </el-form-item>

      <el-form-item label="备注">
        <div class="form-text form-textarea">
          {{ formData.remarks || '-' }}
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="false" type="primary" @click="handleEdit">
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from "vue";

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  student: {
    type: Object,
    default: null,
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "edit"]);

// 响应式数据
const formData = ref({
  name: "",
  phone: "",
  gender: "",
  grade: "",
  school: "",
  className: "",
  parentName: "",
  parentPhone: "",
  status: "active",
  learningGoals: "",
  remarks: "",
});

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 方法
const handleClose = () => {
  dialogVisible.value = false;
};

const handleEdit = () => {
  emit("edit", props.student);
  handleClose();
};

const loadFormData = () => {
  if (props.student) {
    Object.assign(formData.value, {
      ...props.student,
      // 确保性别字段为字符串类型
      gender: String(props.student.gender || ''),
      // 确保年级字段为字符串类型
      grade: String(props.student.grade || '')
    });
  }
};

// 工具方法
const getGenderText = (gender) => {
  const genderMap = {
    '0': '男',
    '1': '女',
    '2': '未知'
  };
  return genderMap[gender] || '未知';
};

const getGradeText = (grade) => {
  const gradeMap = {
    '1': '一年级',
    '2': '二年级',
    '3': '三年级',
    '4': '四年级',
    '5': '五年级',
    '6': '六年级',
    '7': '初一',
    '8': '初二',
    '9': '初三',
    '10': '高一',
    '11': '高二',
    '12': '高三'
  };
  return gradeMap[grade] || grade || '-';
};

const getStatusText = (status) => {
  const statusMap = {
    'active': '在读',
    'inactive': '暂停',
    'graduated': '毕业'
  };
  return statusMap[status] || status || '-';
};

const getStatusTagType = (status) => {
  const typeMap = {
    'active': 'success',
    'inactive': 'warning',
    'graduated': 'info'
  };
  return typeMap[status] || '';
};

// 监听器
watch(
  () => props.student,
  (newStudent) => {
    if (newStudent && props.modelValue) {
      loadFormData();
    }
  },
  { immediate: true }
);

watch(
  () => props.modelValue,
  (visible) => {
    if (visible && props.student) {
      loadFormData();
    }
  }
);
</script>

<style lang="scss" scoped>
.view-form {
  .form-text {
    min-height: 32px;
    line-height: 32px;
    padding: 0 11px;
    background-color: #fafafa;
    border: 0px solid #dcdfe6;
    border-radius: 4px;
    color: #606266;
    font-size: 14px;
    
    &.form-textarea {
      min-height: 80px;
      line-height: 1.5;
      padding: 8px 11px;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
}
</style>
