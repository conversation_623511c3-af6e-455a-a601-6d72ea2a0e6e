<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`为学生 ${studentInfo?.name || ''} 申请预约课`"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="booking-dialog-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
      >
        <!-- 学生信息展示 -->
        <el-card class="student-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>学生信息</span>
            </div>
          </template>
          <div class="student-info">
            <div class="info-item">
              <span class="label">姓名：</span>
              <span class="value">{{ studentInfo?.name }}</span>
            </div>
            <div class="info-item">
              <span class="label">手机号：</span>
              <span class="value">{{ studentInfo?.phone }}</span>
            </div>
            <div class="info-item">
              <span class="label">年级：</span>
              <span class="value">{{ studentInfo?.grade }}</span>
            </div>
            <div class="info-item">
              <span class="label">学校：</span>
              <span class="value">{{ studentInfo?.school }}</span>
            </div>
          </div>
        </el-card>

        <!-- 课程信息 -->
        <el-card class="course-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>课程信息</span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="学科" prop="subject">
                <el-select v-model="form.subject" placeholder="请选择学科" style="width: 100%" @change="handleSubjectChange">
                  <el-option label="英语" value="英语" />
                  <el-option label="语文" value="语文" />
                  <el-option label="数学" value="数学" />
                  <el-option label="物理" value="物理" />
                  <el-option label="化学" value="化学" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="课型" prop="specification">
                <el-select v-model="form.specification" placeholder="请选择课型" style="width: 100%">
                  <el-option
                    v-for="option in availableSpecifications"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 首选教师 -->
        <el-card class="teacher-selection-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Avatar /></el-icon>
              <span>首选教师（最多选择2位）</span>
              <el-button 
                type="primary" 
                size="small" 
                @click="openTeacherSelector"
                :disabled="form.preferredTeachers.length >= 2"
              >
                <el-icon><Plus /></el-icon>
                选择教师
              </el-button>
            </div>
          </template>

          <div class="selected-teachers">
            <div 
              v-for="(teacher, index) in form.preferredTeachers" 
              :key="teacher.teacherId"
              class="teacher-item"
            >
              <div class="teacher-info">
                <div class="teacher-name">{{ teacher.teacherName }}</div>
                <div class="teacher-details">
                  <span>{{ teacher.teacherPhone }}</span>
                  <span v-if="teacher.groupName">{{ teacher.groupName }}</span>
                </div>
              </div>
              <div class="teacher-priority">
                <el-tag :type="index === 0 ? 'primary' : 'info'" size="small">
                  {{ index === 0 ? '第一选择' : '第二选择' }}
                </el-tag>
              </div>
              <div class="teacher-actions">
                <el-button 
                  type="danger" 
                  size="small" 
                  text 
                  @click="removeTeacher(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>

            <el-empty 
              v-if="form.preferredTeachers.length === 0"
              description="请选择首选教师"
              :image-size="80"
            />
          </div>
        </el-card>

        <!-- 首选时间段 -->
        <el-card class="time-slots-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Clock /></el-icon>
              <span>首选时间段</span>
              <el-button 
                type="primary" 
                size="small" 
                @click="addTimeSlot"
              >
                <el-icon><Plus /></el-icon>
                添加时间段
              </el-button>
            </div>
          </template>

          <div class="time-slots">
            <div 
              v-for="(slot, index) in form.preferredTimeSlots" 
              :key="index"
              class="time-slot-item"
            >
              <el-row :gutter="10" align="middle">
                <el-col :span="6">
                  <el-select v-model="slot.weekday" placeholder="星期">
                    <el-option label="周一" :value="1" />
                    <el-option label="周二" :value="2" />
                    <el-option label="周三" :value="3" />
                    <el-option label="周四" :value="4" />
                    <el-option label="周五" :value="5" />
                    <el-option label="周六" :value="6" />
                    <el-option label="周日" :value="7" />
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-time-picker
                    v-model="slot.startTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="开始时间"
                  />
                </el-col>
                <el-col :span="1" class="time-separator">-</el-col>
                <el-col :span="5">
                  <el-time-picker
                    v-model="slot.endTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="结束时间"
                  />
                </el-col>
                <el-col :span="5">
                  <el-select v-model="slot.priority" placeholder="优先级">
                    <el-option label="最优先" :value="1" />
                    <el-option label="次优先" :value="2" />
                    <el-option label="可选" :value="3" />
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <el-button 
                    type="danger" 
                    size="small" 
                    text 
                    @click="removeTimeSlot(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-col>
              </el-row>
            </div>

            <el-empty 
              v-if="form.preferredTimeSlots.length === 0"
              description="请添加首选时间段"
              :image-size="80"
            />
          </div>
        </el-card>

        <!-- 申请原因 -->
        <el-form-item label="申请原因" prop="applicationReason">
          <el-input
            v-model="form.applicationReason"
            type="textarea"
            :rows="3"
            placeholder="请输入申请预约课的原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          提交申请
        </el-button>
      </div>
    </template>

    <!-- 教师选择对话框 -->
    <TeacherSelectorDialog
      v-model="showTeacherSelector"
      :subject="form.subject"
      :specification="form.specification"
      :selected-teachers="form.preferredTeachers"
      @confirm="handleTeacherSelect"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User, Document, Avatar, Clock, Plus, Delete 
} from '@element-plus/icons-vue'
import { submitCourseBookingApplicationApi } from '@/api/management/courseBookingApplication'
import TeacherSelectorDialog from './TeacherSelectorDialog.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// Refs
const formRef = ref()
const submitting = ref(false)
const showTeacherSelector = ref(false)

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  form.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// Computed
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Form data
const form = reactive({
  subject: '',
  specification: '',
  preferredTeachers: [],
  preferredTimeSlots: [],
  applicationReason: ''
})

// Form rules
const rules = {
  subject: [
    { required: true, message: '请选择学科', trigger: 'change' }
  ],
  specification: [
    { required: true, message: '请选择课型', trigger: 'change' }
  ],
  applicationReason: [
    { required: true, message: '请输入申请原因', trigger: 'blur' },
    { min: 10, message: '申请原因至少10个字符', trigger: 'blur' }
  ]
}

// Methods
function handleClose() {
  resetForm()
  emit('update:modelValue', false)
}

function resetForm() {
  form.subject = ''
  form.specification = ''
  form.preferredTeachers = []
  form.preferredTimeSlots = []
  form.applicationReason = ''
  formRef.value?.clearValidate()
}

function openTeacherSelector() {
  if (!form.subject || !form.specification) {
    ElMessage.warning('请先选择学科和课型')
    return
  }
  showTeacherSelector.value = true
}

function handleTeacherSelect(teachers) {
  form.preferredTeachers = teachers
}

function removeTeacher(index) {
  form.preferredTeachers.splice(index, 1)
}

function addTimeSlot() {
  form.preferredTimeSlots.push({
    weekday: null,
    startTime: '',
    endTime: '',
    priority: 1
  })
}

function removeTimeSlot(index) {
  form.preferredTimeSlots.splice(index, 1)
}

async function handleSubmit() {
  try {
    await formRef.value.validate()

    if (form.preferredTeachers.length === 0) {
      ElMessage.warning('请至少选择一位首选教师')
      return
    }

    if (form.preferredTimeSlots.length === 0) {
      ElMessage.warning('请至少添加一个首选时间段')
      return
    }

    // 验证时间段完整性
    for (let i = 0; i < form.preferredTimeSlots.length; i++) {
      const slot = form.preferredTimeSlots[i]
      if (!slot.weekday || !slot.startTime || !slot.endTime) {
        ElMessage.warning(`第${i + 1}个时间段信息不完整`)
        return
      }
    }

    await ElMessageBox.confirm(
      '确定要提交预约课申请吗？',
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    submitting.value = true

    const requestData = {
      studentId: props.studentInfo.id,
      subject: form.subject,
      specification: form.specification,
      preferredTeachers: form.preferredTeachers.map(t => t.teacherId),
      preferredTimeSlots: form.preferredTimeSlots,
      applicationReason: form.applicationReason
    }

    await submitCourseBookingApplicationApi(requestData)

    ElMessage.success('预约课申请提交成功')
    emit('success')
    handleClose()

  } catch (error) {
    console.error('提交预约课申请失败:', error)
    ElMessage.error('提交预约课申请失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// Watch for dialog visibility changes
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.booking-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.student-info-card,
.course-info-card,
.teacher-selection-card,
.time-slots-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.card-header .el-button {
  margin-left: auto;
}

.student-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
}

.selected-teachers {
  min-height: 100px;
}

.teacher-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.teacher-details {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 12px;
}

.teacher-priority {
  margin-right: 12px;
}

.time-slots {
  min-height: 100px;
}

.time-slot-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.time-separator {
  text-align: center;
  font-weight: 500;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
