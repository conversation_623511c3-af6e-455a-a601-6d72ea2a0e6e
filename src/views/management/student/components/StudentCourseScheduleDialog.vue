<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${student?.name || '学生'}的课表`"
    width="95%"
    :before-close="handleClose"
    destroy-on-close
    class="schedule-dialog"
    top="2vh"
  >
    <!-- 完整的课程表页面 -->
    <div class="curriculum-dialog-wrapper">
      <CurriculumIndex
        :student-id="student?.id"
        :is-dialog-mode="true"
        :external-student-context="student"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import CurriculumIndex from '@/views/curriculum/index.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  student: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 事件处理
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.curriculum-dialog-wrapper {
  height: 80vh;
  overflow: hidden;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-dialog) {
  max-height: 95vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: hidden;
  padding: 0;
}

// 确保课程表页面在弹窗中正常显示
:deep(.curriculum-dialog-wrapper) {
  .curriculum-container {
    height: 100%;
    overflow: hidden;
  }

  .schedule-section {
    height: 100%;
    overflow: hidden;

    .el-card {
      height: 100%;

      .el-card__body {
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 16px;
      }
    }
  }

  .schedule-content {
    flex: 1;
    overflow: hidden;
  }
}
</style>
