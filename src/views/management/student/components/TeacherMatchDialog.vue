<template>
  <el-dialog
    v-model="dialogVisible"
    title="匹配老师"
    width="95%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="teacher-match-dialog"
    @close="handleClose"
  >
    <div class="teacher-match-container">
      <div class="match-layout">
        <!-- 左侧：搜索条件 -->
        <div class="search-panel">
          <el-card class="search-card">
            <template #header>
              <span>匹配条件</span>
            </template>

            <el-form
              ref="searchFormRef"
              :model="searchForm"
              :rules="searchRules"
              label-width="80px"
              class="search-form"
            >
              <!-- 学生信息显示（锁定状态） -->
              <el-form-item label="学生信息">
                <div class="locked-student-info">
                  <div class="student-name">{{ studentInfo.name }}</div>
                  <div class="student-details">
                    <span>{{ studentInfo.phone }}</span>
                    <span v-if="studentInfo.grade">{{ getGradeText(studentInfo.grade) }}</span>
                    <span v-if="studentInfo.school">{{ studentInfo.school }}</span>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="开始日期" prop="startDate">
                <el-date-picker
                  v-model="searchForm.startDate"
                  type="date"
                  placeholder="选择开始日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  size="small"
                />
              </el-form-item>

              <el-form-item label="教学组">
                <el-select
                  v-model="searchForm.groupId"
                  placeholder="全部教学组"
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option
                    v-for="group in teachingGroups"
                    :key="group.id"
                    :label="group.name"
                    :value="group.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="关键词">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="教师姓名或手机号"
                  clearable
                  size="small"
                />
              </el-form-item>

              <!-- 高级筛选 -->
              <el-form-item>
                <el-button
                  type="text"
                  size="small"
                  @click="showAdvancedFilters = !showAdvancedFilters"
                >
                  {{ showAdvancedFilters ? '收起筛选' : '高级筛选' }}
                  <el-icon>
                    <ArrowDown v-if="!showAdvancedFilters" />
                    <ArrowUp v-if="showAdvancedFilters" />
                  </el-icon>
                </el-button>
              </el-form-item>

              <!-- 高级筛选面板 -->
              <div v-show="showAdvancedFilters" class="advanced-filters">
                <!-- 基础信息筛选 -->
                <el-divider content-position="left">基础信息</el-divider>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="性别" size="small">
                      <el-select v-model="searchForm.gender" placeholder="不限" clearable style="width: 100%">
                        <el-option label="男" value="0" />
                        <el-option label="女" value="1" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="工作性质" size="small">
                      <el-select v-model="searchForm.employmentType" placeholder="不限" clearable style="width: 100%">
                        <el-option label="全职" value="full_time" />
                        <el-option label="意向全职" value="intended_full_time" />
                        <el-option label="兼职" value="part_time" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="年龄范围" size="small">
                      <el-input-number v-model="searchForm.minAge" :min="18" :max="70" placeholder="最小年龄" style="width: 48%" />
                      <span style="margin: 0 8px">-</span>
                      <el-input-number v-model="searchForm.maxAge" :min="18" :max="70" placeholder="最大年龄" style="width: 48%" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="教龄范围" size="small">
                      <el-input-number v-model="searchForm.minTeachingYears" :min="0" :max="50" placeholder="最少教龄" style="width: 48%" />
                      <span style="margin: 0 8px">-</span>
                      <el-input-number v-model="searchForm.maxTeachingYears" :min="0" :max="50" placeholder="最多教龄" style="width: 48%" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 教育背景筛选 -->
                <el-divider content-position="left">教育背景</el-divider>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="学历" size="small">
                      <el-select v-model="searchForm.education" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="本科" value="本科" />
                        <el-option label="硕士" value="硕士" />
                        <el-option label="博士" value="博士" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="大学属性" size="small">
                      <el-select v-model="searchForm.universityType" multiple placeholder="不限" clearable style="width: 100%">
                            <el-option label="双一流" value="双一流" />
                            <el-option label="985" value="985" />
                            <el-option label="211" value="211" />
                            <el-option label="一本" value="一本" />
                            <el-option label="普通" value="普通" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="师范院校" size="small">
                      <el-select v-model="searchForm.isNormalUniversity" placeholder="不限" clearable style="width: 100%">
                        <el-option label="是" :value="true" />
                        <el-option label="否" :value="false" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="留学经历" size="small">
                      <el-select v-model="searchForm.studyAbroad" placeholder="不限" clearable style="width: 100%">
                        <el-option label="有" :value="true" />
                        <el-option label="无" :value="false" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 教学资质筛选 -->
                <el-divider content-position="left">教学资质</el-divider>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="教资级别" size="small">
                      <el-select v-model="searchForm.teachingCertificateLevel" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="小学" value="小学" />
                        <el-option label="初中" value="初中" />
                        <el-option label="高中" value="高中" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="教授学科" size="small">
                      <el-select v-model="searchForm.subjects" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="英语" value="英语" />
                        <el-option label="语文" value="语文" />
                        <el-option label="数学" value="数学" />
                        <el-option label="物理" value="物理" />
                        <el-option label="化学" value="化学" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="已通过培训科目" size="small">
                      <el-select v-model="searchForm.trainingSubjects" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="音标" value="音标" />
                        <el-option label="语法" value="语法" />
                        <el-option label="阅读" value="阅读" />
                        <el-option label="听说" value="听说" />
                        <el-option label="写作" value="写作" />
                        <el-option label="完型" value="完型" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="英语资质" size="small">
                      <el-select v-model="searchForm.englishQualification" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="四级" value="四级" />
                      <el-option label="六级" value="六级" />
                      <el-option label="专四" value="专四" />
                      <el-option label="专八" value="专八" />
                      <el-option label="雅思" value="雅思" />
                      <el-option label="托福" value="托福" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="沟通能力" size="small">
                      <el-select v-model="searchForm.communicationAbility" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="优秀" value="优秀" />
                        <el-option label="良好" value="良好" />
                        <el-option label="一般" value="一般" />
                        <el-option label="薄弱" value="薄弱" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="英语发音" size="small">
                      <el-select v-model="searchForm.englishPronunciation" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="优秀（母语水平）" value="优秀（母语水平）" />
                        <el-option label="良好" value="良好" />
                        <el-option label="正常" value="正常" />
                        <el-option label="一般" value="一般" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 教学风格筛选 -->
                <el-divider content-position="left">教学风格</el-divider>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="教过课程" size="small">
                      <el-select v-model="searchForm.taughtCourses" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="音标课" value="音标课" />
                        <el-option label="语法课" value="语法课" />
                        <el-option label="阅读课" value="阅读课" />
                        <el-option label="写作课" value="写作课" />
                        <el-option label="口语课" value="口语课" />
                        <el-option label="听力课" value="听力课" />
                        <el-option label="高考课" value="高考课" />
                        <el-option label="中考课" value="中考课" />
                        <el-option label="完型课" value="完型课" />
                        <el-option label="词汇课" value="词汇课" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="上课风格" size="small">
                      <el-select v-model="searchForm.teachingStyle" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="温柔" value="温柔" />
                        <el-option label="亲切" value="亲切" />
                        <el-option label="幽默" value="幽默" />
                        <el-option label="严肃" value="严肃" />
                        <el-option label="活泼" value="活泼" />
                        <el-option label="耐心" value="耐心" />
                        <el-option label="鼓励式" value="鼓励式" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="适合年级" size="small">
                      <el-select
                        v-model="searchForm.suitableGrades"
                        multiple
                        placeholder="不限"
                        clearable
                        style="width: 100%"
                        collapse-tags
                        collapse-tags-tooltip
                      >
                        <el-option
                          v-for="grade in gradeOptions"
                          :key="grade.value"
                          :label="grade.label"
                          :value="grade.value"
                        />
                      </el-select>
                      <!-- 快速选择按钮 -->
                      <div class="grade-quick-select" style="margin-top: 4px;">
                        <el-button size="small" @click="selectPrimaryGrades">小学</el-button>
                        <el-button size="small" @click="selectMiddleGrades">初中</el-button>
                        <el-button size="small" @click="selectHighGrades">高中</el-button>
                        <el-button size="small" @click="clearGradeSelection">清空</el-button>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="适合程度" size="small">
                      <el-select v-model="searchForm.suitableLevels" multiple placeholder="不限" clearable style="width: 100%">
                        <el-option label="学霸" value="学霸" />
                        <el-option label="中等生" value="中等生" />
                        <el-option label="学困生" value="学困生" />
                        <el-option label="零基础" value="零基础" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="适合性格" size="small">
                      <el-select v-model="searchForm.suitablePersonality" placeholder="不限" clearable style="width: 100%">
                        <el-option label="外向活泼" value="外向活泼" />
                        <el-option label="内向腼腆" value="内向腼腆" />
                        <el-option label="都适合" value="都适合" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="课点更新天数" size="small">
                      <el-input-number
                        v-model="searchForm.timeSlotUpdateDays"
                        placeholder="默认3天"
                        :min="1"
                        :max="30"
                        style="width: 100%"
                        size="small"
                        controls-position="right"
                      />
                      <div class="form-item-tip">老师可排课时间最后更新距今天数</div>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 筛选操作按钮 -->
                <el-row :gutter="16">
                  <el-col :span="24" style="text-align: right; margin-top: 16px;">
                    <el-button size="small" @click="resetFilters">重置筛选</el-button>
                    <el-button type="primary" size="small" @click="applyFilters">应用筛选</el-button>
                  </el-col>
                </el-row>
              </div>

              <el-form-item prop="timeSlots" label-width="0">
                <div class="time-slots-container">
                  <div class="time-slots-header">
                    <span>上课时间段</span>
                    <el-button
                      type="primary"
                      size="small"
                      :icon="Plus"
                      @click="addTimeSlot"
                    >
                      添加
                    </el-button>
                  </div>

                  <div v-if="searchForm.timeSlots.length > 0" class="time-slots-list">
                    <div
                      v-for="(slot, index) in searchForm.timeSlots"
                      :key="index"
                      class="time-slot-item"
                    >
                      <div class="schedule-row">
                        <div class="time-controls">
                          <el-select
                            v-model="slot.weekday"
                            placeholder="星期"
                            size="small"
                            @change="onDayOfWeekChange(slot)"
                            class="weekday-select"
                          >
                            <el-option
                              v-for="day in weekDays"
                              :key="day.value"
                              :label="day.label"
                              :value="day.value"
                            />
                          </el-select>

                          <el-select
                            v-model="slot.duration"
                            placeholder="时长"
                            @change="onDurationChange(slot)"
                            size="small"
                            class="duration-select"
                          >
                            <el-option
                              v-for="duration in durationOptions"
                              :key="duration.value"
                              :label="duration.label"
                              :value="duration.value"
                            />
                          </el-select>

                          <div class="time-range-input">
                            <el-select
                              v-model="slot.startTime"
                              placeholder="开始时间"
                              filterable
                              size="small"
                              @change="updateEndTime(slot)"
                              class="start-time-select"
                            >
                              <el-option
                                v-for="time in getAvailableStartTimeOptions(
                                  slot.duration
                                )"
                                :key="time.value"
                                :label="time.label"
                                :value="time.value"
                              />
                            </el-select>
                            <span class="time-separator">～</span>
                            <span class="end-time">{{ slot.endTime || "--:--" }}</span>
                          </div>
                        </div>

                        <div class="slot-actions">
                          <el-button
                            type="danger"
                            size="small"
                            :icon="Delete"
                            @click="removeTimeSlot(index)"
                            circle
                            v-if="searchForm.timeSlots.length > 1"
                            class="delete-btn"
                          />
                        </div>
                      </div>

                      <div class="time-summary">
                        <el-tag size="small" class="time-tag">
                          {{ getTimeSlotDisplay(slot) }}
                        </el-tag>
                      </div>
                    </div>
                  </div>

                  <div v-else class="empty-time-slots">
                    <el-empty description="暂无时间段设置" :image-size="60">
                      <el-button type="primary" @click="addTimeSlot">
                        <el-icon><Plus /></el-icon>
                        添加第一个时间段
                      </el-button>
                    </el-empty>
                  </div>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleSearch"
                  :loading="searching"
                  :disabled="!canSearch"
                  size="small"
                  style="width: 100%; margin-bottom: 8px"
                >
                  <el-icon><Search /></el-icon>
                  开始匹配老师
                </el-button>
                <el-button @click="handleReset" size="small" style="width: 100%">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 右侧：匹配结果 -->
        <div class="result-panel">
          <el-card v-if="matchResult" class="result-card">
            <template #header>
              <div class="result-header">
                <span>匹配结果</span>
                <el-tag type="info">找到 {{ matchResult.totalCount }} 位老师</el-tag>
              </div>
            </template>

            <!-- 教师列表 -->
            <div class="teachers-list">
              <div class="teachers-grid">
                <div
                  v-for="teacher in matchResult.teachers"
                  :key="teacher.teacherId"
                  class="teacher-card"
                >
                  <div class="teacher-header">
                    <div class="teacher-basic">
                      <h5>{{ teacher.teacherName }}</h5>
                      <span class="teacher-phone">{{ teacher.teacherPhone }}</span>
                    </div>
                  </div>

                  <div class="teacher-info">
                    <!-- 基础信息 -->
                    <div class="info-row">
                      <div class="info-item">
                        <span class="label">教学组:</span>
                        <span>{{ teacher.groupName || "未分组" }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">经验:</span>
                        <span>{{ teacher.teachingYears }}年</span>
                      </div>
                    </div>

                    <div class="info-row">
                      <div class="info-item">
                        <span class="label">学生:</span>
                        <span>{{ teacher.currentStudents }}人</span>
                      </div>
                      <div class="info-item" v-if="teacher.age">
                        <span class="label">年龄:</span>
                        <span>{{ teacher.age }}岁</span>
                      </div>
                    </div>

                    <div class="info-row" v-if="teacher.employmentType">
                      <div class="info-item">
                        <span class="label">性质:</span>
                        <el-tag size="small" :type="getEmploymentTypeTagType(teacher.employmentType)">
                          {{ getEmploymentTypeText(teacher.employmentType) }}
                        </el-tag>
                      </div>
                    </div>

                    <!-- 教育背景 -->
                    <div class="info-row" v-if="teacher.education || teacher.universityType">
                      <div class="info-item" v-if="teacher.education">
                        <span class="label">学历:</span>
                        <span>{{ teacher.education }}</span>
                      </div>
                      <div class="info-item" v-if="teacher.universityType">
                        <span class="label">院校:</span>
                        <el-tag size="small" :type="getUniversityTypeTagType(teacher.universityType)">
                          {{ teacher.universityType }}
                        </el-tag>
                      </div>
                    </div>

                    <!-- 教学资质 -->
                    <div class="info-item">
                      <span class="label">学科:</span>
                      <el-tag
                        v-for="subject in teacher.subjects"
                        :key="subject"
                        size="small"
                        class="subject-tag"
                      >
                        {{ subject }}
                      </el-tag>
                    </div>

                    <!-- 已通过培训科目 -->
                    <div class="info-item" v-if="teacher.trainingSubjects && teacher.trainingSubjects.length > 0">
                      <span class="label">培训:</span>
                      <el-tag
                        v-for="subject in teacher.trainingSubjects"
                        :key="subject"
                        size="small"
                        type="success"
                        class="training-tag"
                      >
                        {{ subject }}
                      </el-tag>
                    </div>

                    <!-- 教过课程 -->
                    <div class="info-item" v-if="teacher.taughtCourses && teacher.taughtCourses.length > 0">
                      <span class="label">课程:</span>
                      <el-tag
                        v-for="course in teacher.taughtCourses.slice(0, 3)"
                        :key="course"
                        size="small"
                        type="info"
                        class="course-tag"
                      >
                        {{ course }}
                      </el-tag>
                      <span v-if="teacher.taughtCourses.length > 3" class="more-tags">
                        +{{ teacher.taughtCourses.length - 3 }}
                      </span>
                    </div>

                    <!-- 教学风格 -->
                    <div class="info-item" v-if="teacher.teachingStyle && teacher.teachingStyle.length > 0">
                      <span class="label">风格:</span>
                      <el-tag
                        v-for="style in teacher.teachingStyle.slice(0, 3)"
                        :key="style"
                        size="small"
                        type="success"
                        class="style-tag"
                      >
                        {{ style }}
                      </el-tag>
                      <span v-if="teacher.teachingStyle.length > 3" class="more-tags">
                        +{{ teacher.teachingStyle.length - 3 }}
                      </span>
                    </div>

                    <!-- 适合年级 -->
                    <div class="info-item" v-if="teacher.suitableGrades && teacher.suitableGrades.length > 0">
                      <span class="label">适合:</span>
                      <el-tag
                        v-for="grade in teacher.suitableGrades.slice(0, 4)"
                        :key="grade"
                        size="small"
                        type="primary"
                        class="grade-tag"
                      >
                        {{ grade }}
                      </el-tag>
                      <span v-if="teacher.suitableGrades.length > 4" class="more-tags">
                        +{{ teacher.suitableGrades.length - 4 }}
                      </span>
                    </div>

                    <!-- 其他特色 -->
                    <div class="info-row" v-if="teacher.englishQualification || teacher.communicationAbility">
                      <div class="info-item" v-if="teacher.englishQualification">
                        <span class="label">英语:</span>
                        <el-tag size="small" type="warning">{{ teacher.englishQualification }}</el-tag>
                      </div>
                      <div class="info-item" v-if="teacher.communicationAbility">
                        <span class="label">沟通:</span>
                        <el-tag size="small" :type="getCommunicationAbilityTagType(teacher.communicationAbility)">
                          {{ teacher.communicationAbility }}
                        </el-tag>
                      </div>
                    </div>
                  </div>

                  <div class="teacher-actions">
                    <el-button size="small" @click="viewTeacherDetail(teacher)">
                      <el-icon><User /></el-icon>
                      查看详情
                    </el-button>
                    <el-button size="small" @click="viewTeacherSchedule(teacher)">
                      <el-icon><Calendar /></el-icon>
                      查看时间表
                    </el-button>
                    <el-button
                      type="primary"
                      size="small"
                      @click="selectTeacher(teacher)"
                    >
                      <el-icon><Check /></el-icon>
                      直接分配
                    </el-button>
                    <el-button
                      type="success"
                      size="small"
                      @click="bookingCourse(teacher)"
                    >
                      <el-icon><Calendar /></el-icon>
                      预约课程
                    </el-button>
                  </div>
                </div>
              </div>

              <el-empty
                v-if="matchResult.teachers.length === 0"
                description="没有找到匹配的教师"
              />
            </div>
          </el-card>

          <!-- 空状态 -->
          <div v-else class="empty-result">
            <el-empty description="请设置匹配条件并开始匹配" />
          </div>
        </div>
      </div>
    </div>

    <!-- 教师时间表查看对话框 -->
    <TeacherScheduleDialog
      v-model="showScheduleDialog"
      :teacher="selectedTeacher"
      :start-date="searchForm.startDate"
    />

    <!-- 预约课申请对话框 -->
    <CourseBookingDialog
      v-model="showBookingDialog"
      :student-info="studentInfo"
      @success="handleBookingSuccess"
    />

    <!-- 教师详情查看对话框 -->
    <TeacherDetailDialog
      v-model="showDetailDialog"
      :teacher="selectedTeacherForDetail"
    />

    <!-- 排课对话框 -->
    <ScheduleDialog
      v-if="showCourseScheduleDialog && selectedTeacherForSchedule"
      v-model="showCourseScheduleDialog"
      :teacher-id="selectedTeacherForSchedule.teacherId"
      :teacher-name="selectedTeacherForSchedule.teacherName"
      :student-id="studentInfo.id"
      :student-name="studentInfo.name"
      :start-date="searchForm.startDate"
      :time-slots="convertedTimeSlots"
      @success="handleScheduleSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete, Search, Refresh, Calendar, Check, ArrowDown, ArrowUp, User } from "@element-plus/icons-vue";
import { matchTeachersApi } from "@/api/management/teacherMatch";
import { assignTeacherToStudentApi } from "@/api/management/student";
import { getTeachingGroupsApi } from "@/api/management/teachingGroup";
import { GRADE_OPTIONS, getGradeText, getGradesByStage, getDefaultSuitableGrades } from '@/utils/gradeUtils';
import TeacherScheduleDialog from "@/views/management/teacher-match/components/TeacherScheduleDialog.vue";
import TeacherDetailDialog from "@/views/management/teacher/components/TeacherDetailDialog.vue";
import CourseBookingDialog from "./CourseBookingDialog.vue";
import ScheduleDialog from "@/views/curriculum/components/ScheduleDialog.vue";

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  student: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "success"]);

// 响应式数据
const dialogVisible = ref(false);
const searchFormRef = ref();
const searching = ref(false);
const showScheduleDialog = ref(false);
const showDetailDialog = ref(false);
const showCourseScheduleDialog = ref(false);
const showBookingDialog = ref(false);

// 学生信息（锁定状态）
const studentInfo = computed(() => props.student || {});

// 搜索表单
const searchForm = reactive({
  studentId: "",
  startDate: "",
  timeSlots: [],
  keyword: "",
  groupId: "",

  // ========== 基础信息筛选 ==========
  gender: "",
  minAge: null,
  maxAge: null,
  employmentType: "",
  currentStatus: "",

  // ========== 教育背景筛选 ==========
  education: [],
  universityType: [],
  isNormalUniversity: null,
  studyAbroad: null,

  // ========== 教学资质筛选 ==========
  teachingCertificateLevel: [],
  subjects: [],
  trainingSubjects: [],
  englishQualification: [],
  mandarinQualification: [],
  communicationAbility: [],
  englishPronunciation: [],
  minTeachingYears: null,
  maxTeachingYears: null,

  // ========== 教学经历和风格筛选 ==========
  taughtCourses: [],
  teachingStyle: [],
  suitableGrades: [],
  suitableLevels: [],
  suitablePersonality: "",

  // ========== 课点更新天数筛选 ==========
  timeSlotUpdateDays: 3
});

// 高级筛选状态
const showAdvancedFilters = ref(false);

// 表单验证规则
const searchRules = {
  startDate: [{ required: true, message: "请选择开始日期", trigger: "change" }],
  timeSlots: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error("请设置学生可上课时间段"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
};

// 选项数据
const teachingGroups = ref([]);
const matchResult = ref(null);
const selectedTeacher = ref(null);
const selectedTeacherForDetail = ref(null);
const selectedTeacherForSchedule = ref(null);

// 星期选项
const weekDays = [
  { label: "周一", value: 1 },
  { label: "周二", value: 2 },
  { label: "周三", value: 3 },
  { label: "周四", value: 4 },
  { label: "周五", value: 5 },
  { label: "周六", value: 6 },
  { label: "周日", value: 7 },
];

// 时长选项
const durationOptions = [
  { label: "60分钟", value: 60 },
  { label: "90分钟", value: 90 },
  { label: "120分钟", value: 120 },
];

// 年级选项
const gradeOptions = GRADE_OPTIONS;

// 生成时间选项（从06:00到24:00，每5分钟一个档）
const generateTimeOptions = () => {
  const options = [];
  for (let hour = 6; hour <= 24; hour++) {
    const maxMinute = hour === 24 ? 0 : 60; // 24点只显示00分
    for (let minute = 0; minute < maxMinute; minute += 5) {
      const timeStr = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;
      options.push({
        label: timeStr,
        value: timeStr,
      });
    }
  }
  return options;
};

const timeOptions = generateTimeOptions();

// 计算属性
const canSearch = computed(() => {
  return searchForm.startDate && searchForm.timeSlots.length > 0;
});

// 转换时间段格式，从匹配页面格式转换为排课组件格式
const convertedTimeSlots = computed(() => {
  if (!searchForm.timeSlots || searchForm.timeSlots.length === 0) {
    return [];
  }

  return searchForm.timeSlots.map((slot) => ({
    dayOfWeek: slot.weekday, // 直接使用weekday，因为都是1-7格式
    startTime: slot.startTime,
    endTime: slot.endTime,
    duration: slot.duration || 60,
  }));
});

// 年级快速选择方法
const selectPrimaryGrades = () => {
  searchForm.suitableGrades = getGradesByStage('小学')
}

const selectMiddleGrades = () => {
  searchForm.suitableGrades = getGradesByStage('初中')
}

const selectHighGrades = () => {
  searchForm.suitableGrades = getGradesByStage('高中')
}

const clearGradeSelection = () => {
  searchForm.suitableGrades = []
}

// 监听器
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      initializeForm();
    }
  }
);

watch(dialogVisible, (newVal) => {
  emit("update:modelValue", newVal);
});

// 方法
const initializeForm = () => {
  // 设置学生ID
  searchForm.studentId = studentInfo.value.id;
  // 设置默认开始日期为今天
  searchForm.startDate = new Date().toISOString().split("T")[0];
  // 清空其他字段
  searchForm.timeSlots = [];
  searchForm.keyword = "";
  searchForm.groupId = "";
  matchResult.value = null;

  // 根据学生年级设置默认的适合年级筛选条件
  if (studentInfo.value.grade) {
    searchForm.suitableGrades = getDefaultSuitableGrades(studentInfo.value.grade);
  }

  // 加载教学组数据
  loadTeachingGroups();
};

async function loadTeachingGroups() {
  try {
    const { data } = await getTeachingGroupsApi({
      pageSize: 100, // 获取所有教学组
    });
    teachingGroups.value = data?.records || [];
  } catch (error) {
    console.error("加载教学组失败:", error);
  }
}

// 时间段管理
function addTimeSlot() {
  searchForm.timeSlots.push({
    weekday: 1,
    startTime: "",
    endTime: "",
    duration: 60,
  });
}

function removeTimeSlot(index) {
  searchForm.timeSlots.splice(index, 1);
}

function onDayOfWeekChange(slot) {
  // 星期变化时可以做一些处理
}

function onDurationChange(slot) {
  // 时长变化时重新计算结束时间
  updateEndTime(slot);
}

function updateEndTime(slot) {
  if (!slot.startTime || !slot.duration) {
    slot.endTime = "";
    return;
  }

  const [hours, minutes] = slot.startTime.split(":").map(Number);
  const startMinutes = hours * 60 + minutes;
  const endMinutes = startMinutes + slot.duration;

  const endHours = Math.floor(endMinutes / 60);
  const endMins = endMinutes % 60;

  slot.endTime = `${endHours.toString().padStart(2, "0")}:${endMins
    .toString()
    .padStart(2, "0")}`;
}

function getAvailableStartTimeOptions(duration = 60) {
  if (!duration) return timeOptions;

  return timeOptions.filter((option) => {
    const [hours, minutes] = option.value.split(":").map(Number);
    const startMinutes = hours * 60 + minutes;
    const endMinutes = startMinutes + duration;

    // 确保结束时间不超过24:00
    return endMinutes <= 24 * 60;
  });
}

function getTimeSlotDisplay(slot) {
  if (!slot.weekday || !slot.startTime || !slot.endTime) {
    return "未完整设置";
  }

  const weekDay = weekDays.find((day) => day.value === slot.weekday);
  return `${weekDay?.label} ${slot.startTime}-${slot.endTime}`;
}

// 搜索和重置
async function handleSearch() {
  try {
    await searchFormRef.value.validate();

    searching.value = true;

    // 构建请求参数，只传递有值的筛选条件
    const requestParams = {
      studentId: searchForm.studentId,
      startDate: searchForm.startDate,
      timeSlots: searchForm.timeSlots,
      keyword: searchForm.keyword,
      groupId: searchForm.groupId,
    };

    // 添加基础信息筛选
    if (searchForm.gender) requestParams.gender = searchForm.gender;
    if (searchForm.minAge !== null) requestParams.minAge = searchForm.minAge;
    if (searchForm.maxAge !== null) requestParams.maxAge = searchForm.maxAge;
    if (searchForm.employmentType) requestParams.employmentType = searchForm.employmentType;
    if (searchForm.currentStatus) requestParams.currentStatus = searchForm.currentStatus;

    // 添加教育背景筛选
    if (searchForm.education.length > 0) requestParams.education = searchForm.education;
    if (searchForm.universityType.length > 0) requestParams.universityType = searchForm.universityType;
    if (searchForm.isNormalUniversity !== null) requestParams.isNormalUniversity = searchForm.isNormalUniversity;
    if (searchForm.studyAbroad !== null) requestParams.studyAbroad = searchForm.studyAbroad;

    // 添加教学资质筛选
    if (searchForm.teachingCertificateLevel.length > 0) requestParams.teachingCertificateLevel = searchForm.teachingCertificateLevel;
    if (searchForm.subjects.length > 0) requestParams.subjects = searchForm.subjects;
    if (searchForm.trainingSubjects.length > 0) requestParams.trainingSubjects = searchForm.trainingSubjects;
    if (searchForm.englishQualification.length > 0) requestParams.englishQualification = searchForm.englishQualification;
    if (searchForm.mandarinQualification.length > 0) requestParams.mandarinQualification = searchForm.mandarinQualification;
    if (searchForm.communicationAbility.length > 0) requestParams.communicationAbility = searchForm.communicationAbility;
    if (searchForm.englishPronunciation.length > 0) requestParams.englishPronunciation = searchForm.englishPronunciation;
    if (searchForm.minTeachingYears !== null) requestParams.minTeachingYears = searchForm.minTeachingYears;
    if (searchForm.maxTeachingYears !== null) requestParams.maxTeachingYears = searchForm.maxTeachingYears;

    // 添加教学经历和风格筛选
    if (searchForm.taughtCourses.length > 0) requestParams.taughtCourses = searchForm.taughtCourses;
    if (searchForm.teachingStyle.length > 0) requestParams.teachingStyle = searchForm.teachingStyle;
    if (searchForm.suitableGrades.length > 0) requestParams.suitableGrades = searchForm.suitableGrades;
    if (searchForm.suitableLevels.length > 0) requestParams.suitableLevels = searchForm.suitableLevels;
    if (searchForm.suitablePersonality) requestParams.suitablePersonality = searchForm.suitablePersonality;

    // 添加课点更新天数筛选
    if (searchForm.timeSlotUpdateDays !== null && searchForm.timeSlotUpdateDays !== undefined) {
      requestParams.timeSlotUpdateDays = searchForm.timeSlotUpdateDays;
    }

    const { data } = await matchTeachersApi(requestParams);

    matchResult.value = data;

    if (data.totalCount === 0) {
      ElMessage.info("没有找到匹配的教师，请调整搜索条件");
    } else {
      ElMessage.success(`找到 ${data.totalCount} 位匹配的教师`);
    }
  } catch (error) {
    console.error("匹配教师失败:", error);
    ElMessage.error("匹配教师失败");
  } finally {
    searching.value = false;
  }
}

function handleReset() {
  searchForm.timeSlots = [];
  searchForm.keyword = "";
  searchForm.groupId = "";
  searchForm.startDate = new Date().toISOString().split("T")[0];
  matchResult.value = null;
  resetFilters();
}

// 重置筛选条件
function resetFilters() {
  // 基础信息筛选
  searchForm.gender = "";
  searchForm.minAge = null;
  searchForm.maxAge = null;
  searchForm.employmentType = "";
  searchForm.currentStatus = "";

  // 教育背景筛选
  searchForm.education = [];
  searchForm.universityType = [];
  searchForm.isNormalUniversity = null;
  searchForm.studyAbroad = null;

  // 教学资质筛选
  searchForm.teachingCertificateLevel = [];
  searchForm.subjects = [];
  searchForm.trainingSubjects = [];
  searchForm.englishQualification = [];
  searchForm.mandarinQualification = [];
  searchForm.communicationAbility = [];
  searchForm.englishPronunciation = [];
  searchForm.minTeachingYears = null;
  searchForm.maxTeachingYears = null;

  // 教学经历和风格筛选
  searchForm.taughtCourses = [];
  searchForm.teachingStyle = [];
  searchForm.suitableGrades = [];
  searchForm.suitableLevels = [];
  searchForm.suitablePersonality = "";

  // 重置课点更新天数筛选
  searchForm.timeSlotUpdateDays = 3;
}

// 应用筛选条件
function applyFilters() {
  // 触发搜索
  handleSearch();
}

// 标签样式辅助方法
function getUniversityTypeTagType(type) {
  const typeMap = {
    '985': 'danger',
    '211': 'warning',
    '重点': 'primary',
    '普通': 'info'
  };
  return typeMap[type] || 'info';
}

function getCommunicationAbilityTagType(ability) {
  const abilityMap = {
    '优秀': 'success',
    '良好': 'primary',
    '一般': 'warning',
    '薄弱': 'danger'
  };
  return abilityMap[ability] || 'info';
}

// 教师操作
function viewTeacherDetail(teacher) {
  selectedTeacherForDetail.value = {
    id: teacher.teacherId,
    name: teacher.teacherName,
    phone: teacher.teacherPhone,
    ...teacher
  };
  showDetailDialog.value = true;
}

function viewTeacherSchedule(teacher) {
  selectedTeacher.value = teacher;
  showScheduleDialog.value = true;
}

async function selectTeacher(teacher) {
  try {
    await ElMessageBox.confirm(`确定选择教师 ${teacher.teacherName} 吗？`, "确认选择", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "info",
    });

    // 保存选中的教师信息
    selectedTeacherForSchedule.value = teacher;

    // 先建立师生关系
    ElMessage.info("正在建立师生关系...");
    try {
      await assignTeacherToStudentApi(searchForm.studentId, teacher.teacherId);
      ElMessage.success("师生关系建立成功");

      // 师生关系建立成功后才打开排课对话框
      showCourseScheduleDialog.value = true;
      ElMessage.success(`已选择教师：${teacher.teacherName}，正在打开排课界面`);
    } catch (error) {
      console.error("建立师生关系失败:", error);
      ElMessage.error("建立师生关系失败，无法进行排课");
      // 师生关系建立失败时不打开排课对话框
      selectedTeacherForSchedule.value = null;
      return;
    }
  } catch (error) {
    // 用户取消或其他错误
  }
}

// 预约课程功能
function bookingCourse(teacher) {
  // 打开预约课申请对话框
  showBookingDialog.value = true;
}

// 处理预约课申请成功
function handleBookingSuccess() {
  ElMessage.success('预约课申请提交成功');
  emit('success');
}

// 排课成功处理
function handleScheduleSuccess() {
  showCourseScheduleDialog.value = false;
  selectedTeacherForSchedule.value = null;
  ElMessage.success("排课成功！");
  emit("success");
  handleClose();
}

// 关闭对话框
function handleClose() {
  dialogVisible.value = false;
  // 重置数据
  matchResult.value = null;
  selectedTeacher.value = null;
  selectedTeacherForDetail.value = null;
  selectedTeacherForSchedule.value = null;
  showScheduleDialog.value = false;
  showDetailDialog.value = false;
  showCourseScheduleDialog.value = false;
}

// 获取工作性质标签类型
function getEmploymentTypeTagType(type) {
  const typeMap = {
    'full_time': 'success',
    'intended_full_time': 'primary',
    'part_time': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取工作性质文本
function getEmploymentTypeText(type) {
  const textMap = {
    'full_time': '全职',
    'intended_full_time': '意向全职',
    'part_time': '兼职'
  }
  return textMap[type] || type
}
</script>

<style scoped>
.teacher-match-dialog .dialog-content {
  display: flex;
  gap: 20px;
  height: 70vh;
  min-height: 500px;
}

.teacher-match-dialog .search-panel {
  width: 400px; /* 增加宽度以容纳筛选条件 */
  flex-shrink: 0;
}

.teacher-match-dialog .result-panel {
  flex: 1;
  min-width: 0;
}

.teacher-match-dialog .search-card,
.teacher-match-dialog .result-card {
  height: 100%;
}

.teacher-match-dialog .search-card :deep(.el-card__body),
.teacher-match-dialog .result-card :deep(.el-card__body) {
  height: calc(100% - 60px);
  overflow-y: auto;
}

/* 高级筛选面板样式 */
.advanced-filters {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.advanced-filters :deep(.el-divider) {
  margin: 16px 0 12px 0;
}

.advanced-filters :deep(.el-divider__text) {
  font-size: 13px;
  font-weight: 500;
  color: #606266;
}

.advanced-filters :deep(.el-form-item) {
  margin-bottom: 12px;
}

.advanced-filters :deep(.el-form-item__label) {
  font-size: 12px;
  line-height: 1.2;
}

/* 教师卡片样式增强 */
.teacher-card .style-tag,
.teacher-card .grade-tag,
.teacher-card .training-tag,
.teacher-card .course-tag {
  margin-right: 4px;
  margin-bottom: 2px;
}

.teacher-card .more-tags {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.teacher-card .info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.teacher-card .info-row .info-item {
  flex: 1;
  margin-right: 12px;
}

.teacher-card .info-row .info-item:last-child {
  margin-right: 0;
}

.teacher-card .info-item {
  margin-bottom: 6px;
  font-size: 13px;
}

.teacher-card .info-item .label {
  color: #606266;
  margin-right: 4px;
  font-weight: 500;
}

/* 年级快速选择按钮样式 */
.grade-quick-select {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.grade-quick-select .el-button {
  font-size: 12px;
  padding: 2px 6px;
  height: auto;
  min-height: 20px;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.2;
}
</style>
