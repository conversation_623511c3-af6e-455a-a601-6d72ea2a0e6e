<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`编辑时间段 - ${teacher?.name || ''}`"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="teacher" class="time-slot-edit-container">
      <!-- 教师基本信息 -->
      <el-card class="teacher-info-card">
        <div class="teacher-info">
          <div class="info-item">
            <label>教师姓名：</label>
            <span>{{ teacher.name }}</span>
          </div>
          <div class="info-item">
            <label>暑期课上课时间：</label>
            <el-tag :type="getSummerScheduleTagType(teacher.summerScheduleType)" size="small">
              {{ getSummerScheduleText(teacher.summerScheduleType) }}
            </el-tag>
          </div>
          <div v-if="lastUpdateInfo" class="info-item">
            <label>最后更新时间：</label>
            <span class="update-time">
              {{ formatUpdateTime(lastUpdateInfo.lastUpdateTime) }}
              <span v-if="lastUpdateInfo.daysSinceLastUpdate !== null" class="days-ago">
                （{{ lastUpdateInfo.daysSinceLastUpdate }}天前）
              </span>
            </span>
          </div>
        </div>
      </el-card>

      <!-- 权限提示 -->
      <div v-if="!canEdit" class="permission-notice">
        <el-alert
          title="权限提示"
          description="您没有权限编辑该教师的时间段"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 高级时间段编辑器 -->
      <div class="time-slot-editor">
        <AdvancedTimeSlotEditor
          v-model="timeSlots"
          :readonly="!isEditing || !canEdit"
          @change="handleTimeSlotsChange"
        >
          <!-- 传递操作按钮到编辑器工具栏 -->
          <template #actions>
            <el-button
              size="small"
              type="primary"
              @click="handleEdit"
              :disabled="!canEdit"
              v-if="!isEditing"
            >
              <el-icon><Edit /></el-icon>
              编辑时间表
            </el-button>
            <el-button
              size="small"
              @click="handleReset"
              v-if="isEditing"
            >
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </template>
        </AdvancedTimeSlotEditor>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          v-if="isEditing && canEdit" 
          @click="handleCancelEdit"
        >
          取消编辑
        </el-button>
        <el-button 
          v-if="isEditing && canEdit" 
          type="primary" 
          @click="handleSave"
          :loading="saving"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Refresh } from '@element-plus/icons-vue'
import { useTeachingGroupStore } from '@/stores/teachingGroup'
import { checkRole, checkPermi } from '@/utils/permission'
import useUserStore from '@/store/modules/user'
import AdvancedTimeSlotEditor from './AdvancedTimeSlotEditor.vue'
import { checkTeacherTimeSlotUpdateTimeApi } from '@/api/management/teachingGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// Store
const teachingGroupStore = useTeachingGroupStore()
const userStore = useUserStore()

// 响应式数据
const timeSlots = ref([])
const originalTimeSlots = ref([])
const isEditing = ref(false)
const saving = ref(false)
const lastUpdateInfo = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 权限检查
const canEdit = computed(() => {
  const teacherId = props.teacher?.id || props.teacher?.userId || props.teacher?.teacherId
  if (!teacherId) return false

  // 管理员和HR有权限编辑所有教师的时间段
  if (checkRole(['admin', 'hr'])) {
    return true
  }

  // 教师只能编辑自己的时间段
  if (checkRole(['teacher'])) {
    return teacherId === userStore.id
  }

  // 教学组长和教务可以编辑所属教学组内教师的时间段
  if (checkRole(['teaching_group_leader', 'teaching_group_admin'])) {
    // 这里需要检查教师是否在当前用户管理的教学组中
    // 暂时返回true，实际应该调用API检查
    return true
  }

  return false
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.teacher) {
    fetchTimeSlots()
  }
})

watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 工具函数
// 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
const normalizeTimeFormat = (timeStr) => {
  if (!timeStr || typeof timeStr !== 'string') {
    return timeStr
  }

  const parts = timeStr.split(':')
  if (parts.length >= 2) {
    // 只取小时和分钟部分，忽略秒
    const hours = parts[0].padStart(2, '0')
    const minutes = parts[1].padStart(2, '0')
    return `${hours}:${minutes}`
  }

  return timeStr
}

// 方法
const fetchTimeSlots = async () => {
  // 检查教师ID，支持多种可能的字段名
  const teacherId = props.teacher?.id || props.teacher?.userId || props.teacher?.teacherId

  if (!teacherId) {
    console.warn('Teacher ID is missing:', props.teacher)
    ElMessage.warning('教师ID缺失，无法获取时间表')
    // 设置空数组避免undefined导致的问题
    timeSlots.value = []
    originalTimeSlots.value = []
    return
  }

  try {
    // 调用API获取教师时间表
    const response = await teachingGroupStore.fetchTeacherTimeSlots(teacherId)

    // 确保响应数据是数组格式，并验证数据完整性
    const validTimeSlots = Array.isArray(response) ? response.filter(slot => {
      return slot &&
             typeof slot.weekday === 'number' &&
             typeof slot.startTime === 'string' &&
             typeof slot.endTime === 'string' &&
             slot.startTime.includes(':') &&
             slot.endTime.includes(':')
    }).map(slot => ({
      ...slot,
      // 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
      startTime: normalizeTimeFormat(slot.startTime),
      endTime: normalizeTimeFormat(slot.endTime)
    })) : []

    timeSlots.value = validTimeSlots
    originalTimeSlots.value = JSON.parse(JSON.stringify(validTimeSlots))

    console.log('获取教师时间表成功:', { teacherId, count: validTimeSlots.length })

    // 获取更新时间信息
    await fetchUpdateTimeInfo()
  } catch (error) {
    console.error('获取教师时间表失败:', error)
    ElMessage.error('获取教师时间表失败')
    timeSlots.value = []
    originalTimeSlots.value = []
  }
}

// 获取时间表更新时间信息
const fetchUpdateTimeInfo = async () => {
  const teacherId = props.teacher?.id || props.teacher?.userId || props.teacher?.teacherId
  if (!teacherId) return

  try {
    const response = await checkTeacherTimeSlotUpdateTimeApi(teacherId)
    if (response.code === 200) {
      lastUpdateInfo.value = response.data
    }
  } catch (error) {
    console.error('获取时间表更新时间失败:', error)
    // 不显示错误信息，静默失败
  }
}

const handleEdit = () => {
  if (!canEdit.value) {
    ElMessage.warning('您没有权限编辑该教师的时间段')
    return
  }
  isEditing.value = true
}

const handleCancelEdit = () => {
  isEditing.value = false
  timeSlots.value = JSON.parse(JSON.stringify(originalTimeSlots.value))
}

const handleTimeSlotsChange = (newTimeSlots) => {
  // 时间段变化时的处理
  console.log('时间段变化:', newTimeSlots)
}

const handleReset = () => {
  if (isEditing.value) {
    handleCancelEdit()
  } else {
    fetchTimeSlots()
  }
}

// 验证时间段数据
const validateTimeSlots = (slots) => {
  for (let i = 0; i < slots.length; i++) {
    const slot = slots[i]

    // 检查必填字段
    if (!slot.startTime || !slot.endTime) {
      return { valid: false, message: `第${i + 1}个时间段的开始时间或结束时间为空` }
    }

    // 检查时间格式
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    if (!timeRegex.test(slot.startTime) || !timeRegex.test(slot.endTime)) {
      return { valid: false, message: `第${i + 1}个时间段的时间格式不正确` }
    }

    // 检查时间逻辑
    if (slot.startTime >= slot.endTime) {
      return { valid: false, message: `第${i + 1}个时间段的开始时间必须小于结束时间` }
    }

    // 检查是否有无效的00:00结束时间
    if (slot.endTime === '00:00') {
      return { valid: false, message: `第${i + 1}个时间段的结束时间不能为00:00，请选择有效的结束时间` }
    }
  }

  return { valid: true }
}

const handleSave = async () => {
  if (!canEdit.value) {
    ElMessage.warning('您没有权限编辑该教师的时间段')
    return
  }

  const teacherId = props.teacher?.id || props.teacher?.userId || props.teacher?.teacherId

  if (!teacherId) {
    ElMessage.error('教师ID缺失，无法保存')
    return
  }

  // 验证时间段数据
  const validation = validateTimeSlots(timeSlots.value)
  if (!validation.valid) {
    ElMessage.error(validation.message)
    return
  }

  try {
    saving.value = true

    // 保存时间段数据
    const success = await teachingGroupStore.updateTeacherTimeSlots({
      teacherId: teacherId,
      timeSlots: timeSlots.value
    })

    if (success) {
      originalTimeSlots.value = JSON.parse(JSON.stringify(timeSlots.value))
      isEditing.value = false
      ElMessage.success('保存成功')
      emit('success')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 格式化更新时间
const formatUpdateTime = (updateTime) => {
  if (!updateTime) return '未知'

  try {
    const date = new Date(updateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '格式错误'
  }
}

const handleClose = () => {
  if (isEditing.value) {
    handleCancelEdit()
  }
  dialogVisible.value = false
}

const getSummerScheduleText = (type) => {
  const typeMap = {
    'full': '全满档',
    'golden': '黄金档',
    'other': '其他档'
  }
  return typeMap[type] || '其他档'
}

const getSummerScheduleTagType = (type) => {
  const typeMap = {
    'full': 'danger',
    'golden': 'warning',
    'other': 'info'
  }
  return typeMap[type] || 'info'
}

// 生命周期
onMounted(() => {
  if (props.modelValue && props.teacher) {
    fetchTimeSlots()
  }
})
</script>

<style lang="scss" scoped>
.time-slot-edit-container {
  .teacher-info-card {
    margin-bottom: 16px;

    .teacher-info {
      display: flex;
      gap: 24px;
      align-items: center;

      .info-item {
        display: flex;
        align-items: center;
        gap: 8px;

        label {
          font-weight: 500;
          color: #606266;
        }

        .update-time {
          color: #909399;
          font-size: 14px;

          .days-ago {
            color: #C0C4CC;
            font-size: 12px;
          }
        }
      }
    }
  }

  .permission-notice {
    margin-bottom: 12px;
  }

  .time-slot-editor {
    min-height: 300px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
