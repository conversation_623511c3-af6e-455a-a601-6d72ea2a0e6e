<template>
  <div class="precise-time-slot-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <span class="title">可上课时间设置</span>
        <span class="subtitle">拖拽选择时间段（5分钟精度），点击删除</span>
      </div>
      <div class="toolbar-right">
        <el-button size="small" @click="clearAll" :disabled="readonly">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
        <el-button size="small" type="primary" @click="applyTemplate" :disabled="readonly">
          <el-icon><Clock /></el-icon>
          模板
        </el-button>
      </div>
    </div>

    <!-- 时间网格 -->
    <div class="time-grid-container" ref="gridContainer">
      <!-- 表头 -->
      <div class="grid-header">
        <div class="time-column-header">时间</div>
        <div
          v-for="day in weekDays"
          :key="day.value"
          class="day-header"
        >
          <div class="day-name">{{ day.label }}</div>
          <div class="day-date">{{ getDayDate(day.value) }}</div>
        </div>
      </div>

      <!-- 时间网格主体 -->
      <div 
        class="grid-body" 
        @mouseup="endSelection" 
        @mouseleave="endSelection"
        @selectstart="preventDefault"
      >
        <div
          v-for="hour in displayHours"
          :key="hour"
          class="hour-row"
        >
          <!-- 时间标签 -->
          <div class="time-label">
            {{ formatHour(hour) }}
          </div>

          <!-- 每天的时间格子 -->
          <div
            v-for="day in weekDays"
            :key="`${day.value}-${hour}`"
            class="day-column"
          >
            <!-- 5分钟精度的时间格子 -->
            <div
              v-for="minute in minuteSlots"
              :key="`${day.value}-${hour}-${minute}`"
              class="time-cell"
              :class="getCellClass(day.value, hour, minute)"
              :data-weekday="day.value"
              :data-hour="hour"
              :data-minute="minute"
              @mousedown="startSelection($event, day.value, hour, minute)"
              @mouseenter="updateSelection(day.value, hour, minute)"
              @click="toggleCell(day.value, hour, minute)"
            >
              <!-- 时间段标签（只在开始位置显示） -->
              <div 
                v-if="isSlotStart(day.value, hour, minute)" 
                class="slot-label"
                :title="getSlotLabel(day.value, hour, minute)"
              >
                {{ getSlotLabel(day.value, hour, minute) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中时间段信息 -->
    <div v-if="selectedSlots.length > 0" class="selected-info">
      <div class="info-header">
        <span>已选择 {{ selectedSlots.length }} 个时间段</span>
        <el-button size="small" @click="selectedSlots = []">清除选择</el-button>
      </div>
      <div class="slots-preview">
        <div
          v-for="slot in sortedSlots"
          :key="slot.id"
          class="slot-preview"
        >
          <span class="slot-day">{{ getWeekDayName(slot.weekday) }}</span>
          <span class="slot-time">{{ slot.startTime }} - {{ slot.endTime }}</span>
          <el-button
            type="danger"
            link
            size="small"
            @click="removeSlot(slot)"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      title="选择时间模板"
      width="500px"
    >
      <div class="template-list">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-item"
          @click="applySelectedTemplate(template)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-description">{{ template.description }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Clock, Close } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  },
  startHour: {
    type: Number,
    default: 6
  },
  endHour: {
    type: Number,
    default: 23
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const selectedSlots = ref([])
const showTemplateDialog = ref(false)
const isSelecting = ref(false)
const selectionStart = ref(null)
const selectionEnd = ref(null)
const currentWeekday = ref(null)
const gridContainer = ref()

// 常量
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 5分钟间隔
const minuteSlots = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]

// 预设模板
const templates = [
  {
    id: 'workday',
    name: '工作日模板',
    description: '周一至周五，9:00-12:00，14:00-18:00',
    slots: generateTemplateSlots([1, 2, 3, 4, 5], [
      { start: '09:00', end: '12:00' },
      { start: '14:00', end: '18:00' }
    ])
  },
  {
    id: 'weekend',
    name: '周末模板',
    description: '周六周日，9:00-17:00',
    slots: generateTemplateSlots([6, 7], [  // 6=周六, 7=周日 (Java格式)
      { start: '09:00', end: '17:00' }
    ])
  },
  {
    id: 'evening',
    name: '晚间模板',
    description: '周一至周五，18:30-21:30',
    slots: generateTemplateSlots([1, 2, 3, 4, 5], [
      { start: '18:30', end: '21:30' }
    ])
  }
]

// 计算属性
const displayHours = computed(() => {
  return Array.from({ length: props.endHour - props.startHour + 1 }, (_, i) => i + props.startHour)
})

const sortedSlots = computed(() => {
  return [...selectedSlots.value].sort((a, b) => {
    if (a.weekday !== b.weekday) {
      return a.weekday - b.weekday
    }
    return a.startTime.localeCompare(b.startTime)
  })
})

// 工具函数
// 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
function normalizeTimeFormat(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return timeStr
  }

  const parts = timeStr.split(':')
  if (parts.length >= 2) {
    // 只取小时和分钟部分，忽略秒
    const hours = parts[0].padStart(2, '0')
    const minutes = parts[1].padStart(2, '0')
    return `${hours}:${minutes}`
  }

  return timeStr
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  // 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
  const normalizedSlots = (newValue || []).map(slot => ({
    ...slot,
    startTime: normalizeTimeFormat(slot.startTime),
    endTime: normalizeTimeFormat(slot.endTime)
  }))
  selectedSlots.value = normalizedSlots
}, { immediate: true, deep: true })

watch(selectedSlots, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
}, { deep: true })

// 方法
function formatHour(hour) {
  return `${hour.toString().padStart(2, '0')}:00`
}

function getDayDate(weekday) {
  const today = new Date()
  const currentDay = today.getDay()
  const targetDay = weekday === 0 ? 7 : weekday
  const currentDayAdjusted = currentDay === 0 ? 7 : currentDay

  const diff = targetDay - currentDayAdjusted
  const targetDate = new Date(today)
  targetDate.setDate(today.getDate() + diff)

  return `${targetDate.getMonth() + 1}/${targetDate.getDate()}`
}

function getWeekDayName(weekday) {
  const day = weekDays.find(d => d.value === weekday)
  return day ? day.label : '未知'
}

function generateTemplateSlots(weekdays, timeRanges) {
  const slots = []
  weekdays.forEach(weekday => {
    timeRanges.forEach(range => {
      slots.push({
        id: `template_${weekday}_${range.start}_${range.end}`,
        weekday,
        startTime: range.start,
        endTime: range.end,
        status: 'available'
      })
    })
  })
  return slots
}

function preventDefault(event) {
  event.preventDefault()
}

function getCellClass(weekday, hour, minute) {
  const classes = []

  if (props.readonly) {
    classes.push('readonly')
  }

  if (isCellSelected(weekday, hour, minute)) {
    classes.push('selected')
  }

  if (isSelecting.value && currentWeekday.value === weekday) {
    if (isInSelectionRange(hour, minute)) {
      classes.push('selecting')
    }
  }

  return classes
}

function isCellSelected(weekday, hour, minute) {
  return selectedSlots.value.some(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const cellTime = hour * 60 + minute

    return cellTime >= slotStart && cellTime < slotEnd
  })
}

function timeToMinutes(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number)
  return hours * 60 + minutes
}

function minutesToTime(minutes) {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

function isInSelectionRange(hour, minute) {
  if (!selectionStart.value || !selectionEnd.value) return false

  const startTime = selectionStart.value.hour * 60 + selectionStart.value.minute
  const endTime = selectionEnd.value.hour * 60 + selectionEnd.value.minute
  const currentTime = hour * 60 + minute

  const minTime = Math.min(startTime, endTime)
  const maxTime = Math.max(startTime, endTime)

  return currentTime >= minTime && currentTime <= maxTime
}

function startSelection(event, weekday, hour, minute) {
  if (props.readonly) return

  event.preventDefault()
  isSelecting.value = true
  currentWeekday.value = weekday
  selectionStart.value = { hour, minute }
  selectionEnd.value = { hour, minute }

  // 添加全局事件监听
  document.addEventListener('mouseup', endSelection)
}

function updateSelection(weekday, hour, minute) {
  if (!isSelecting.value || currentWeekday.value !== weekday) return

  selectionEnd.value = { hour, minute }
}

function endSelection() {
  if (!isSelecting.value) return

  if (selectionStart.value && selectionEnd.value) {
    createTimeSlot()
  }

  // 重置选择状态
  isSelecting.value = false
  selectionStart.value = null
  selectionEnd.value = null
  currentWeekday.value = null

  // 移除全局事件监听
  document.removeEventListener('mouseup', endSelection)
}

function createTimeSlot() {
  const weekday = currentWeekday.value
  const startTime = selectionStart.value.hour * 60 + selectionStart.value.minute
  const endTime = selectionEnd.value.hour * 60 + selectionEnd.value.minute

  const minTime = Math.min(startTime, endTime)
  const maxTime = Math.max(startTime, endTime) + 5 // 包含结束时间段（5分钟）

  const newSlot = {
    id: `slot_${Date.now()}`,
    weekday,
    startTime: minutesToTime(minTime),
    endTime: minutesToTime(maxTime),
    status: 'available'
  }

  // 合并重叠的时间段
  mergeTimeSlots(newSlot)
}

function mergeTimeSlots(newSlot) {
  // 移除与新时间段重叠的现有时间段
  const nonOverlappingSlots = selectedSlots.value.filter(slot => {
    if (slot.weekday !== newSlot.weekday) return true

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const newStart = timeToMinutes(newSlot.startTime)
    const newEnd = timeToMinutes(newSlot.endTime)

    // 检查是否重叠
    return !(slotStart < newEnd && newStart < slotEnd)
  })

  // 添加新时间段
  nonOverlappingSlots.push(newSlot)

  // 合并相邻的时间段
  const sameDaySlots = nonOverlappingSlots.filter(slot => slot.weekday === newSlot.weekday)
  const otherDaySlots = nonOverlappingSlots.filter(slot => slot.weekday !== newSlot.weekday)

  // 按开始时间排序
  sameDaySlots.sort((a, b) => timeToMinutes(a.startTime) - timeToMinutes(b.startTime))

  // 合并相邻时间段
  const merged = []
  for (const slot of sameDaySlots) {
    if (merged.length === 0) {
      merged.push(slot)
    } else {
      const lastSlot = merged[merged.length - 1]
      const lastEnd = timeToMinutes(lastSlot.endTime)
      const currentStart = timeToMinutes(slot.startTime)

      if (lastEnd >= currentStart) {
        // 合并时间段
        const currentEnd = timeToMinutes(slot.endTime)
        lastSlot.endTime = minutesToTime(Math.max(lastEnd, currentEnd))
        lastSlot.id = `slot_${Date.now()}_merged`
      } else {
        merged.push(slot)
      }
    }
  }

  selectedSlots.value = [...otherDaySlots, ...merged]
}

function toggleCell(weekday, hour, minute) {
  if (props.readonly || isSelecting.value) return

  if (isCellSelected(weekday, hour, minute)) {
    // 删除包含此单元格的时间段
    removeTimeSlotAt(weekday, hour, minute)
  }
}

function removeTimeSlotAt(weekday, hour, minute) {
  const cellTime = hour * 60 + minute

  selectedSlots.value = selectedSlots.value.filter(slot => {
    if (slot.weekday !== weekday) return true

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)

    return !(cellTime >= slotStart && cellTime < slotEnd)
  })
}

function isSlotStart(weekday, hour, minute) {
  return selectedSlots.value.some(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const cellTime = hour * 60 + minute

    return cellTime === slotStart
  })
}

function getSlotLabel(weekday, hour, minute) {
  const slot = selectedSlots.value.find(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const cellTime = hour * 60 + minute

    return cellTime === slotStart
  })

  return slot ? `${slot.startTime}-${slot.endTime}` : ''
}

function removeSlot(slot) {
  const index = selectedSlots.value.findIndex(s => s.id === slot.id)
  if (index >= 0) {
    selectedSlots.value.splice(index, 1)
  }
}

function clearAll() {
  if (selectedSlots.value.length === 0) {
    ElMessage.info('没有时间段需要清空')
    return
  }

  ElMessageBox.confirm(
    '确定要清空所有时间段吗？',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    selectedSlots.value = []
    ElMessage.success('已清空所有时间段')
  }).catch(() => {
    // 用户取消
  })
}

function applyTemplate() {
  showTemplateDialog.value = true
}

function applySelectedTemplate(template) {
  selectedSlots.value = template.slots.map((slot, index) => ({
    ...slot,
    id: `template_${template.id}_${index}`
  }))

  showTemplateDialog.value = false
  ElMessage.success(`已应用${template.name}`)
}
</script>

<style lang="scss" scoped>
.precise-time-slot-editor {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;

    .toolbar-left {
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .subtitle {
        font-size: 12px;
        color: #909399;
        margin-left: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .time-grid-container {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
    user-select: none;

    .grid-header {
      display: flex;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;

      .time-column-header {
        width: 80px;
        padding: 12px 8px;
        border-right: 1px solid #e4e7ed;
        font-weight: 600;
        text-align: center;
      }

      .day-header {
        flex: 1;
        padding: 8px;
        text-align: center;
        border-right: 1px solid #e4e7ed;

        &:last-child {
          border-right: none;
        }

        .day-name {
          font-weight: 600;
          color: #303133;
          margin-bottom: 2px;
        }

        .day-date {
          font-size: 11px;
          color: #909399;
        }
      }
    }

    .grid-body {
      max-height: 600px;
      overflow-y: auto;

      .hour-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .time-label {
          width: 80px;
          padding: 8px;
          border-right: 1px solid #e4e7ed;
          font-size: 12px;
          color: #909399;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #fafbfc;
        }

        .day-column {
          flex: 1;
          display: flex;
          flex-direction: column;
          border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }

          .time-cell {
            height: 5px;
            position: relative;
            cursor: pointer;
            transition: all 0.1s ease;
            border-bottom: 1px solid rgba(240, 240, 240, 0.5);

            &:hover:not(.readonly):not(.selected) {
              background-color: #e1f3d8;
              height: 8px;
            }

            &.selected {
              background-color: #67c23a;
              
              &:hover {
                background-color: #5daf34;
              }
            }

            &.selecting {
              background-color: #a0cfff;
              animation: pulse 0.6s infinite;
            }

            &.readonly {
              cursor: not-allowed;
              opacity: 0.6;
            }

            .slot-label {
              position: absolute;
              top: -2px;
              left: 2px;
              right: 2px;
              font-size: 9px;
              color: #fff;
              background-color: rgba(0, 0, 0, 0.7);
              padding: 1px 2px;
              border-radius: 2px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              z-index: 10;
              line-height: 1;
            }
          }
        }
      }
    }
  }

  .selected-info {
    margin-top: 16px;
    padding: 12px 16px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;

    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-weight: 500;
      color: #409eff;
    }

    .slots-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .slot-preview {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 8px;
        background-color: #fff;
        border: 1px solid #d9ecff;
        border-radius: 4px;
        font-size: 12px;

        .slot-day {
          color: #606266;
          font-weight: 500;
        }

        .slot-time {
          color: #303133;
        }
      }
    }
  }

  .template-list {
    .template-item {
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      .template-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .template-description {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
</style>
