<template>
  <div class="weekly-time-slot-view">
    <div class="week-header">
      <div class="time-column"></div>
      <div
        v-for="day in weekDays"
        :key="day.value"
        class="day-column"
      >
        <div class="day-name">{{ day.label }}</div>
        <div class="day-date">{{ getDayDate(day.value) }}</div>
      </div>
    </div>

    <div class="week-body">
      <div
        v-for="hour in displayHours"
        :key="hour"
        class="time-row"
      >
        <div class="time-label">
          {{ formatHour(hour) }}
        </div>
        
        <div
          v-for="day in weekDays"
          :key="`${day.value}-${hour}`"
          class="time-cell"
        >
          <div
            v-for="slot in getTimeSlotsForHour(day.value, hour)"
            :key="slot.id"
            class="time-slot"
            :class="`status-${slot.status}`"
            :style="getSlotStyle(slot, hour)"
          >
            <div class="slot-content">
              <div class="slot-time">{{ slot.startTime }}-{{ slot.endTime }}</div>
              <div v-if="slot.remark" class="slot-remark">{{ slot.remark }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  timeSlots: {
    type: Array,
    default: () => []
  },
  startHour: {
    type: Number,
    default: 6
  },
  endHour: {
    type: Number,
    default: 23
  }
})

// 常量
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 计算属性
const displayHours = computed(() => {
  return Array.from({ length: props.endHour - props.startHour + 1 }, (_, i) => i + props.startHour)
})

// 方法
function formatHour(hour) {
  return `${hour.toString().padStart(2, '0')}:00`
}

function getDayDate(weekday) {
  const today = new Date()
  const currentDay = today.getDay()
  // 现在weekday使用1-7格式，需要转换为JavaScript的0-6格式进行计算
  const targetDay = weekday === 7 ? 0 : weekday
  const currentDayAdjusted = currentDay

  const diff = targetDay - currentDayAdjusted
  const targetDate = new Date(today)
  targetDate.setDate(today.getDate() + diff)

  return `${targetDate.getMonth() + 1}/${targetDate.getDate()}`
}

function getTimeSlotsForHour(weekday, hour) {
  return props.timeSlots.filter(slot => {
    if (slot.weekday !== weekday) return false
    
    const startHour = parseInt(slot.startTime.split(':')[0])
    const endHour = parseInt(slot.endTime.split(':')[0])
    const endMinute = parseInt(slot.endTime.split(':')[1])
    
    // 如果结束时间的分钟数为0，则不包含该小时
    const actualEndHour = endMinute === 0 ? endHour - 1 : endHour
    
    return hour >= startHour && hour <= actualEndHour
  })
}

function getSlotStyle(slot, currentHour) {
  const startTime = slot.startTime.split(':')
  const endTime = slot.endTime.split(':')
  const startHour = parseInt(startTime[0])
  const startMinute = parseInt(startTime[1])
  const endHour = parseInt(endTime[0])
  const endMinute = parseInt(endTime[1])
  
  // 计算在当前小时内的位置和高度
  let top = 0
  let height = 60 // 默认一小时的高度
  
  if (currentHour === startHour) {
    // 开始小时
    top = (startMinute / 60) * 60
    if (currentHour === endHour) {
      // 开始和结束在同一小时
      height = ((endMinute - startMinute) / 60) * 60
    } else {
      // 从开始分钟到小时结束
      height = ((60 - startMinute) / 60) * 60
    }
  } else if (currentHour === endHour && endMinute > 0) {
    // 结束小时
    top = 0
    height = (endMinute / 60) * 60
  }
  
  return {
    top: `${top}px`,
    height: `${height}px`
  }
}
</script>

<style lang="scss" scoped>
.weekly-time-slot-view {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;

  .week-header {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;

    .time-column {
      width: 80px;
      padding: 12px 8px;
      border-right: 1px solid #e4e7ed;
    }

    .day-column {
      flex: 1;
      padding: 12px 8px;
      text-align: center;
      border-right: 1px solid #e4e7ed;

      &:last-child {
        border-right: none;
      }

      .day-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .day-date {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .week-body {
    .time-row {
      display: flex;
      border-bottom: 1px solid #f0f0f0;
      height: 60px;

      &:last-child {
        border-bottom: none;
      }

      .time-label {
        width: 80px;
        padding: 8px;
        border-right: 1px solid #e4e7ed;
        font-size: 12px;
        color: #909399;
        display: flex;
        align-items: flex-start;
        justify-content: center;
      }

      .time-cell {
        flex: 1;
        border-right: 1px solid #e4e7ed;
        position: relative;

        &:last-child {
          border-right: none;
        }

        .time-slot {
          position: absolute;
          left: 2px;
          right: 2px;
          border-radius: 4px;
          padding: 2px 6px;
          font-size: 11px;
          line-height: 1.2;
          overflow: hidden;

          &.status-available {
            background-color: #e1f3d8;
            border: 1px solid #67c23a;
            color: #67c23a;
          }

          &.status-scheduled {
            background-color: #fdf6ec;
            border: 1px solid #e6a23c;
            color: #e6a23c;
          }

          .slot-content {
            .slot-time {
              font-weight: 500;
              white-space: nowrap;
            }

            .slot-remark {
              font-size: 10px;
              opacity: 0.8;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
</style>
