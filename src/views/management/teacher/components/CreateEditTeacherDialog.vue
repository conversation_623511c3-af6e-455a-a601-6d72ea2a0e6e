<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑教师' : '新增教师'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
      v-loading="loading"
      @submit.prevent
    >
      <!-- 基本信息 -->
      <el-card class="form-section">
        <template #header>
          <span class="section-title">基本信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入姓名"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickname">
              <el-input
                v-model="formData.nickname"
                placeholder="请输入昵称（必填）"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select
                v-model="formData.gender"
                placeholder="请选择性别"
                style="width: 100%"
              >
                <el-option label="男" value="0" />
                <el-option label="女" value="1" />
                <el-option label="未知" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄" prop="age">
              <el-input-number
                v-model="formData.age"
                :min="18"
                :max="70"
                placeholder="请输入年龄"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="formData.phone"
                placeholder="请输入手机号码"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="formData.email"
                placeholder="请输入邮箱地址（可选）"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="目前所在地" prop="currentLocation">
              <el-input
                v-model="formData.currentLocation"
                placeholder="请输入目前所在地"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作性质" prop="employmentType">
              <el-select
                v-model="formData.employmentType"
                placeholder="请选择工作性质"
                style="width: 100%"
              >
                <el-option label="全职" value="full_time" />
                <el-option label="意向全职" value="intended_full_time" />
                <el-option label="兼职" value="part_time" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="目前状态" prop="currentStatus">
              <el-input
                v-model="formData.currentStatus"
                placeholder="请输入目前状态（如：上班族、学生、居家办公等）"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="教学组" prop="groupId">
              <el-select
                v-model="formData.groupId"
                placeholder="请选择教学组"
                style="width: 100%"
              >
                <el-option
                  v-for="group in teachingGroups"
                  :key="group.id"
                  :label="group.name"
                  :value="group.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="正式入职时间" prop="formalEntryDate">
              <el-date-picker
                v-model="formData.formalEntryDate"
                type="date"
                placeholder="请选择正式入职时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 教育背景 -->
      <el-card class="form-section">
        <template #header>
          <span class="section-title">教育背景</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="学历" prop="education">
              <el-select
                v-model="formData.education"
                placeholder="请选择学历"
                style="width: 100%"
              >
                <el-option label="本科" value="本科" />
                <el-option label="硕士" value="硕士" />
                <el-option label="博士" value="博士" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="毕业院校" prop="graduateSchool">
              <el-input
                v-model="formData.graduateSchool"
                placeholder="请输入毕业院校"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专业" prop="major">
              <el-input
                v-model="formData.major"
                placeholder="请输入专业"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="大学属性" prop="universityType">
              <el-select
                v-model="formData.universityType"
                placeholder="请选择大学属性"
                style="width: 100%"
              >
                <el-option label="双一流" value="双一流" />
                <el-option label="985" value="985" />
                <el-option label="211" value="211" />
                <el-option label="一本" value="一本" />
                <el-option label="普通" value="普通" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="师范院校" prop="isNormalUniversity">
              <el-switch
                v-model="formData.isNormalUniversity"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否留学" prop="studyAbroad">
              <el-switch
                v-model="formData.studyAbroad"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="formData.studyAbroad">
          <el-col :span="12">
            <el-form-item label="留学国家" prop="studyAbroadCountry">
              <el-input
                v-model="formData.studyAbroadCountry"
                placeholder="请输入留学国家"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 教学资质 -->
      <el-card class="form-section">
        <template #header>
          <span class="section-title">教学资质</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="教资级别" prop="teachingCertificateLevel">
              <el-select
                v-model="formData.teachingCertificateLevel"
                placeholder="请选择教资级别"
                style="width: 100%"
              >
                <el-option label="小学" value="小学" />
                <el-option label="初中" value="初中" />
                <el-option label="高中" value="高中" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="教龄" prop="teachingYears">
              <el-input-number
                v-model="formData.teachingYears"
                :min="0"
                :max="50"
                placeholder="请输入教龄"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="英语资质" prop="englishQualification">
              <el-select
                v-model="formData.englishQualification"
                placeholder="请选择英语资质"
                style="width: 100%"
              >
                <el-option label="四级" value="四级" />
                <el-option label="六级" value="六级" />
                <el-option label="专四" value="专四" />
                <el-option label="专八" value="专八" />
                <el-option label="雅思" value="雅思" />
                <el-option label="托福" value="托福" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="普通话资质" prop="mandarinQualification">
              <el-select
                v-model="formData.mandarinQualification"
                placeholder="请选择普通话资质"
                style="width: 100%"
              >
                <el-option label="一级甲等" value="一级甲等" />
                <el-option label="一级乙等" value="一级乙等" />
                <el-option label="二级甲等" value="二级甲等" />
                <el-option label="二级乙等" value="二级乙等" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="沟通能力" prop="communicationAbility">
              <el-select
                v-model="formData.communicationAbility"
                placeholder="请选择沟通能力"
                style="width: 100%"
              >
                <el-option label="优秀" value="优秀" />
                <el-option label="良好" value="良好" />
                <el-option label="一般" value="一般" />
                <el-option label="薄弱" value="薄弱" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英语发音" prop="englishPronunciation">
              <el-select
                v-model="formData.englishPronunciation"
                placeholder="请选择英语发音水平"
                style="width: 100%"
              >
                <el-option label="优秀（母语水平）" value="优秀（母语水平）" />
                        <el-option label="良好" value="良好" />
                        <el-option label="正常" value="正常" />
                        <el-option label="一般" value="一般" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="教授学科" prop="subjects">
          <el-select
            v-model="formData.subjects"
            multiple
            placeholder="请选择教授学科"
            style="width: 100%"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 其他信息 -->
      <el-card class="form-section">
        <template #header>
          <span class="section-title">其他信息</span>
        </template>

        <el-form-item label="教过课程" prop="taughtCourses">
          <el-select
            v-model="formData.taughtCourses"
            multiple
            placeholder="请选择教过的课程"
            style="width: 100%"
          >
            <el-option label="音标课" value="音标课" />
            <el-option label="语法课" value="语法课" />
            <el-option label="阅读课" value="阅读课" />
            <el-option label="写作课" value="写作课" />
            <el-option label="口语课" value="口语课" />
            <el-option label="听力课" value="听力课" />
            <el-option label="高考课" value="高考课" />
            <el-option label="中考课" value="中考课" />
            <el-option label="小升初课" value="小升初课" />
          </el-select>
        </el-form-item>

        <el-form-item label="上课风格" prop="teachingStyle">
          <el-select
            v-model="formData.teachingStyle"
            multiple
            placeholder="请选择上课风格"
            style="width: 100%"
          >
            <el-option label="温柔" value="温柔" />
            <el-option label="亲切" value="亲切" />
            <el-option label="幽默" value="幽默" />
            <el-option label="严肃" value="严肃" />
            <el-option label="活泼" value="活泼" />
            <el-option label="耐心" value="耐心" />
            <el-option label="鼓励式" value="鼓励式" />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="适合学生年级" prop="suitableGrades">
              <el-select
                v-model="formData.suitableGrades"
                multiple
                placeholder="请选择适合的学生年级"
                style="width: 100%"
              >
                <el-option label="一年级" value="一年级" />
                <el-option label="二年级" value="二年级" />
                <el-option label="三年级" value="三年级" />
                <el-option label="四年级" value="四年级" />
                <el-option label="五年级" value="五年级" />
                <el-option label="六年级" value="六年级" />
                <el-option label="初一" value="初一" />
                <el-option label="初二" value="初二" />
                <el-option label="初三" value="初三" />
                <el-option label="高一" value="高一" />
                <el-option label="高二" value="高二" />
                <el-option label="高三" value="高三" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适合学生程度" prop="suitableLevels">
              <el-select
                v-model="formData.suitableLevels"
                multiple
                placeholder="请选择适合的学生程度"
                style="width: 100%"
              >
                <el-option label="学霸" value="学霸" />
                <el-option label="中等生" value="中等生" />
                <el-option label="学困生" value="学困生" />
                <el-option label="零基础" value="零基础" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="适合学生性格" prop="suitablePersonality">
          <el-select
            v-model="formData.suitablePersonality"
            placeholder="请选择适合的学生性格"
            style="width: 100%"
          >
            <el-option label="外向活泼" value="外向活泼" />
            <el-option label="内向腼腆" value="内向腼腆" />
            <el-option label="都适合" value="都适合" />
          </el-select>
        </el-form-item>

        <el-form-item label="暑期课上课时间" prop="summerScheduleType">
          <el-select
            v-model="formData.summerScheduleType"
            placeholder="请选择暑期课上课时间"
            style="width: 100%"
          >
            <el-option label="全满档（全天，一周6-7天，均可排课）" value="full" />
            <el-option
              label="黄金档（周一到周五晚上，周末2天，均可排课）"
              value="golden"
            />
            <el-option label="其他档（其他指定时间可排课）" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="教学经验" prop="teachingExperience">
          <el-input
            v-model="formData.teachingExperience"
            type="textarea"
            :rows="3"
            placeholder="请描述教学经验"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="获奖情况" prop="awards">
          <el-input
            v-model="formData.awards"
            type="textarea"
            :rows="3"
            placeholder="请描述获奖情况（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="个人简介" prop="introduction">
          <el-input
            v-model="formData.introduction"
            type="textarea"
            :rows="4"
            placeholder="请输入个人简介"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="其他说明" prop="other">
          <el-input
            v-model="formData.other"
            type="textarea"
            :rows="2"
            placeholder="其他需要说明的信息（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 资质证书上传 -->
        <el-form-item label="资质证书" prop="qualificationCertificates">

          <BatchFileUpload
            ref="certificateUploadRef"
            v-model="formData.qualificationCertificates"
            :upload-url="certificateUploadUrl"
            :limit="5"
            :file-size="10"
            :file-types="['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']"
            tip-text="可同时选择多个资质证书文件，然后一起上传"
          />
        </el-form-item>

        <!-- 示范上课视频上传 -->
        <el-form-item label="示范上课视频" prop="demoVideos">

          <BatchFileUpload
            ref="videoUploadRef"
            v-model="formData.demoVideos"
            :upload-url="videoUploadUrl"
            :limit="3"
            :file-size="100"
            :file-types="['mp4', 'avi', 'mov', 'wmv', 'flv']"
            tip-text="可同时选择多个示范视频文件，然后一起上传"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="在职" value="active" />
                <el-option label="离职" value="inactive" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? "保存" : "创建" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useTeachingGroupStore } from "@/stores/teachingGroup";
import BatchFileUpload from "@/components/BatchFileUpload/index.vue";

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  teacher: {
    type: Object,
    default: null,
  },
  teachingGroups: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "success"]);

// Store
const teachingGroupStore = useTeachingGroupStore();

// 上传URL
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const certificateUploadUrl = `${baseUrl}/word/file/teacher/certificate`;
const videoUploadUrl = `${baseUrl}/word/file/teacher/video`;

// 响应式数据
const formRef = ref();
const certificateUploadRef = ref();
const videoUploadRef = ref();
const submitting = ref(false);
const loading = ref(false);
const teacherDetail = ref(null);

const formData = ref({
  name: "",
  nickname: "",
  phone: "",
  gender: "",
  age: null,
  email: "",
  currentLocation: "",
  employmentType: "",
  currentStatus: "",
  groupId: "",

  // 教育背景
  education: "",
  graduateSchool: "",
  major: "",
  universityType: "",
  isNormalUniversity: false,
  studyAbroad: false,
  studyAbroadCountry: "",

  // 教学资质
  teachingCertificateLevel: "",
  teachingYears: null,
  englishQualification: "",
  mandarinQualification: "",
  communicationAbility: "",
  englishPronunciation: "",
  subjects: [],

  // 教学经历和风格
  taughtCourses: [],
  teachingStyle: [],
  suitableGrades: [],
  suitableLevels: [],
  suitablePersonality: "",

  // 暑期课上课时间
  summerScheduleType: "other",

  teachingExperience: "",
  awards: "",
  introduction: "",
  other: "",
  status: "active",

  // 新增字段
  formalEntryDate: "",
  qualificationCertificates: [],
  demoVideos: [],
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    { min: 2, max: 20, message: "姓名长度在 2 到 20 个字符", trigger: "blur" },
  ],
  nickname: [{ required: true, message: "请输入昵称", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" },
  ],
  gender: [{ required: true, message: "请选择性别", trigger: "change" }],
  email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }],
  groupId: [{ required: false, message: "请选择教学组", trigger: "change" }],
};

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const isEdit = computed(() => {
  return props.teacher && props.teacher.id;
});

// 方法
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  formData.value = {
    name: "",
    nickname: "",
    phone: "",
    gender: "",
    age: null,
    email: "",
    currentLocation: "",
    employmentType: "",
    currentStatus: "",
    groupId: "",

    // 教育背景
    education: "",
    graduateSchool: "",
    major: "",
    universityType: "",
    isNormalUniversity: false,
    studyAbroad: false,
    studyAbroadCountry: "",

    // 教学资质
    teachingCertificateLevel: "",
    teachingYears: null,
    englishQualification: "",
    mandarinQualification: "",
    communicationAbility: "",
    englishPronunciation: "",
    subjects: [],

    // 教学经历和风格
    taughtCourses: [],
    teachingStyle: [],
    suitableGrades: [],
    suitableLevels: [],
    suitablePersonality: "",

    // 暑期课上课时间
    summerScheduleType: "other",

    teachingExperience: "",
    awards: "",
    introduction: "",
    other: "",
    status: "active",

    // 新增字段
    formalEntryDate: "",
    qualificationCertificates: [],
    demoVideos: [],
  };

  // 重置文件上传组件
  nextTick(() => {
    if (certificateUploadRef.value) {
      certificateUploadRef.value.clearAllFiles();
    }
    if (videoUploadRef.value) {
      videoUploadRef.value.clearAllFiles();
    }
  });
};

// 获取教师详细信息
const fetchTeacherDetail = async () => {
  if (!props.teacher?.id) return;

  loading.value = true;
  try {
    // 获取教师详细信息
    const detail = await teachingGroupStore.fetchTeacherDetail(props.teacher.id);
    if (detail) {
      teacherDetail.value = detail;
      // 使用最新的详细信息填充表单
      fillForm(detail);
    }
  } catch (error) {
    console.error("获取教师详细信息失败:", error);
    // 如果获取失败，使用传入的教师信息
    fillForm(props.teacher);
  } finally {
    loading.value = false;
  }
};

// 填充表单数据
const fillForm = (teacher) => {
  if (teacher) {
    console.log('填充表单数据:', teacher);
    console.log('资质证书:', teacher.qualificationCertificates);
    console.log('示范视频:', teacher.demoVideos);

    Object.assign(formData.value, {
      name: teacher.name || "",
      nickname: teacher.nickname || "",
      gender: teacher.gender || "",
      age: teacher.age || null,
      phone: teacher.phone || "",
      email: teacher.email || "",
      currentLocation: teacher.currentLocation || "",
      employmentType: teacher.employmentType || "",
      currentStatus: teacher.currentStatus || "",
      groupId: teacher.groupId || "",

      // 教育背景
      education: teacher.education || "",
      graduateSchool: teacher.graduateSchool || "",
      major: teacher.major || "",
      universityType: teacher.universityType || "",
      isNormalUniversity: teacher.isNormalUniversity || false,
      studyAbroad: teacher.studyAbroad || false,
      studyAbroadCountry: teacher.studyAbroadCountry || "",

      // 教学资质
      teachingCertificateLevel: teacher.teachingCertificateLevel || "",
      teachingYears: teacher.teachingYears || null,
      englishQualification: teacher.englishQualification || "",
      mandarinQualification: teacher.mandarinQualification || "",
      communicationAbility: teacher.communicationAbility || "",
      englishPronunciation: teacher.englishPronunciation || "",
      subjects: teacher.subjects || [],

      // 教学经历和风格
      taughtCourses: teacher.taughtCourses || [],
      teachingStyle: teacher.teachingStyle || [],
      suitableGrades: teacher.suitableGrades || [],
      suitableLevels: teacher.suitableLevels || [],
      suitablePersonality: teacher.suitablePersonality || "",

      // 暑期课上课时间
      summerScheduleType: teacher.summerScheduleType || "other",

      teachingExperience: teacher.teachingExperience || "",
      awards: teacher.awards || "",
      introduction: teacher.introduction || "",
      other: teacher.other || "",
      status: teacher.status || "active",

      // 新增字段
      formalEntryDate: teacher.formalEntryDate || "",
      qualificationCertificates: teacher.qualificationCertificates || [],
      demoVideos: teacher.demoVideos || [],
    });

    console.log('表单数据更新后:', formData.value.qualificationCertificates, formData.value.demoVideos);

    // 确保文件上传组件能够正确显示已有文件
    nextTick(() => {
      console.log('nextTick 中检查组件状态');
      if (certificateUploadRef.value) {
        console.log('证书上传组件存在，强制刷新证书文件');
        certificateUploadRef.value.refreshUploadedFiles(formData.value.qualificationCertificates);
      }
      if (videoUploadRef.value) {
        console.log('视频上传组件存在，强制刷新视频文件');
        videoUploadRef.value.refreshUploadedFiles(formData.value.demoVideos);
      }
    });
  }
};

const loadFormData = () => {
  if (props.teacher && props.teacher.id) {
    // 编辑模式：获取详细信息
    fetchTeacherDetail();
  } else {
    // 新增模式：重置表单
    resetForm();
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 检查是否有待上传的文件
    let pendingFilesCount = 0;
    let pendingFileTypes = [];

    // 检查证书上传组件
    if (certificateUploadRef.value && certificateUploadRef.value.hasPendingFiles()) {
      const count = certificateUploadRef.value.getPendingFilesCount();
      pendingFilesCount += count;
      pendingFileTypes.push(`${count}个资质证书`);
    }

    // 检查视频上传组件
    if (videoUploadRef.value && videoUploadRef.value.hasPendingFiles()) {
      const count = videoUploadRef.value.getPendingFilesCount();
      pendingFilesCount += count;
      pendingFileTypes.push(`${count}个示范视频`);
    }

    // 如果有待上传文件，提示用户
    if (pendingFilesCount > 0) {
      const fileTypesText = pendingFileTypes.join('、');
      const result = await ElMessageBox.confirm(
        `您还有 ${fileTypesText} 未上传，是否继续保存？未上传的文件将不会被保存。`,
        '提示',
        {
          confirmButtonText: '继续保存',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).catch(() => false);

      if (!result) {
        return;
      }
    }

    submitting.value = true;

    if (isEdit.value) {
      // 编辑模式：调用更新API
      const success = await teachingGroupStore.updateTeacherInfo({
        teacherId: props.teacher.id,
        name: formData.value.name,
        nickname: formData.value.nickname,
        gender: formData.value.gender,
        age: formData.value.age,
        phone: formData.value.phone,
        email: formData.value.email,
        currentLocation: formData.value.currentLocation,
        employmentType: formData.value.employmentType,
        currentStatus: formData.value.currentStatus,

        // 教育背景
        education: formData.value.education,
        graduateSchool: formData.value.graduateSchool,
        major: formData.value.major,
        universityType: formData.value.universityType,
        isNormalUniversity: formData.value.isNormalUniversity,
        studyAbroad: formData.value.studyAbroad,
        studyAbroadCountry: formData.value.studyAbroadCountry,

        // 教学资质
        teachingCertificateLevel: formData.value.teachingCertificateLevel,
        teachingYears: formData.value.teachingYears,
        englishQualification: formData.value.englishQualification,
        mandarinQualification: formData.value.mandarinQualification,
        communicationAbility: formData.value.communicationAbility,
        englishPronunciation: formData.value.englishPronunciation,
        subjects: formData.value.subjects,

        // 教学经历和风格
        taughtCourses: formData.value.taughtCourses,
        teachingStyle: formData.value.teachingStyle,
        suitableGrades: formData.value.suitableGrades,
        suitableLevels: formData.value.suitableLevels,
        suitablePersonality: formData.value.suitablePersonality,

        // 暑期课上课时间
        summerScheduleType: formData.value.summerScheduleType,

        teachingExperience: formData.value.teachingExperience,
        awards: formData.value.awards,
        introduction: formData.value.introduction,
        other: formData.value.other,
        status: formData.value.status,

        // 新增字段
        formalEntryDate: formData.value.formalEntryDate,
        qualificationCertificates: formData.value.qualificationCertificates,
        demoVideos: formData.value.demoVideos,
      });

      if (success) {
        ElMessage.success("教师信息更新成功");
        emit("success");
        handleClose();
      }
    } else {
      // 新增模式：调用创建API
      const submitData = {
        ...formData.value,
        userName: formData.value.phone, // 手机号作为用户名
        password: formData.value.phone.slice(-6), // 手机号后六位作为默认密码
      };

      const rst = await teachingGroupStore.createTeacher(submitData);

      if (rst) {
        ElMessage.success("教师创建成功");
        emit("success");
        handleClose();
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  } finally {
    submitting.value = false;
  }
};

// 监听器
watch(
  () => props.teacher,
  (newTeacher) => {
    if (newTeacher && props.modelValue) {
      // 重置详细信息
      teacherDetail.value = null;
      loadFormData();
    }
  },
  { immediate: true }
);

watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      // 重置详细信息
      teacherDetail.value = null;
      loadFormData();
    } else {
      // 清除表单验证
      formRef.value?.clearValidate();
    }
  }
);
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-weight: 600;
    color: #303133;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select) {
  width: 100%;
}

.upload-section {
  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .form-section {
    :deep(.el-row) {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
