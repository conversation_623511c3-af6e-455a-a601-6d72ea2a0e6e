<template>
  <div class="enhanced-time-slot-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <span class="title">可上课时间设置</span>
        <span class="subtitle">点击添加时间段，拖拽调整时间</span>
      </div>
      <div class="toolbar-right">
        <el-button size="small" @click="clearAll" :disabled="readonly">
          <el-icon><Delete /></el-icon>
          清空所有
        </el-button>
        <el-button size="small" type="primary" @click="applyTemplate" :disabled="readonly">
          <el-icon><Clock /></el-icon>
          常用模板
        </el-button>
      </div>
    </div>

    <!-- 快速操作栏 -->
    <div v-if="!readonly" class="quick-actions">
      <div class="quick-actions-title">快速添加：</div>
      <div class="quick-buttons">
        <el-button size="small" @click="quickAddMorning">
          <el-icon><Sunrise /></el-icon>
          上午时段
        </el-button>
        <el-button size="small" @click="quickAddAfternoon">
          <el-icon><Sunny /></el-icon>
          下午时段
        </el-button>
        <el-button size="small" @click="quickAddEvening">
          <el-icon><Moon /></el-icon>
          晚间时段
        </el-button>
        <el-button size="small" @click="quickAddFullDay">
          <el-icon><Calendar /></el-icon>
          全天时段
        </el-button>
      </div>
    </div>

    <!-- 时间段列表 -->
    <div class="time-slots-list">
      <div class="list-header">
        <span>已设置时间段 ({{ timeSlots.length }}个)</span>
        <el-button 
          v-if="!readonly" 
          size="small" 
          type="primary" 
          @click="showAddDialog = true"
        >
          <el-icon><Plus /></el-icon>
          添加时间段
        </el-button>
      </div>
      
      <div class="slots-container">
        <div
          v-for="(slot, index) in sortedTimeSlots"
          :key="slot.id || index"
          class="slot-item"
          :class="{ 'readonly': readonly }"
        >
          <div class="slot-day">
            {{ getWeekDayName(slot.weekday) }}
          </div>
          <div class="slot-time">
            <span class="time-range">{{ slot.startTime }} - {{ slot.endTime }}</span>
            <span class="duration">({{ calculateDuration(slot.startTime, slot.endTime) }})</span>
          </div>
          <div class="slot-status">
            <el-tag :type="getStatusTagType(slot.status)" size="small">
              {{ getStatusText(slot.status) }}
            </el-tag>
          </div>
          <div class="slot-actions" v-if="!readonly">
            <el-button
              type="primary"
              link
              size="small"
              @click="editTimeSlot(index)"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click="removeTimeSlot(index)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
        
        <div v-if="timeSlots.length === 0" class="empty-state">
          <el-empty description="暂无时间段设置">
            <el-button 
              v-if="!readonly" 
              type="primary" 
              @click="showAddDialog = true"
            >
              添加第一个时间段
            </el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 周视图预览 -->
    <div v-if="timeSlots.length > 0" class="weekly-preview">
      <div class="preview-header">
        <span>周视图预览</span>
        <el-button size="small" @click="showPreview = !showPreview">
          {{ showPreview ? '隐藏' : '显示' }}预览
        </el-button>
      </div>

      <div v-show="showPreview" class="preview-content">
        <WeeklyTimeSlotView :time-slots="timeSlots" />
      </div>
    </div>

    <!-- 添加/编辑时间段对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingIndex >= 0 ? '编辑时间段' : '添加时间段'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="currentSlot"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="星期" prop="weekday">
          <el-select v-model="currentSlot.weekday" placeholder="请选择星期">
            <el-option
              v-for="day in weekDays"
              :key="day.value"
              :label="day.label"
              :value="day.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="开始时间" prop="startTime">
          <el-time-picker
            v-model="currentSlot.startTime"
            format="HH:mm"
            value-format="HH:mm"
            :picker-options="timePickerOptions"
            placeholder="选择开始时间"
          />
        </el-form-item>
        
        <el-form-item label="结束时间" prop="endTime">
          <el-time-picker
            v-model="currentSlot.endTime"
            format="HH:mm"
            value-format="HH:mm"
            :picker-options="timePickerOptions"
            placeholder="选择结束时间"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="currentSlot.status">
            <el-radio value="available">可上课</el-radio>
            <el-radio value="scheduled">已排课</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="currentSlot.remark"
            type="textarea"
            :rows="2"
            placeholder="可选备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveTimeSlot">
            {{ editingIndex >= 0 ? '保存' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      title="选择时间模板"
      width="600px"
    >
      <div class="template-list">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-item"
          @click="applySelectedTemplate(template)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-description">{{ template.description }}</div>
          <div class="template-preview">
            <div
              v-for="slot in template.slots"
              :key="`${slot.weekday}-${slot.startTime}`"
              class="preview-slot"
            >
              {{ getWeekDayName(slot.weekday) }} {{ slot.startTime }}-{{ slot.endTime }}
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Clock, Plus, Edit, Sunrise, Sunny, Moon, Calendar } from '@element-plus/icons-vue'
import WeeklyTimeSlotView from './WeeklyTimeSlotView.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const timeSlots = ref([])
const showAddDialog = ref(false)
const showTemplateDialog = ref(false)
const showPreview = ref(true)
const editingIndex = ref(-1)
const formRef = ref()

// 当前编辑的时间段
const currentSlot = ref({
  weekday: 1,
  startTime: '09:00',
  endTime: '10:00',
  status: 'available',
  remark: ''
})

// 常量
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 时间选择器配置（5分钟间隔）
const timePickerOptions = {
  start: '06:00',
  step: '00:05',
  end: '23:55'
}

// 表单验证规则
const formRules = {
  weekday: [
    { required: true, message: '请选择星期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ]
}

// 预设模板
const templates = [
  {
    id: 'workday',
    name: '工作日模板',
    description: '周一至周五，上午9:00-12:00，下午14:00-18:00',
    slots: [
      ...Array.from({ length: 5 }, (_, i) => [
        { weekday: i + 1, startTime: '09:00', endTime: '12:00', status: 'available' },
        { weekday: i + 1, startTime: '14:00', endTime: '18:00', status: 'available' }
      ]).flat()
    ]
  },
  {
    id: 'fullweek',
    name: '全周模板',
    description: '周一至周日，上午9:00-12:00，下午14:00-18:00',
    slots: [
      ...Array.from({ length: 7 }, (_, i) => [
        { weekday: i + 1, startTime: '09:00', endTime: '12:00', status: 'available' },
        { weekday: i + 1, startTime: '14:00', endTime: '18:00', status: 'available' }
      ]).flat()
    ]
  },
  {
    id: 'evening',
    name: '晚间模板',
    description: '周一至周五，晚上18:30-21:30',
    slots: Array.from({ length: 5 }, (_, i) => ({
      weekday: i + 1,
      startTime: '18:30',
      endTime: '21:30',
      status: 'available'
    }))
  }
]

// 工具函数
// 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
function normalizeTimeFormat(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return timeStr
  }

  const parts = timeStr.split(':')
  if (parts.length >= 2) {
    // 只取小时和分钟部分，忽略秒
    const hours = parts[0].padStart(2, '0')
    const minutes = parts[1].padStart(2, '0')
    return `${hours}:${minutes}`
  }

  return timeStr
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  // 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
  const normalizedSlots = (newValue || []).map(slot => ({
    ...slot,
    startTime: normalizeTimeFormat(slot.startTime),
    endTime: normalizeTimeFormat(slot.endTime)
  }))
  timeSlots.value = normalizedSlots
}, { immediate: true, deep: true })

watch(timeSlots, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
}, { deep: true })

// 计算属性
const sortedTimeSlots = computed(() => {
  return [...timeSlots.value].sort((a, b) => {
    if (a.weekday !== b.weekday) {
      return a.weekday - b.weekday
    }
    return a.startTime.localeCompare(b.startTime)
  })
})

// 方法
function validateEndTime(rule, value, callback) {
  if (!value) {
    callback(new Error('请选择结束时间'))
    return
  }

  if (value <= currentSlot.value.startTime) {
    callback(new Error('结束时间必须大于开始时间'))
    return
  }

  callback()
}

function getWeekDayName(weekday) {
  const day = weekDays.find(d => d.value === weekday)
  return day ? day.label : '未知'
}

function getStatusText(status) {
  const statusMap = {
    'available': '可上课',
    'scheduled': '已排课'
  }
  return statusMap[status] || status
}

function getStatusTagType(status) {
  const typeMap = {
    'available': 'success',
    'scheduled': 'warning'
  }
  return typeMap[status] || 'info'
}

function calculateDuration(startTime, endTime) {
  const start = new Date(`2000-01-01 ${startTime}:00`)
  const end = new Date(`2000-01-01 ${endTime}:00`)
  const diff = end - start
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (hours > 0 && minutes > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时`
  } else {
    return `${minutes}分钟`
  }
}

function addTimeSlot() {
  showAddDialog.value = true
  editingIndex.value = -1
  currentSlot.value = {
    weekday: 1,
    startTime: '09:00',
    endTime: '10:00',
    status: 'available',
    remark: ''
  }
}

function editTimeSlot(index) {
  const slot = timeSlots.value[index]
  showAddDialog.value = true
  editingIndex.value = index
  currentSlot.value = { ...slot }
}

function saveTimeSlot() {
  formRef.value.validate((valid) => {
    if (!valid) return

    // 检查时间冲突
    const conflictIndex = timeSlots.value.findIndex((slot, index) => {
      if (editingIndex.value >= 0 && index === editingIndex.value) {
        return false // 排除自己
      }

      return slot.weekday === currentSlot.value.weekday &&
             isTimeOverlap(slot.startTime, slot.endTime, currentSlot.value.startTime, currentSlot.value.endTime)
    })

    if (conflictIndex >= 0) {
      ElMessage.error('时间段与现有时间段冲突，请重新选择')
      return
    }

    const newSlot = {
      ...currentSlot.value,
      id: editingIndex.value >= 0 ? timeSlots.value[editingIndex.value].id : `slot_${Date.now()}`
    }

    if (editingIndex.value >= 0) {
      timeSlots.value[editingIndex.value] = newSlot
    } else {
      timeSlots.value.push(newSlot)
    }

    showAddDialog.value = false
    ElMessage.success(editingIndex.value >= 0 ? '时间段已更新' : '时间段已添加')
  })
}

function isTimeOverlap(start1, end1, start2, end2) {
  return start1 < end2 && start2 < end1
}

function cancelEdit() {
  showAddDialog.value = false
  editingIndex.value = -1
}

function removeTimeSlot(index) {
  ElMessageBox.confirm(
    '确定要删除这个时间段吗？',
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    timeSlots.value.splice(index, 1)
    ElMessage.success('时间段已删除')
  }).catch(() => {
    // 用户取消删除
  })
}

function clearAll() {
  if (timeSlots.value.length === 0) {
    ElMessage.info('没有时间段需要清空')
    return
  }

  ElMessageBox.confirm(
    '确定要清空所有时间段吗？',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    timeSlots.value = []
    ElMessage.success('已清空所有时间段')
  }).catch(() => {
    // 用户取消清空
  })
}

function applyTemplate() {
  showTemplateDialog.value = true
}

function applySelectedTemplate(template) {
  const newSlots = template.slots.map((slot, index) => ({
    ...slot,
    id: `template_${template.id}_${index}`,
    remark: slot.remark || ''
  }))

  timeSlots.value = newSlots
  showTemplateDialog.value = false
  ElMessage.success(`已应用${template.name}`)
}

// 快速添加方法
function quickAddMorning() {
  addQuickTimeSlot('09:00', '12:00', '上午时段')
}

function quickAddAfternoon() {
  addQuickTimeSlot('14:00', '17:30', '下午时段')
}

function quickAddEvening() {
  addQuickTimeSlot('18:30', '21:30', '晚间时段')
}

function quickAddFullDay() {
  addQuickTimeSlot('09:00', '17:30', '全天时段')
}

function addQuickTimeSlot(startTime, endTime, remark) {
  currentSlot.value = {
    weekday: 1, // 默认周一
    startTime,
    endTime,
    status: 'available',
    remark
  }
  showAddDialog.value = true
  editingIndex.value = -1
}
</script>

<style lang="scss" scoped>
.enhanced-time-slot-editor {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;

    .toolbar-left {
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .subtitle {
        font-size: 12px;
        color: #909399;
        margin-left: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .quick-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background-color: #fafbfc;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    margin-bottom: 16px;

    .quick-actions-title {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }

    .quick-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .time-slots-list {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .slots-container {
      .slot-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background-color: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        margin-bottom: 8px;
        transition: all 0.3s;

        &:hover:not(.readonly) {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        .slot-day {
          width: 60px;
          font-weight: 500;
          color: #606266;
        }

        .slot-time {
          flex: 1;
          margin-left: 16px;

          .time-range {
            font-weight: 500;
            color: #303133;
          }

          .duration {
            margin-left: 8px;
            font-size: 12px;
            color: #909399;
          }
        }

        .slot-status {
          margin-left: 16px;
        }

        .slot-actions {
          margin-left: 16px;
          display: flex;
          gap: 4px;
        }
      }

      .empty-state {
        text-align: center;
        padding: 40px 20px;
      }
    }
  }

  .template-list {
    .template-item {
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      .template-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .template-description {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }

      .template-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .preview-slot {
          padding: 2px 8px;
          background-color: #e1f3d8;
          border-radius: 4px;
          font-size: 12px;
          color: #67c23a;
        }
      }
    }
  }

  .weekly-preview {
    margin-top: 24px;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .preview-content {
      border-radius: 8px;
      overflow: hidden;
    }
  }
}
</style>
