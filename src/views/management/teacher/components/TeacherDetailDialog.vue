<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`教师详情 - ${currentTeacher?.name || ''}`"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="currentTeacher" v-loading="loading" class="teacher-detail-container">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" size="small" @click="handleEdit">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>教师姓名：</label>
              <span>{{ currentTeacher.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>昵称：</label>
              <span>{{ currentTeacher.nickname || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>手机号码：</label>
              <span>{{ currentTeacher.phone }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>性别：</label>
              <span>{{ getGenderText(currentTeacher.gender) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>年龄：</label>
              <span>{{ currentTeacher.age || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ currentTeacher.email || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>目前所在地：</label>
              <span>{{ currentTeacher.currentLocation || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>工作性质：</label>
              <el-tag v-if="currentTeacher.employmentType" size="small" :type="getEmploymentTypeTagType(currentTeacher.employmentType)">
                {{ getEmploymentTypeText(currentTeacher.employmentType) }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>目前状态：</label>
              <span>{{ currentTeacher.currentStatus || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>教学组：</label>
              <span>{{ currentTeacher.groupName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>正式入职时间：</label>
              <span>{{ currentTeacher.formalEntryDate || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>状态：</label>
              <el-tag :type="getStatusTagType(currentTeacher.status)" size="small">
                {{ getStatusText(currentTeacher.status) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>入职时间：</label>
              <span>{{ formatDateTime(currentTeacher.createTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>最后更新：</label>
              <span>{{ formatDateTime(currentTeacher.updateTime) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 教育背景 -->
      <el-card class="info-card">
        <template #header>
          <span>教育背景</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>最高学历：</label>
              <span>{{ currentTeacher.education || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>毕业院校：</label>
              <span>{{ currentTeacher.graduateSchool || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>毕业专业：</label>
              <span>{{ currentTeacher.major || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>大学属性：</label>
              <el-tag v-if="currentTeacher.universityType" size="small" :type="getUniversityTypeTagType(currentTeacher.universityType)">
                {{ currentTeacher.universityType }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>是否师范类：</label>
              <el-tag :type="currentTeacher.isNormalUniversity ? 'success' : 'info'" size="small">
                {{ currentTeacher.isNormalUniversity ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>是否留学：</label>
              <el-tag :type="currentTeacher.studyAbroad ? 'success' : 'info'" size="small">
                {{ currentTeacher.studyAbroad ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="currentTeacher.studyAbroad">
          <el-col :span="8">
            <div class="info-item">
              <label>留学国家：</label>
              <span>{{ currentTeacher.studyAbroadCountry || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>教龄：</label>
              <span>{{ currentTeacher.teachingYears || 0 }} 年</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>教资级别：</label>
              <span>{{ currentTeacher.teachingCertificateLevel || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>教授学科：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="subject in (currentTeacher.subjects || [])"
                  :key="subject"
                  size="small"
                  class="subject-tag"
                >
                  {{ subject }}
                </el-tag>
                <span v-if="!currentTeacher.subjects || currentTeacher.subjects.length === 0">-</span>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>已通过培训科目：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="subject in (currentTeacher.trainingSubjects || [])"
                  :key="subject"
                  size="small"
                  class="subject-tag"
                  type="success"
                >
                  {{ subject }}
                </el-tag>
                <span v-if="!currentTeacher.trainingSubjects || currentTeacher.trainingSubjects.length === 0">-</span>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>英语资质：</label>
              <span>{{ currentTeacher.englishQualification || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>普通话资质：</label>
              <span>{{ currentTeacher.mandarinQualification || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>沟通能力：</label>
              <el-tag v-if="currentTeacher.communicationAbility" size="small" :type="getCommunicationAbilityTagType(currentTeacher.communicationAbility)">
                {{ currentTeacher.communicationAbility }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>英语发音：</label>
              <el-tag v-if="currentTeacher.englishPronunciation" size="small" :type="getEnglishPronunciationTagType(currentTeacher.englishPronunciation)">
                {{ currentTeacher.englishPronunciation }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>暑期课上课时间：</label>
              <el-tag :type="getSummerScheduleTagType(currentTeacher.summerScheduleType)" size="small">
                {{ getSummerScheduleText(currentTeacher.summerScheduleType) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 教学信息 -->
      <el-card class="info-card">
        <template #header>
          <span>教学信息</span>
        </template>
        <div class="stats-grid">
          <div class="stats-item">
            <div class="stats-number">{{ currentTeacher.currentStudentCount || 0 }}</div>
            <div class="stats-label">现有学生数</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">{{ formatTeachingHours(currentTeacher.totalTeachingHours) }}</div>
            <div class="stats-label">已上课时</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">{{ formatPassRate(currentTeacher.trialCoursePassRate) }}</div>
            <div class="stats-label">试听课通过率</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">{{ teachingStats.totalHours }}</div>
            <div class="stats-label">总课时</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">{{ teachingStats.completedHours }}</div>
            <div class="stats-label">已完成课时</div>
          </div>
          <div class="stats-item">
            <div class="stats-number">{{ teachingStats.scheduledHours }}</div>
            <div class="stats-label">已排课时</div>
          </div>
        </div>
      </el-card>

      <!-- 个人信息 -->
      <el-card class="info-card">
        <template #header>
          <span>个人信息</span>
        </template>
        
        <el-row v-if="currentTeacher.taughtCourses && currentTeacher.taughtCourses.length > 0" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>教过课程：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="course in currentTeacher.taughtCourses"
                  :key="course"
                  size="small"
                  class="subject-tag"
                  type="info"
                >
                  {{ course }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="currentTeacher.teachingStyle && currentTeacher.teachingStyle.length > 0" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>上课风格：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="style in currentTeacher.teachingStyle"
                  :key="style"
                  size="small"
                  class="subject-tag"
                  type="success"
                >
                  {{ style }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8" v-if="currentTeacher.suitableGrades && currentTeacher.suitableGrades.length > 0">
            <div class="info-item">
              <label>适合学生年级：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="grade in currentTeacher.suitableGrades"
                  :key="grade"
                  size="small"
                  class="subject-tag"
                  type="primary"
                >
                  {{ grade }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="8" v-if="currentTeacher.suitableLevels && currentTeacher.suitableLevels.length > 0">
            <div class="info-item">
              <label>适合学生程度：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="level in currentTeacher.suitableLevels"
                  :key="level"
                  size="small"
                  class="subject-tag"
                  type="warning"
                >
                  {{ level }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="8" v-if="currentTeacher.suitablePersonality">
            <div class="info-item">
              <label>适合学生性格：</label>
              <el-tag size="small" type="info">
                {{ currentTeacher.suitablePersonality }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="currentTeacher.teachingExperience" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>教学经历：</label>
              <div class="text-content">{{ currentTeacher.teachingExperience }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="currentTeacher.awards" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>获奖奖项：</label>
              <div class="text-content">{{ currentTeacher.awards }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="currentTeacher.introduction" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>个人简介：</label>
              <div class="text-content">{{ currentTeacher.introduction }}</div>
            </div>
          </el-col>
        </el-row>

        <!-- 资质证书 -->
        <el-row v-if="currentTeacher.qualificationCertificates && currentTeacher.qualificationCertificates.length > 0" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>资质证书：</label>
              <div class="file-list">
                <div
                  v-for="(cert, index) in currentTeacher.qualificationCertificates"
                  :key="index"
                  class="file-item"
                >
                  <el-link
                    :href="cert"
                    target="_blank"
                    type="primary"
                    :underline="false"
                  >
                    <el-icon><Document /></el-icon>
                    证书{{ index + 1 }}
                  </el-link>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 示范上课视频 -->
        <el-row v-if="currentTeacher.demoVideos && currentTeacher.demoVideos.length > 0" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>示范上课视频：</label>
              <div class="file-list">
                <div
                  v-for="(video, index) in currentTeacher.demoVideos"
                  :key="index"
                  class="file-item"
                >
                  <el-link
                    :href="video"
                    target="_blank"
                    type="primary"
                    :underline="false"
                  >
                    <el-icon><VideoPlay /></el-icon>
                    示范视频{{ index + 1 }}
                  </el-link>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 最近课程 -->
      <el-card v-if="false" class="info-card" >
        <template #header>
          <div class="card-header">
            <span>最近课程</span>
            <el-button type="primary" size="small" @click="handleViewAllCourses">
              查看全部
            </el-button>
          </div>
        </template>
        
        <el-table :data="recentCourses" stripe>
          <el-table-column prop="date" label="日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.date) }}
            </template>
          </el-table-column>
          <el-table-column prop="time" label="时间" width="150">
            <template #default="{ row }">
              {{ row.startTime }} - {{ row.endTime }}
            </template>
          </el-table-column>
          <el-table-column prop="studentName" label="学生" width="100" />
          <el-table-column prop="subject" label="科目" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getCourseStatusTagType(row.status)" size="small">
                {{ getCourseStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remarks" label="备注" show-overflow-tooltip />
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Edit, Document, VideoPlay } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import { useTeachingGroupStore } from '@/stores/teachingGroup'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit'])

// 响应式数据
const teachingStats = ref({
  studentCount: 0,
  totalHours: 0,
  completedHours: 0,
  scheduledHours: 0
})

const recentCourses = ref([])
const teacherDetail = ref(null)
const loading = ref(false)

// Store
const teachingGroupStore = useTeachingGroupStore()

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 当前显示的教师信息（优先使用最新获取的详细信息）
const currentTeacher = computed(() => {
  return teacherDetail.value || props.teacher
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleEdit = () => {
  // 传递最新的教师信息
  emit('edit', currentTeacher.value)
  handleClose()
}

const handleViewAllCourses = () => {
  // 这里可以跳转到课程管理页面或打开课程列表弹窗
  console.log('查看全部课程')
}

const getGenderText = (gender) => {
  const genderMap = {
    '0': '男',
    '1': '女',
    '2': '未知'
  }
  return genderMap[gender] || gender
}

const getStatusTagType = (status) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '在职',
    'inactive': '离职'
  }
  return statusMap[status] || status
}

const getCourseStatusTagType = (status) => {
  const statusMap = {
    'completed': 'success',
    'scheduled': 'primary',
    'cancelled': 'danger',
    'in_progress': 'warning'
  }
  return statusMap[status] || 'info'
}

const getCourseStatusText = (status) => {
  const statusMap = {
    'completed': '已完成',
    'scheduled': '已排课',
    'cancelled': '已取消',
    'in_progress': '进行中'
  }
  return statusMap[status] || status
}

const getUniversityTypeTagType = (type) => {
  const typeMap = {
    '双一流': 'danger',
    '985': 'warning',
    '211': 'primary',
    '一本': 'info',
    '普通': '',
  }
  return typeMap[type] || 'info'
}

// 格式化上课时长（分钟转小时）
const formatTeachingHours = (minutes) => {
  if (!minutes || minutes === 0) return '0小时'

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60

  if (hours === 0) {
    return `${remainingMinutes}分钟`
  } else if (remainingMinutes === 0) {
    return `${hours}小时`
  } else {
    return `${hours}小时${remainingMinutes}分钟`
  }
}

// 格式化通过率
const formatPassRate = (rate) => {
  if (rate === null || rate === undefined) return '0%'
  return `${rate.toFixed(1)}%`
}

const getCommunicationAbilityTagType = (ability) => {
  const abilityMap = {
    '优秀': 'success',
    '良好': 'primary',
    '一般': 'warning',
    '薄弱': 'danger'
  }
  return abilityMap[ability] || 'info'
}

const getEnglishPronunciationTagType = (pronunciation) => {
  const pronunciationMap = {
    '优秀': 'success',
    '正常': 'primary',
    '一般': 'warning',
    '较薄弱': 'danger'
  }
  return pronunciationMap[pronunciation] || 'info'
}

const getSummerScheduleText = (type) => {
  const typeMap = {
    'full': '全满档',
    'golden': '黄金档',
    'other': '其他档'
  }
  return typeMap[type] || '其他档'
}

const getSummerScheduleTagType = (type) => {
  const typeMap = {
    'full': 'danger',
    'golden': 'warning',
    'other': 'info'
  }
  return typeMap[type] || 'info'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取工作性质标签类型
const getEmploymentTypeTagType = (type) => {
  const typeMap = {
    'full_time': 'success',
    'intended_full_time': 'primary',
    'part_time': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取工作性质文本
const getEmploymentTypeText = (type) => {
  const textMap = {
    'full_time': '全职',
    'intended_full_time': '意向全职',
    'part_time': '兼职'
  }
  return textMap[type] || type
}

// 获取教师详细信息
const fetchTeacherDetail = async () => {
  if (!props.teacher?.id) return

  loading.value = true
  try {
    // 获取教师详细信息
    const detail = await teachingGroupStore.fetchTeacherDetail(props.teacher.id)
    if (detail) {
      console.log('获取到的教师详细信息:', detail)
      console.log('新增统计字段:', {
        trialCoursePassRate: detail.trialCoursePassRate,
        totalTeachingHours: detail.totalTeachingHours,
        currentStudentCount: detail.currentStudentCount
      })
      teacherDetail.value = detail
    }
  } catch (error) {
    console.error('获取教师详细信息失败:', error)
    ElMessage.error('获取教师详细信息失败')
  } finally {
    loading.value = false
  }
}

const fetchTeachingData = async () => {
  if (!props.teacher?.id) return

  try {
    // 获取教师带教信息
    const teachingInfo = await teachingGroupStore.fetchTeachingInfo(props.teacher.id)
    if (teachingInfo) {
      teachingStats.value = {
        studentCount: teachingInfo.currentStudents || 0,
        totalHours: teachingInfo.totalHours || 0,
        completedHours: teachingInfo.consumedHours || 0,
        scheduledHours: teachingInfo.unconsumedHours || 0
      }
    }

    // 获取教师最近课程
    const courses = await teachingGroupStore.fetchTeacherSchedule(props.teacher.id)
    recentCourses.value = courses.slice(0, 5) // 只显示最近5个课程
  } catch (error) {
    console.error('获取教学数据失败:', error)
    // 如果API调用失败，设置默认值
    teachingStats.value = {
      studentCount: 0,
      totalHours: 0,
      completedHours: 0,
      scheduledHours: 0
    }
    recentCourses.value = []
  }
}

// 监听器
watch(() => props.teacher, (newTeacher) => {
  if (newTeacher && props.modelValue) {
    // 重置详细信息
    teacherDetail.value = null
    fetchTeacherDetail()
    fetchTeachingData()
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && props.teacher) {
    // 重置详细信息
    teacherDetail.value = null
    fetchTeacherDetail()
    fetchTeachingData()
  }
})
</script>

<style lang="scss" scoped>
.teacher-detail-container {
  .info-card {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .info-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-weight: 600;
        color: #606266;
        margin-right: 8px;
      }

      .text-content {
        margin-top: 4px;
        padding: 8px 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #303133;
        line-height: 1.5;
      }

      .subjects-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .subject-tag {
          margin: 0;
        }
      }

      .file-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-top: 8px;

        .file-item {
          .el-link {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            background-color: #f5f7fa;
            border-radius: 4px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s;

            &:hover {
              background-color: #ecf5ff;
              border-color: #409eff;
            }

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      .stats-item {
        text-align: center;
        padding: 16px;
        background-color: #f5f7fa;
        border-radius: 8px;

        .stats-number {
          font-size: 24px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 4px;
        }

        .stats-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
