<template>
  <el-dialog
    title="核销记录详情"
    v-model="visible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="writeOffDetail" class="writeoff-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card" style="margin-bottom: 20px;">
        <template #header>
          <span>基本信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="第三方订单号">{{ writeOffDetail.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="核销来源">
            <el-tag :type="getSourceTagType(writeOffDetail.orderSource)">
              {{ writeOffDetail.orderSource }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ writeOffDetail.studentName }}</el-descriptions-item>
          <el-descriptions-item label="学生手机">{{ writeOffDetail.studentPhone }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ writeOffDetail.createBy }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(writeOffDetail.createTime) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 产品信息 -->
      <el-card class="detail-card" style="margin-bottom: 20px;">
        <template #header>
          <span>产品信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品名称">{{ writeOffDetail.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品价格">
            <span class="amount-text">¥{{ (writeOffDetail.productPrice / 100).toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="学科">{{ writeOffDetail.subject }}</el-descriptions-item>
          <el-descriptions-item label="课型">{{ writeOffDetail.courseType }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 审核信息 -->
      <el-card class="detail-card" style="margin-bottom: 20px;">
        <template #header>
          <span>审核信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getApproveStatusTagType(writeOffDetail.approveStatus)">
              {{ writeOffDetail.approveStatus }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核人">{{ writeOffDetail.approveBy || '-' }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{ formatDateTime(writeOffDetail.approveTime) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="审核备注" :span="2">{{ writeOffDetail.approveRemark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 订单截图 -->
      <el-card class="detail-card" v-if="writeOffDetail.orderImg">
        <template #header>
          <span>订单截图</span>
        </template>
        <div class="image-container">
          <el-image
            :src="writeOffDetail.orderImg"
            :preview-src-list="[writeOffDetail.orderImg]"
            fit="contain"
            style="width: 200px; height: 200px;"
          />
        </div>
      </el-card>
    </div>

    <div v-loading="detailLoading" v-else class="loading-container">
      <div style="text-align: center; padding: 50px;">
        加载中...
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关 闭</el-button>
      <el-button 
        v-if="canApprove" 
        type="success" 
        @click="handleApprove('审核通过')"
      >
        审核通过
      </el-button>
      <el-button 
        v-if="canApprove" 
        type="danger" 
        @click="handleApprove('审核拒绝')"
      >
        审核拒绝
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { getWriteOffDetail } from '@/api/management/order-writeroff'
import { formatDateTime } from '@/utils/date'

export default {
  name: 'WriteOffDetailDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    writeoffId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: this.value,
      detailLoading: false,
      writeOffDetail: null
    }
  },
  computed: {
    canApprove() {
      return this.writeOffDetail && this.writeOffDetail.approveStatus === '待审核'
    }
  },
  watch: {
    value(val) {
      this.visible = val
      if (val && this.writeoffId) {
        this.loadDetail()
      }
    },
    visible(val) {
      this.$emit('input', val)
    },
    writeoffId(val) {
      if (val && this.visible) {
        this.loadDetail()
      }
    }
  },
  methods: {
    formatDateTime,
    async loadDetail() {
      if (!this.writeoffId) return
      
      try {
        this.detailLoading = true
        const response = await getWriteOffDetail(this.writeoffId)
        
        if (response.code === 200) {
          this.writeOffDetail = response.data
        } else {
          this.$message.error(response.msg || '获取详情失败')
        }
      } catch (error) {
        console.error('获取核销详情失败:', error)
        this.$message.error('获取详情失败')
      } finally {
        this.detailLoading = false
      }
    },
    getSourceTagType(source) {
      const sourceMap = {
        '抖店': 'primary',
        '星橙CRM': 'success'
      }
      return sourceMap[source] || 'info'
    },
    getApproveStatusTagType(status) {
      const statusMap = {
        '待审核': 'warning',
        '审核通过': 'success',
        '审核拒绝': 'danger'
      }
      return statusMap[status] || 'info'
    },
    handleApprove(result) {
      this.$emit('approve', {
        record: this.writeOffDetail,
        result: result
      })
    },
    handleClose() {
      this.visible = false
      this.writeOffDetail = null
    }
  }
}
</script>

<style scoped>
.writeoff-detail {
  padding: 10px 0;
}

.detail-card {
  border-radius: 8px;
}

.amount-text {
  font-weight: 500;
  color: #E6A23C;
}

.image-container {
  text-align: center;
  padding: 20px;
}

.loading-container {
  min-height: 200px;
}
</style>
