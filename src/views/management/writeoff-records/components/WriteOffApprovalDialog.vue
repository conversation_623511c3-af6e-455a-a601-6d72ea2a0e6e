<template>
  <el-dialog
    title="核销审核"
    v-model="visible"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="writeoffRecord" class="approval-content">
      <!-- 核销信息概要 -->
      <el-card class="info-card" style="margin-bottom: 20px;">
        <template #header>
          <span>核销信息</span>
        </template>
        <el-descriptions :column="1" size="small">
          <el-descriptions-item label="第三方订单号">{{ writeoffRecord.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="核销来源">{{ writeoffRecord.orderSource }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ writeoffRecord.studentName }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ writeoffRecord.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品价格">¥{{ (writeoffRecord.productPrice / 100).toFixed(2) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 审核表单 -->
      <el-form
        ref="approvalForm"
        :model="approvalForm"
        :rules="approvalRules"
        label-width="100px"
      >
        <el-form-item label="审核结果">
          <el-tag :type="approvalResult === '审核通过' ? 'success' : 'danger'">
            {{ approvalResult }}
          </el-tag>
        </el-form-item>
        
        <el-form-item label="审核备注" prop="approveRemark">
          <el-input
            v-model="approvalForm.approveRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入审核备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleClose">取 消</el-button>
      <el-button 
        type="primary" 
        :loading="submitting" 
        @click="handleSubmit"
      >
        {{ submitting ? '提交中...' : '确认审核' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { approveWriteOff } from '@/api/management/order-writeroff'

export default {
  name: 'WriteOffApprovalDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    writeoffRecord: {
      type: Object,
      default: null
    },
    approvalResult: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: this.value,
      submitting: false,
      approvalForm: {
        approveRemark: ''
      },
      approvalRules: {
        approveRemark: [
          { required: true, message: '请输入审核备注', trigger: 'blur' },
          { min: 5, max: 200, message: '审核备注长度在5到200个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    value(val) {
      this.visible = val
      if (val) {
        this.resetForm()
      }
    },
    visible(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    resetForm() {
      this.approvalForm.approveRemark = ''
      if (this.$refs.approvalForm) {
        this.$refs.approvalForm.resetFields()
      }
    },
    handleSubmit() {
      this.$refs.approvalForm.validate(async (valid) => {
        if (valid) {
          await this.doApproval()
        }
      })
    },
    async doApproval() {
      try {
        this.submitting = true
        
        const approvalData = {
          approveStatus: this.approvalResult,
          approveRemark: this.approvalForm.approveRemark
        }
        
        const response = await approveWriteOff(this.writeoffRecord.id, approvalData)
        
        if (response.code === 200) {
          this.$message.success('审核成功')
          this.$emit('success')
          this.handleClose()
        } else {
          this.$message.error('审核失败: ' + (response.msg || '未知错误'))
        }
      } catch (error) {
        console.error('审核失败:', error)
        this.$message.error('审核失败: ' + (error.message || '未知错误'))
      } finally {
        this.submitting = false
      }
    },
    handleClose() {
      this.visible = false
      this.resetForm()
    }
  }
}
</script>

<style scoped>
.approval-content {
  padding: 10px 0;
}

.info-card {
  border-radius: 8px;
}
</style>
