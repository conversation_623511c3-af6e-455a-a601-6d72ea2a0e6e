<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>学生课时管理</span>
        </div>
      </template>

      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
        <el-form-item label="学生姓名" prop="studentName">
          <el-input
            v-model="queryParams.studentName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学生手机" prop="studentPhone">
          <el-input
            v-model="queryParams.studentPhone"
            placeholder="请输入学生手机号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select
            v-model="queryParams.subject"
            placeholder="请选择学科"
            clearable
            style="width: 150px"
            @change="handleSubjectChange"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        <el-form-item label="课型" prop="specification">
          <el-select
            v-model="queryParams.specification"
            placeholder="请选择课型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in availableSpecifications"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="性质" prop="nature">
          <el-select
            v-model="queryParams.nature"
            placeholder="请选择性质"
            clearable
            style="width: 150px"
          >
            <el-option label="正式课" value="正式课" />
            <el-option label="试听课" value="试听课" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="生效" value="active" />
            <el-option label="失效" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item label="老师姓名" prop="teacherName">
          <el-input
            v-model="queryParams.teacherName"
            placeholder="请输入老师姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="老师手机" prop="teacherPhone">
          <el-input
            v-model="queryParams.teacherPhone"
            placeholder="请输入老师手机号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Upload"
            @click="handleImport"
            v-hasPermi="['management:course-hours:import']"
          >
            导入课时
          </el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="List"
            @click="handleViewAllConsumption"
            v-hasPermi="['management:student-course-hours:consumption']"
          >
            课消记录
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Clock"
            @click="handleViewAllHistory"
            v-hasPermi="['management:student-course-hours:history']"
          >
            调整历史
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAddCourseHours"
            v-hasPermi="['management:student-course-hours:add']"
          >
            新增课时
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['management:student-course-hours:export']"
            :loading="exportLoading"
          >
            导出课时
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="courseHoursList" border>
        <el-table-column label="学生姓名" prop="studentName" width="120" />
        <el-table-column label="学生手机" prop="studentPhone" width="130" />
        <el-table-column label="老师" prop="teacherInfo" width="180">
          <template #default="scope">
            <span v-if="scope.row.teacherInfo" class="text-info">{{ scope.row.teacherInfo }}</span>
            <span v-else class="text-muted">暂无老师</span>
          </template>
        </el-table-column>
        <el-table-column label="学科" prop="subject" width="80" />
        <el-table-column label="课型" prop="specification" width="80" />
        <el-table-column label="性质" prop="nature" width="80" />
        <el-table-column v-if="false" label="课时类型" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="scope.row.courseHoursType === '首购' ? 'success' : 'warning'" 
              size="small"
            >
              {{ scope.row.courseHoursType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="下单销售" width="150" align="center">
          <template #default="scope">
            <div v-if="scope.row.orderSalesName || scope.row.orderSalesGroupName" class="sales-info">
              <div v-if="scope.row.orderSalesName" class="sales-name">
                <el-tag type="info" size="small">{{ scope.row.orderSalesName }}</el-tag>
              </div>
              <div v-if="scope.row.orderSalesGroupName" class="sales-group">
                <el-tag type="" size="small">{{ scope.row.orderSalesGroupName }}</el-tag>
              </div>
            </div>
            <span v-else class="text-muted">暂无销售</span>
          </template>
        </el-table-column>
        <el-table-column label="当前销售" width="150" align="center">
          <template #default="scope">
            <div v-if="scope.row.currentSaleName || scope.row.currentSalesGroupName" class="sales-info">
              <div v-if="scope.row.currentSaleName" class="sales-name">
                <el-tag type="success" size="small">{{ scope.row.currentSaleName }}</el-tag>
              </div>
              <div v-if="scope.row.currentSalesGroupName" class="sales-group">
                <el-tag type="primary" size="small">{{ scope.row.currentSalesGroupName }}</el-tag>
              </div>
            </div>
            <span v-else class="text-muted">暂无销售</span>
          </template>
        </el-table-column>
        <el-table-column label="来源" width="120" align="center">
          <template #default="scope">
            <div class="source-info">
              <el-tag 
                :type="isOrderBasedCourseHours(scope.row) ? 'warning' : 'info'" 
                size="small"
              >
                {{ scope.row.sourceType || '手动' }}
              </el-tag>
              <div v-if="isOrderBasedCourseHours(scope.row) && scope.row.batchNo" class="order-id">
                {{ scope.row.batchNo }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="总课时" prop="totalHours" width="100" align="right">
          <template #default="scope">
            <span class="text-primary">{{ scope.row.totalHours }}</span>
          </template>
        </el-table-column>
        <el-table-column label="剩余课时" prop="remainingHours" width="100" align="right">
          <template #default="scope">
            <span :class="scope.row.remainingHours <= 5 ? 'text-danger' : 'text-success'">
              {{ scope.row.remainingHours }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="购买课时" width="120" align="center">
          <template #default="scope">
            <div class="hours-detail">
              <div class="total-hours">总: {{ scope.row.purchasedHours }}</div>
              <div
                class="remaining-hours"
                :class="{ 'low-hours': (scope.row.remainingPurchasedHours || 0) <= 2 }"
              >
                剩: {{ scope.row.remainingPurchasedHours || 0 }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="赠送课时" width="120" align="center">
          <template #default="scope">
            <div class="hours-detail">
              <div class="total-hours">总: {{ scope.row.giftHours }}</div>
              <div
                class="remaining-hours"
                :class="{ 'low-hours': (scope.row.remainingGiftHours || 0) <= 2 }"
              >
                剩: {{ scope.row.remainingGiftHours || 0 }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="已消耗"
          prop="consumedTotalHours"
          width="100"
          align="right"
        />
        <el-table-column label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag 
              :type="scope.row.status === 'active' ? 'success' : 'danger'" 
              size="small"
            >
              {{ scope.row.status === 'active' ? '生效' : '失效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="单价" prop="unitPrice" width="100" align="right">
          <template #default="scope">
            <span class="text-primary">¥{{ scope.row.unitPrice || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="500" fixed="right">
          <template #default="scope">
            <el-tooltip 
              :content="getAdjustTooltip(scope.row)"
              placement="top"
            >
              <el-button
                type="primary"
                size="small"
                @click="handleAdjustSingle(scope.row)"
                v-hasPermi="['management:student-course-hours:adjust']"
                :disabled="!canAdjustCourseHours(scope.row)"
              >
                调整课时
              </el-button>
            </el-tooltip>
            <el-tooltip 
              :content="getConsumptionTooltip(scope.row)"
              placement="top"
            >
              <el-button
                type="danger"
                size="small"
                @click="handleRecordConsumption(scope.row)"
                v-hasPermi="['management:student-course-hours:consume']"
                :disabled="!canRecordConsumption(scope.row)"
              >
                录入课消
              </el-button>
            </el-tooltip>
            <el-button
              type="success"
              size="small"
              @click="handleViewStudentConsumption(scope.row)"
              v-hasPermi="['management:student-course-hours:consumption']"
            >
              课消记录
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleViewStudentHistory(scope.row)"
              v-hasPermi="['management:student-course-hours:history']"
            >
              调整历史
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 课时调整对话框 -->
    <AdjustDialog
      v-model:visible="adjustDialogVisible"
      :student-info="selectedStudent"
      @success="handleAdjustSuccess"
    />

    <!-- 课时导入对话框 -->
    <ImportDialog v-model:visible="importDialogVisible" @success="handleImportSuccess" />

    <!-- 新增课时对话框 -->
    <AddCourseHoursDialog v-model:visible="addCourseHoursDialogVisible" @success="handleAddCourseHoursSuccess" />

    <!-- 课消记录对话框 -->
    <CourseConsumptionDialog
      v-model:visible="consumptionDialogVisible"
      :student-info="selectedStudent"
      :course-hours-id="selectedCourseHoursId"
    />

    <!-- 调整历史对话框 -->
    <CourseAdjustmentHistoryDialog
      v-model:visible="historyDialogVisible"
      :student-info="selectedStudent"
      :course-hours-id="selectedStudent?.id"
    />

    <!-- 录入课消对话框 -->
    <RecordConsumptionDialog
      v-model:visible="recordConsumptionDialogVisible"
      :course-hours-info="selectedStudent"
      @success="handleRecordConsumptionSuccess"
    />
  </div>
</template>

<script setup name="StudentCourseHoursManagement">
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { parseTime } from "@/utils/ruoyi";
import { listStudentCourseHours, exportStudentCourseHours } from "@/api/management/studentCourseHours";
import { useRouter } from "vue-router";
import AdjustDialog from "./components/AdjustDialog.vue";
import ImportDialog from "./components/ImportDialog.vue";
import AddCourseHoursDialog from "./components/AddCourseHoursDialog.vue";
import RecordConsumptionDialog from "./components/RecordConsumptionDialog.vue";
import CourseConsumptionDialog from "@/components/CourseConsumptionDialog.vue";
import CourseAdjustmentHistoryDialog from "@/components/CourseAdjustmentHistoryDialog.vue";

// 路由实例
const router = useRouter();

// 响应式数据
const loading = ref(true);
const exportLoading = ref(false);
const courseHoursList = ref([]);
const total = ref(0);
const adjustDialogVisible = ref(false);
const importDialogVisible = ref(false);
const addCourseHoursDialogVisible = ref(false);
const consumptionDialogVisible = ref(false);
const historyDialogVisible = ref(false);
const recordConsumptionDialogVisible = ref(false);
const selectedStudent = ref(null);
const selectedCourseHoursId = ref("");

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  queryParams.value.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  studentName: "",
  studentPhone: "",
  subject: "",
  specification: "",
  nature: "",
  status: "active", // 默认查询生效状态的课时包
  teacherName: "",
  teacherPhone: "",
});

// 查询表单引用
const queryRef = ref();

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    const response = await listStudentCourseHours(queryParams.value);
    courseHoursList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    console.error("获取课时列表失败:", error);
    ElMessage.error("获取课时列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  queryRef.value?.resetFields();
  handleQuery();
};

// 导入课时
const handleImport = () => {
  importDialogVisible.value = true;
};

// 新增课时
const handleAddCourseHours = () => {
  addCourseHoursDialogVisible.value = true;
};

// 检查是否为来自订单的课时包
const isOrderBasedCourseHours = (courseHours) => {
  // 检查是否有关联的订单ID
  if (courseHours.orderId && courseHours.orderId.trim() !== '') {
    return true;
  }
  
  // 检查课时来源类型是否为订单
  if (courseHours.sourceType === '订单') {
    return true;
  }
  
  return false;
};

// 检查课时包是否失效
const isCourseHoursInactive = (courseHours) => {
  return courseHours.status === 'inactive';
};

// 检查是否可以调整课时（不能是来自订单的或失效的课时包）
const canAdjustCourseHours = (courseHours) => {
  return !isOrderBasedCourseHours(courseHours) && !isCourseHoursInactive(courseHours);
};

// 检查是否可以录入课消（不能是失效的课时包，且要有剩余课时）
const canRecordConsumption = (courseHours) => {
  return !isCourseHoursInactive(courseHours) && (courseHours.remainingHours || 0) > 0;
};

// 获取调整课时的提示信息
const getAdjustTooltip = (courseHours) => {
  if (isOrderBasedCourseHours(courseHours)) {
    return '来自订单的课时包禁止调整';
  }
  if (isCourseHoursInactive(courseHours)) {
    return '失效的课时包禁止调整';
  }
  return '调整课时';
};

// 获取录入课消的提示信息
const getConsumptionTooltip = (courseHours) => {
  if (isCourseHoursInactive(courseHours)) {
    return '失效的课时包禁止录入课消';
  }
  if ((courseHours.remainingHours || 0) <= 0) {
    return '剩余课时不足，无法录入课消';
  }
  return '录入课消';
};

// 单个学生课时调整
const handleAdjustSingle = (row) => {
  // 检查是否可以调整
  if (!canAdjustCourseHours(row)) {
    if (isOrderBasedCourseHours(row)) {
      ElMessage.warning('来自订单的课时包禁止调整课时');
    } else if (isCourseHoursInactive(row)) {
      ElMessage.warning('失效的课时包禁止调整课时');
    }
    return;
  }
  
  selectedStudent.value = row;
  adjustDialogVisible.value = true;
};

// 查看学生课消记录（对话框模式，锁定到当前课时记录）
const handleViewStudentConsumption = (row) => {
  selectedStudent.value = row;
  selectedCourseHoursId.value = row.id; // 锁定到当前课时记录
  consumptionDialogVisible.value = true;
};

// 查看所有课消记录（打开对话框）
const handleViewAllConsumption = () => {
  selectedStudent.value = null; // 不锁定特定学生，查看所有课消记录
  consumptionDialogVisible.value = true;
};

// 查看所有调整历史（跳转页面）
const handleViewAllHistory = () => {
  router.push("CourseAdjustmentHistoryQuery");
};

// 查看学生调整历史（对话框模式）
const handleViewStudentHistory = (row) => {
  selectedStudent.value = row;
  historyDialogVisible.value = true;
};

// 录入课消
const handleRecordConsumption = (row) => {
  // 检查是否可以录入课消
  if (!canRecordConsumption(row)) {
    if (isCourseHoursInactive(row)) {
      ElMessage.warning('失效的课时包禁止录入课消');
    } else if ((row.remainingHours || 0) <= 0) {
      ElMessage.warning('剩余课时不足，无法录入课消');
    }
    return;
  }
  
  selectedStudent.value = row;
  recordConsumptionDialogVisible.value = true;
};

// 调整成功回调
const handleAdjustSuccess = () => {
  getList();
};

// 导入成功回调
const handleImportSuccess = () => {
  getList();
};

// 新增课时成功回调
const handleAddCourseHoursSuccess = () => {
  getList();
};

// 录入课消成功回调
const handleRecordConsumptionSuccess = () => {
  getList();
};

// 导出课时数据
const handleExport = async () => {
  try {
    exportLoading.value = true;

    // 使用当前查询条件进行导出
    const exportParams = {
      studentName: queryParams.value.studentName,
      studentPhone: queryParams.value.studentPhone,
      subject: queryParams.value.subject,
      specification: queryParams.value.specification,
      nature: queryParams.value.nature,
      status: queryParams.value.status,
      teacherName: queryParams.value.teacherName,
      teacherPhone: queryParams.value.teacherPhone
    };

    const response = await exportStudentCourseHours(exportParams);

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 生成文件名
    const now = new Date();
    const timestamp = now.getFullYear() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0') + '_' +
      String(now.getHours()).padStart(2, '0') +
      String(now.getMinutes()).padStart(2, '0') +
      String(now.getSeconds()).padStart(2, '0');

    link.download = `学生课时数据_${timestamp}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败: ' + (error.message || '未知错误'));
  } finally {
    exportLoading.value = false;
  }
};

// 监听对话框关闭，清空选中的课时id
watch(consumptionDialogVisible, (newVal) => {
  if (!newVal) {
    selectedCourseHoursId.value = "";
  }
});

// 初始化
onMounted(() => {
  getList();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-primary {
  color: #409eff;
  font-weight: bold;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.hours-detail {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.total-hours {
  font-size: 12px;
  color: #606266;
}

.remaining-hours {
  font-size: 14px;
  font-weight: bold;
  color: #409eff;
}

.low-hours {
  color: #f56c6c !important;
}

.text-info {
  color: #909399;
  font-size: 13px;
}

.text-muted {
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

.source-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.order-id {
  font-size: 10px;
  color: #909399;
  text-align: center;
}
</style>
