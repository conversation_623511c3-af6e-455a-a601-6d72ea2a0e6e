<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
      <el-form-item label="学生ID" prop="studentId">
        <el-input
          v-model="queryParams.studentId"
          placeholder="请输入学生ID"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="学科" prop="subject">
        <el-select v-model="queryParams.subject" placeholder="请选择学科" clearable style="width: 120px" @change="handleSubjectChange">
          <el-option label="英语" value="英语" />
          <el-option label="语文" value="语文" />
          <el-option label="数学" value="数学" />
          <el-option label="物理" value="物理" />
          <el-option label="化学" value="化学" />
        </el-select>
      </el-form-item>
      <el-form-item label="课型" prop="specification">
        <el-select v-model="queryParams.specification" placeholder="请选择课型" clearable style="width: 120px">
          <el-option
            v-for="option in availableSpecifications"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课型" prop="specification">
        <el-select v-model="queryParams.specification" placeholder="请选择课型" clearable style="width: 120px">
          <el-option label="正式课" value="正式课" />
          <el-option label="试听课" value="试听课" />
        </el-select>
      </el-form-item>
      <el-form-item label="调整类型" prop="adjustmentType">
        <el-select v-model="queryParams.adjustmentType" placeholder="请选择调整类型" clearable style="width: 120px">
          <el-option label="增加" value="increase" />
          <el-option label="减少" value="decrease" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作人" prop="operatorName">
        <el-input
          v-model="queryParams.operatorName"
          placeholder="请输入操作人姓名"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="调整时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="historyList" border max-height="400">
      <el-table-column label="学生姓名" prop="studentName" width="120" />
      <el-table-column label="学科" prop="subject" width="80" />
      <el-table-column label="课型" prop="specification" width="80" />
      <el-table-column label="性质" prop="nature" width="80" />
      <el-table-column label="调整类型" prop="adjustmentType" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.adjustmentType === 'increase' ? 'success' : 'warning'">
            {{ scope.row.adjustmentType === 'increase' ? '增加' : '减少' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="总调整" prop="adjustmentHours" width="100" align="right">
        <template #default="scope">
          <span :class="scope.row.adjustmentType === 'increase' ? 'text-success' : 'text-warning'">
            {{ scope.row.adjustmentType === 'increase' ? '+' : '-' }}{{ scope.row.adjustmentHours }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="调整详情" width="200" align="center">
        <template #default="scope">
          <div class="adjustment-details">
            <div v-if="scope.row.purchasedHoursAdjustment !== 0" class="detail-item">
              <span class="detail-label">购买:</span>
              <span :class="scope.row.purchasedHoursAdjustment > 0 ? 'text-success' : 'text-warning'">
                {{ scope.row.purchasedHoursAdjustment > 0 ? '+' : '' }}{{ scope.row.purchasedHoursAdjustment }}
              </span>
            </div>
            <div v-if="scope.row.giftHoursAdjustment !== 0" class="detail-item">
              <span class="detail-label">赠送:</span>
              <span :class="scope.row.giftHoursAdjustment > 0 ? 'text-success' : 'text-warning'">
                {{ scope.row.giftHoursAdjustment > 0 ? '+' : '' }}{{ scope.row.giftHoursAdjustment }}
              </span>
            </div>
            <div v-if="scope.row.purchasedHoursAdjustment === 0 && scope.row.giftHoursAdjustment === 0" class="no-detail">
              <span class="text-muted">无详情</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="调整前" width="150" align="center">
        <template #default="scope">
          <div class="before-after-info">
            <div class="info-item">
              <span class="info-label">总:</span>
              <span>{{ scope.row.beforeTotalHours }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">剩余:</span>
              <span>{{ scope.row.beforeRemainingHours }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">购买:</span>
              <span>{{ scope.row.beforePurchasedHours || 0 }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">赠送:</span>
              <span>{{ scope.row.beforeGiftHours || 0 }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="调整后" width="150" align="center">
        <template #default="scope">
          <div class="before-after-info">
            <div class="info-item">
              <span class="info-label">总:</span>
              <span>{{ scope.row.afterTotalHours }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">剩余:</span>
              <span>{{ scope.row.afterRemainingHours }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">购买:</span>
              <span>{{ scope.row.afterPurchasedHours || 0 }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">赠送:</span>
              <span>{{ scope.row.afterGiftHours || 0 }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="调整原因" prop="adjustmentReason" min-width="200" show-overflow-tooltip />
      <el-table-column label="操作人" prop="operatorName" width="120" />
      <el-table-column label="调整时间" prop="adjustmentTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.adjustmentTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      style="margin-top: 20px"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { parseTime } from '@/utils/ruoyi'
import { listAdjustmentHistory } from '@/api/management/studentCourseHours'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const dialogTitle = computed(() => {
  return props.studentInfo
    ? `课时调整历史 - ${props.studentInfo.studentName} (${props.studentInfo.subject} - ${props.studentInfo.specification})`
    : '课时调整历史'
})

const loading = ref(false)
const historyList = ref([])
const total = ref(0)
const dateRange = ref([])

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  queryParams.value.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  studentId: '',
  subject: '',
  specification: '',
  adjustmentType: '',
  operatorName: '',
  startTime: '',
  endTime: ''
})

// 查询表单引用
const queryRef = ref()

// 监听时间范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    queryParams.value.startTime = newVal[0]
    queryParams.value.endTime = newVal[1]
  } else {
    queryParams.value.startTime = ''
    queryParams.value.endTime = ''
  }
})

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    getList()
  }
})

// 监听对话框打开和学生信息变化
watch(() => [props.visible, props.studentInfo], ([visible, studentInfo]) => {
  if (visible) {
    if (studentInfo) {
      // 如果传入了学生信息，自动设置查询条件
      queryParams.value.studentId = studentInfo.studentId
      queryParams.value.subject = studentInfo.subject
      queryParams.value.specification = studentInfo.specification
    } else {
      // 清空学生相关的查询条件
      queryParams.value.studentId = null
      queryParams.value.subject = ''
      queryParams.value.specification = ''
    }
    getList()
  }
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const response = await listAdjustmentHistory(queryParams.value)
    historyList.value = response.rows
    total.value = response.total
  } catch (error) {
    console.error('获取调整历史失败:', error)
    ElMessage.error('获取调整历史失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryRef.value?.resetFields()
  dateRange.value = []
  queryParams.value.startTime = ''
  queryParams.value.endTime = ''
  handleQuery()
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.adjustment-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.detail-label {
  color: #606266;
  margin-right: 4px;
  min-width: 30px;
}

.no-detail {
  text-align: center;
}

.before-after-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.info-label {
  color: #606266;
  margin-right: 4px;
  min-width: 30px;
}
</style>
