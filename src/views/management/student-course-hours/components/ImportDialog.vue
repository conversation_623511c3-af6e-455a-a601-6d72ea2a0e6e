<template>
  <el-dialog
    v-model="dialogVisible"
    title="课时数据导入"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-content">
      <el-alert
        title="导入说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>
            <p>1. 请按照模板格式准备Excel文件，包含以下列：老师姓名、手机号、学生姓名、手机号、学科、课型、购买课时、剩余购买课时、赠送课时、剩余赠送课时、单价</p>
            <p>2. 如果老师或学生不存在，系统会自动创建</p>
            <p>3. <strong>每次导入都会创建新的课时记录</strong>，不会更新现有记录，支持多批次课时管理</p>
            <p>4. 课消时按照先入库的课时先消费（FIFO原则），消完一批再消下一批</p>
            <p>5. 系统会自动计算总课时（购买课时+赠送课时）和消耗课时等字段</p>
            <p>6. 单价字段用于记录每课时的费用，可选填</p>
            <p>7. 支持的文件格式：.xlsx、.xls</p>
            <p><strong>8. 导入过程可能需要较长时间，请耐心等待，不要关闭页面</strong></p>
          </div>
        </template>
      </el-alert>

      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
          accept=".xlsx,.xls"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>

        <div class="upload-actions">
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            开始导入
          </el-button>
          <el-button @click="downloadTemplateFile">下载模板</el-button>
          <el-button @click="resetUpload">重置</el-button>
        </div>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="result-section">
        <el-card>
          <template #header>
            <span>导入结果</span>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="总行数">{{ importResult.totalRows }}</el-descriptions-item>
            <el-descriptions-item label="成功行数">{{ importResult.successRows }}</el-descriptions-item>
            <el-descriptions-item label="失败行数">{{ importResult.failedRows }}</el-descriptions-item>
            <el-descriptions-item label="创建老师">{{ importResult.createdTeachers }}</el-descriptions-item>
            <el-descriptions-item label="创建学生">{{ importResult.createdStudents }}</el-descriptions-item>
            <el-descriptions-item label="创建师生关系">{{ importResult.createdRelations }}</el-descriptions-item>
            <el-descriptions-item label="创建课时记录">{{ importResult.createdHours }}</el-descriptions-item>
          </el-descriptions>

          <div v-if="importResult.detailMessage" class="detail-message">
            <h4>详细信息：</h4>
            <pre>{{ importResult.detailMessage }}</pre>
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

import { importCourseHours, downloadTemplate } from '@/api/management/studentCourseHours'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const uploadRef = ref()
const uploading = ref(false)
const overrideExisting = ref(false)
const importResult = ref(null)
const fileList = ref([])
const selectedFile = ref(null)

// 文件选择处理
const handleFileChange = (file) => {
  selectedFile.value = file.raw
  fileList.value = [file]
}

// 提交上传
const submitUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请先选择要上传的文件')
    return
  }

  uploading.value = true
  try {
    const response = await importCourseHours(selectedFile.value, overrideExisting.value)

    if (response.code === 200) {
      ElMessage.success('导入成功!')
      importResult.value = response.data
      emit('success')
    } else {
      ElMessage.error(response.msg || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败: ' + (error.message || '未知错误'))
  } finally {
    uploading.value = false
  }
}

// 上传前检查
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }

  return false // 阻止自动上传，我们手动处理
}



// 下载模板
const downloadTemplateFile = async () => {
  try {
    const response = await downloadTemplate()
    // 创建下载链接
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '课时导入模板.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  }
}

// 重置上传
const resetUpload = () => {
  uploadRef.value?.clearFiles()
  fileList.value = []
  selectedFile.value = null
  importResult.value = null
  overrideExisting.value = false
}

// 关闭对话框
const handleClose = () => {
  resetUpload()
  emit('update:visible', false)
}
</script>

<style scoped>
.import-content {
  max-width: 100%;
}

.upload-section {
  margin: 20px 0;
}

.upload-options {
  margin: 15px 0;
}

.upload-actions {
  margin: 15px 0;
}

.result-section {
  margin-top: 20px;
}

.detail-message {
  margin-top: 15px;
}

.detail-message pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.dialog-footer {
  text-align: right;
}
</style>
