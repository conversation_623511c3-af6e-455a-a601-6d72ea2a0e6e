<template>
  <el-dialog
    v-model="dialogVisible"
    title="新增课时"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="100px"
      v-loading="submitting"
    >
      <el-form-item label="学生" prop="studentId">
        <el-select
          v-model="form.studentId"
          placeholder="请搜索选择学生"
          filterable
          remote
          :remote-method="searchStudents"
          :loading="studentsLoading"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="student in studentOptions"
            :key="student.id"
            :label="`${student.name}（${student.phone}）`"
            :value="student.id"
          >
            <div class="student-option">
              <span class="student-name">{{ student.name }}</span>
              <span class="student-phone">({{ student.phone }})</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="学科" prop="subject">
        <el-select v-model="form.subject" placeholder="请选择学科" style="width: 100%" @change="handleSubjectChange">
          <el-option label="英语" value="英语" />
          <el-option label="语文" value="语文" />
          <el-option label="数学" value="数学" />
          <el-option label="物理" value="物理" />
          <el-option label="化学" value="化学" />
        </el-select>
      </el-form-item>

      <el-form-item label="课型" prop="specification">
        <el-select v-model="form.specification" placeholder="请选择课型" style="width: 100%">
          <el-option
            v-for="option in availableSpecifications"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="性质" prop="nature">
        <el-select v-model="form.nature" placeholder="请选择性质" style="width: 100%">
          <el-option label="正式课" value="正式课" />
          <el-option label="试听课" value="试听课" />
        </el-select>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="购买课时" prop="purchasedHours">
            <el-input-number
              v-model="form.purchasedHours"
              :min="0"
              :precision="2"
              :step="0.5"
              placeholder="请输入购买课时"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="赠送课时" prop="giftHours">
            <el-input-number
              v-model="form.giftHours"
              :min="0"
              :precision="2"
              :step="0.5"
              placeholder="请输入赠送课时"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="单价" prop="unitPrice">
        <el-input-number
          v-model="form.unitPrice"
          :min="0"
          :precision="2"
          :step="1"
          placeholder="请输入单价"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="原因说明" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入新增课时的原因说明"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getStudentsApi } from '@/api/management/student'
import { addCourseHours } from '@/api/management/studentCourseHours'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const formRef = ref()
const submitting = ref(false)
const studentsLoading = ref(false)
const studentOptions = ref([])

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(englishSpecifications) // 默认显示英语课型

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  form.value.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 表单数据
const form = ref({
  studentId: '',
  subject: '英语',
  specification: '',
  nature: '正式课',
  purchasedHours: 0,
  giftHours: 0,
  unitPrice: 0,
  reason: ''
})

// 表单验证规则
const formRules = {
  studentId: [
    { required: true, message: '请选择学生', trigger: 'change' }
  ],
  subject: [
    { required: true, message: '请选择学科', trigger: 'change' }
  ],
  specification: [
    { required: true, message: '请选择课型', trigger: 'change' }
  ],
  nature: [
    { required: true, message: '请选择性质', trigger: 'change' }
  ],
  purchasedHours: [
    { required: true, message: '请输入购买课时', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value <= 0 && form.value.giftHours <= 0) {
          callback(new Error('购买课时和赠送课时至少有一项大于0'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  giftHours: [
    { required: true, message: '请输入赠送课时', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value <= 0 && form.value.purchasedHours <= 0) {
          callback(new Error('购买课时和赠送课时至少有一项大于0'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value < 0) {
          callback(new Error('单价必须大于等于0'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: '请输入原因说明', trigger: 'blur' }
  ]
}

// 搜索学生
const searchStudents = async (query) => {
  if (!query) {
    studentOptions.value = []
    return
  }

  studentsLoading.value = true
  try {
    const { data } = await getStudentsApi({
      keyword: query,
      pageSize: 20
    })
    studentOptions.value = data?.records || []
  } catch (error) {
    console.error('搜索学生失败:', error)
    ElMessage.error('搜索学生失败')
  } finally {
    studentsLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    studentId: '',
    subject: '英语',
    specification: '',
    nature: '正式课',
    purchasedHours: 0,
    giftHours: 0,
    unitPrice: 0,
    reason: ''
  }
  studentOptions.value = []
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitting.value = true

    const requestData = {
      studentId: form.value.studentId,
      subject: form.value.subject,
      specification: form.value.specification,
      nature: form.value.nature,
      purchasedHours: form.value.purchasedHours,
      giftHours: form.value.giftHours,
      unitPrice: form.value.unitPrice,
      reason: form.value.reason
    }

    await addCourseHours(requestData)

    ElMessage.success('新增课时成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('新增课时失败:', error)
    ElMessage.error('新增课时失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.student-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.student-name {
  font-weight: bold;
}

.student-phone {
  color: #999;
  font-size: 12px;
}
</style>
