<template>
  <el-dialog
    v-model="dialogVisible"
    title="录入课消"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="!courseHoursInfo" class="no-course-info">
      <el-alert
        title="提示"
        description="课消录入只能从课时管理列表的操作按钮进入"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <div v-else>
      <!-- 失效课时包警告 -->
      <el-alert
        v-if="isCourseHoursInactive(courseHoursInfo)"
        title="警告"
        description="此课时包已失效，禁止录入课消"
        type="error"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />
      
      <!-- 课时包信息（只读显示） -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">课时包信息</span>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="学生姓名">{{ courseHoursInfo.studentName }}</el-descriptions-item>
          <el-descriptions-item label="学生手机">{{ courseHoursInfo.studentPhone }}</el-descriptions-item>
          <el-descriptions-item label="学科">{{ courseHoursInfo.subject }}</el-descriptions-item>
          <el-descriptions-item label="课型">{{ courseHoursInfo.specification }}</el-descriptions-item>
          <el-descriptions-item label="性质">{{ courseHoursInfo.nature }}</el-descriptions-item>
          <el-descriptions-item label="批次号">{{ courseHoursInfo.batchNo }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 当前课时统计 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">当前课时统计</span>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="总课时">
            <span class="hours-number">{{ courseHoursInfo.totalHours || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="剩余课时">
            <span class="hours-number" :class="{ 'low-hours': (courseHoursInfo.remainingHours || 0) <= 5 }">
              {{ courseHoursInfo.remainingHours || 0 }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="已消耗课时">
            <span class="hours-number consumed">{{ courseHoursInfo.consumedTotalHours || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="单价">
            <span class="hours-number">¥{{ courseHoursInfo.unitPrice || 0 }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 课消录入表单 -->
      <el-card class="consume-card" shadow="never">
        <template #header>
          <span class="card-title">录入课消</span>
        </template>

        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          :disabled="isCourseHoursInactive(courseHoursInfo)"
        >
          <el-form-item label="消费课时数" prop="consumedHours">
            <el-input-number
              v-model="form.consumedHours"
              :min="0.01"
              :max="courseHoursInfo.remainingHours || 0"
              :precision="2"
              :step="0.5"
              placeholder="请输入消费的课时数"
              style="width: 100%"
            />
            <div class="form-tip">
              可消费课时：{{ courseHoursInfo.remainingHours || 0 }} 课时
            </div>
          </el-form-item>

          <el-form-item label="老师" prop="teacherId">
            <el-select
              v-model="form.teacherId"
              placeholder="请选择授课老师"
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="teacher in teacherList"
                :key="teacher.id"
                :label="teacher.name"
                :value="teacher.id"
              />
            </el-select>
            <div class="form-tip">选择授课老师（可选）</div>
          </el-form-item>

          <el-form-item label="课程ID" prop="courseId">
            <el-input
              v-model="form.courseId"
              placeholder="请输入关联的课程ID（可选）"
              maxlength="64"
            />
            <div class="form-tip">关联的课程ID，用于追踪课消来源</div>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入课消备注信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <!-- 课消预览 -->
          <el-form-item label="课消预览" v-if="form.consumedHours > 0">
            <el-alert
              :title="consumptionPreview"
              type="warning"
              :closable="false"
              show-icon
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="!canRecordConsumption"
        >
          确定录入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { recordCourseConsumption } from '@/api/management/studentCourseHours'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  courseHoursInfo: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const formRef = ref()
const submitting = ref(false)
const teacherList = ref([]) // 老师列表，可以从API获取

// 表单数据
const form = ref({
  consumedHours: 0,
  teacherId: '',
  courseId: '',
  remark: ''
})

// 表单验证规则
const rules = {
  consumedHours: [
    { required: true, message: '请输入消费课时数', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value <= 0) {
          callback(new Error('消费课时数必须大于0'))
        } else if (props.courseHoursInfo && value > props.courseHoursInfo.remainingHours) {
          callback(new Error('消费课时数不能大于剩余课时'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  remark: [
    { required: true, message: '请输入课消备注', trigger: 'blur' },
    { min: 2, max: 500, message: '备注长度在2-500个字符', trigger: 'blur' }
  ]
}

// 检查课时包是否失效
const isCourseHoursInactive = (courseHours) => {
  if (!courseHours) return false;
  return courseHours.status === 'inactive';
};

// 计算属性
const consumptionPreview = computed(() => {
  if (!form.value.consumedHours || !props.courseHoursInfo) return ''
  
  const remaining = props.courseHoursInfo.remainingHours - form.value.consumedHours
  return `将消费 ${form.value.consumedHours} 课时，剩余 ${remaining} 课时`
})

// 检查是否可以录入课消
const canRecordConsumption = computed(() => {
  return props.courseHoursInfo && 
         !isCourseHoursInactive(props.courseHoursInfo) &&
         form.value.consumedHours > 0 &&
         form.value.consumedHours <= (props.courseHoursInfo.remainingHours || 0)
})

// 监听对话框打开，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  form.value = {
    consumedHours: 0,
    teacherId: '',
    courseId: '',
    remark: ''
  }
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  if (!props.courseHoursInfo) {
    ElMessage.error('请从课时管理列表进入课消录入功能')
    return
  }

  if (isCourseHoursInactive(props.courseHoursInfo)) {
    ElMessage.error('失效的课时包禁止录入课消')
    return
  }

  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitting.value = true

    const requestData = {
      courseHoursId: props.courseHoursInfo.id,
      studentId: props.courseHoursInfo.studentId,
      subject: props.courseHoursInfo.subject,
      specification: props.courseHoursInfo.specification,
      nature: props.courseHoursInfo.nature,
      consumedHours: form.value.consumedHours,
      teacherId: form.value.teacherId || null,
      courseId: form.value.courseId || null,
      remark: form.value.remark
    }

    await recordCourseConsumption(requestData)

    ElMessage.success('课消录入成功')
    emit('success')
    handleClose()

  } catch (error) {
    console.error('课消录入失败:', error)
    ElMessage.error('课消录入失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}
</script>

<style scoped>
.no-course-info {
  text-align: center;
  padding: 40px 20px;
}

.info-card {
  margin-bottom: 20px;
}

.consume-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.hours-number {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.hours-number.consumed {
  color: #f56c6c;
}

.low-hours {
  color: #f56c6c !important;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  text-align: right;
}
</style>
