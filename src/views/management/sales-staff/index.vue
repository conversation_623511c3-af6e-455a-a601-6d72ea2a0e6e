<template>
  <div class="sales-staff-management">
    <!-- 搜索表单 -->
    <el-card shadow="never" class="search-card">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="姓名">
          <el-input
            v-model="searchForm.salesName"
            placeholder="请输入姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="销售组">
          <el-select
            v-model="searchForm.groupId"
            placeholder="请选择销售组"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="group in groupOptions"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="正常" value="active" />
            <el-option label="停用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" size="small" @click="handleCreate" v-hasPermi="['sales:staff:add']">
            <el-icon><Plus /></el-icon>
            新建销售人员
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格容器 -->
    <div class="table-container">

      <!-- 销售人员列表 -->
      <el-card shadow="never" class="table-card">
        <div class="table-wrapper">
          <el-table
            v-loading="loading"
            :data="salesStaffList"
            :max-height="tableMaxHeight"
            @selection-change="handleSelectionChange"
            stripe
            border
          >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="nickName" label="姓名" min-width="120" />
        <el-table-column prop="phonenumber" label="手机号" min-width="130" />
        <el-table-column prop="groupName" label="所属销售组" min-width="150" show-overflow-tooltip />
        <el-table-column prop="studentCount" label="负责学生数" width="120" align="center" />
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
              {{ row.status === 'active' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="180" />
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleView(row)"
              v-hasPermi="['sales:staff:query']"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleEdit(row)"
              v-hasPermi="['sales:staff:edit']"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              link
              @click="handleResetPassword(row)"
              v-hasPermi="['sales:staff:resetPwd']"
            >
              重置密码
            </el-button>
            <el-button
              :type="row.status === '0' ? 'warning' : 'success'"
              link
              @click="handleChangeStatus(row)"
              v-hasPermi="['sales:staff:edit']"
            >
              {{ row.status === 'active' ? '停用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
              v-hasPermi="['sales:staff:remove']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 固定分页 -->
    <div class="fixed-pagination">
      <el-pagination
        v-model:current-page="searchForm.pageNum"
        v-model:page-size="searchForm.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSearch"
        @current-change="handleSearch"
      />
    </div>

    <!-- 创建/编辑销售人员对话框 -->
    <SalesStaffDialog
      v-model="showDialog"
      :staff="selectedStaff"
      @success="handleDialogSuccess"
    />

    <!-- 销售人员详情对话框 -->
    <SalesStaffDetailDialog
      v-model="showDetailDialog"
      :staff-id="selectedStaffId"
    />
  </div>
</template>

<script setup name="sales-staff">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import {
  getSalesStaffListApi,
  getSalesStaffDetailApi,
  deleteSalesStaffApi,
  resetSalesStaffPasswordApi,
  changeSalesStaffStatusApi
} from '@/api/management/salesStaff'
import { getSalesGroupListApi } from '@/api/management/salesGroup'
import { parseTime } from '@/utils/ruoyi'
import SalesStaffDialog from './components/SalesStaffDialog.vue'
import SalesStaffDetailDialog from './components/SalesStaffDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const salesStaffList = ref([])
const total = ref(0)
const selectedRows = ref([])
const groupOptions = ref([])

// 计算表格最大高度
const tableMaxHeight = ref(600)

// 对话框状态
const showDialog = ref(false)
const showDetailDialog = ref(false)
const selectedStaff = ref(null)
const selectedStaffId = ref(null)

// 搜索表单
const searchForm = reactive({
  salesName: '', // 修正字段名
  phone: '',
  groupId: '',
  roleType: '', // 修正字段名
  status: 'active',
  pageNum: 1,
  pageSize: 20
})

// 计算表格高度
const calculateTableHeight = () => {
  const windowHeight = window.innerHeight
  const usedHeight = 40 + 120 + 80 + 40 // 大概的固定高度
  tableMaxHeight.value = Math.max(400, windowHeight - usedHeight)
}

// 页面初始化
onMounted(() => {
  handleSearch()
  loadGroupOptions()
  calculateTableHeight()

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})

// 搜索
const handleSearch = async () => {
  loading.value = true
  try {
    const response = await getSalesStaffListApi(searchForm)
    // 后端返回的是IPage格式，需要从data中获取
    const pageData = response.data || response
    salesStaffList.value = pageData.records || pageData.rows || []
    total.value = pageData.total || 0
  } catch (error) {
    ElMessage.error('获取销售人员列表失败')
    console.error('获取销售人员列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    salesName: '',
    phone: '',
    groupId: '',
    roleType: '',
    status: 'active',
    pageNum: 1,
    pageSize: 20
  })
  handleSearch()
}

// 加载销售组选项
const loadGroupOptions = async () => {
  try {
    const response = await getSalesGroupListApi({ pageNum: 1, pageSize: 1000 })
    // 兼容不同的响应格式
    const pageData = response.data || response
    groupOptions.value = pageData.records || pageData.rows || []
  } catch (error) {
    console.error('获取销售组选项失败:', error)
    groupOptions.value = []
  }
}



// 创建销售人员
const handleCreate = () => {
  selectedStaff.value = null
  showDialog.value = true
}

// 编辑销售人员
const handleEdit = async (row) => {
  try {
    console.log('开始编辑销售人员，获取最新数据:', row.userId)
    // 重新获取销售人员详情，确保数据是最新的
    const response = await getSalesStaffDetailApi(row.userId)
    console.log('获取到的销售人员详情:', response.data)
    selectedStaff.value = response.data
    showDialog.value = true
  } catch (error) {
    console.error('获取销售人员详情失败:', error)
    ElMessage.error('获取销售人员信息失败')
  }
}

// 查看销售人员详情
const handleView = (row) => {
  selectedStaffId.value = row.userId
  showDetailDialog.value = true
}

// 重置密码
const handleResetPassword = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置"${row.nickName}"的密码吗？重置后密码为：654321`,
      '重置密码确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await resetSalesStaffPasswordApi(row.userId)
    ElMessage.success('密码重置成功，新密码为：654321')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('密码重置失败')
    }
  }
}

// 更改状态
const handleChangeStatus = async (row) => {
  const newStatus = row.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '停用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}"${row.nickName}"吗？`,
      `${action}确认`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await changeSalesStaffStatusApi(row.userId, newStatus)
    ElMessage.success(`${action}成功`)
    handleSearch()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除销售人员
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除销售人员"${row.nickName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteSalesStaffApi(row.userId)
    ElMessage.success('删除成功')
    handleSearch()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 对话框成功回调
const handleDialogSuccess = () => {
  showDialog.value = false
  handleSearch()
}
</script>

<style scoped>
.sales-staff-management {
  padding: 20px 20px 0 20px;
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 搜索条件样式 */
.search-card {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.search-card .el-form {
  padding: 8px 0;
}

.search-card .el-form-item {
  margin-bottom: 12px;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  padding-bottom: 80px; /* 为固定分页留出空间 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  overflow: hidden;
  min-height: 0;
}

.table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.table-wrapper .el-table {
  width: 100% !important;
}

.table-wrapper .el-table .el-table__cell {
  padding: 8px 12px;
}

/* 固定分页样式 */
.fixed-pagination {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  align-items: center;
}
</style>
