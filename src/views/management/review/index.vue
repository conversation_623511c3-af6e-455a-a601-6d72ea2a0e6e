<template>
  <div class="review-management-container">
    <!-- 高级搜索 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键字">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入复习任务名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="老师">
          <el-select
            v-model="searchForm.teacherId"
            placeholder="请选择老师"
            clearable
            filterable
            style="width: 150px"
          >
            <el-option
              v-for="teacher in teachers"
              :key="teacher.id"
              :label="teacher.name"
              :value="teacher.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学生">
          <el-select
            v-model="searchForm.studentId"
            placeholder="请选择学生"
            clearable
            filterable
            style="width: 150px"
          >
            <el-option
              v-for="student in students"
              :key="student.id"
              :label="student.name"
              :value="student.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="复习类型">
          <el-select
            v-model="searchForm.reviewTypes"
            placeholder="请选择复习类型"
            multiple
            clearable
            style="width: 180px"
          >
            <el-option label="D2 (2天后)" value="D2" />
            <el-option label="D4 (4天后)" value="D4" />
            <el-option label="D7 (7天后)" value="D7" />
            <el-option label="D14 (14天后)" value="D14" />
            <el-option label="D21 (21天后)" value="D21" />
          </el-select>
        </el-form-item>
        <el-form-item label="复习状态">
          <el-select
            v-model="searchForm.statuses"
            placeholder="请选择复习状态"
            multiple
            clearable
            style="width: 180px"
          >
            <el-option label="待开始" value="待开始" />
            <el-option label="进行中" value="进行中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已跳过" value="已跳过" />
          </el-select>
        </el-form-item>
        <el-form-item label="复习时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 复习列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="reviews"
        stripe
        style="width: 100%"
        :height="tableHeight"
      >
        <el-table-column
          prop="name"
          label="复习任务"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="studentName" label="学生" min-width="80">
          <template #default="{ row }">
            <span>{{ row.studentName || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reviewType" label="复习类型" min-width="90">
          <template #default="{ row }">
            <el-tag :type="getReviewTypeTagType(row.reviewType)" size="small">
              {{ row.reviewType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scheduledTime" label="计划时间" min-width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.scheduledTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="wordIds" label="单词数量" min-width="90" align="center">
          <template #default="{ row }">
            <span>{{ row.wordIds?.length || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="actualStartTime" label="开始时间" min-width="150">
          <template #default="{ row }">
            {{ row.actualStartTime ? formatDateTime(row.actualStartTime) : "-" }}
          </template>
        </el-table-column>
        <el-table-column prop="actualEndTime" label="完成时间" min-width="150">
          <template #default="{ row }">
            {{ row.actualEndTime ? formatDateTime(row.actualEndTime) : "-" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200" fixed="right">
          <template #default="{ row }">
            <el-button v-if="false" type="primary" size="small" @click="handleView(row)">
              查看详情
            </el-button>

            <!-- D14、D21类型的特殊操作 -->
            <template v-if="isUploadableReviewType(row.reviewType)">
              <el-button
                v-if="row.status === '待开始' || row.status === '进行中'"
                type="success"
                size="small"
                @click="handleUploadClick(row)"
              >
                上传完成情况
              </el-button>
              <el-button
                v-if="row.status === '已完成'"
                type="info"
                size="small"
                @click="handleViewUpload(row)"
              >
                查看上传内容
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 复习详情对话框 -->
    <ReviewDetailDialog v-model="detailDialogVisible" :review="currentReview" />

    <!-- 上传对话框 -->
    <ReviewUploadDialog
      v-model="uploadDialog.visible"
      :review-schedule-id="uploadDialog.reviewScheduleId"
      :mode="uploadDialog.mode"
      @uploaded="handleUploadSuccess"
      @close="handleUploadDialogClose"
    />
  </div>
</template>

<script setup name="ReviewManagement">
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { Search, Refresh } from "@element-plus/icons-vue";
import { formatDateTime } from "@/utils/date";
import {
  getReviewPlansPageApi,
  getTeachersApi as getCurriculumTeachersApi,
  getStudentsApi as getCurriculumStudentsApi,
} from "@/api/curriculum";
import { getTeacherSelectListsApi } from "@/api/management/teachingGroup";
import { getStudentsApi } from "@/api/management/student";
import ReviewDetailDialog from "./components/ReviewDetailDialog.vue";
import ReviewUploadDialog from "@/views/curriculum/components/ReviewUploadDialog.vue";

// 响应式数据
const searchForm = reactive({
  keyword: "",
  teacherId: "",
  studentId: "",
  reviewTypes: [],
  statuses: [],
  dateRange: [],
});

const reviews = ref([]);
const teachers = ref([]);
const students = ref([]);
const detailDialogVisible = ref(false);
const currentReview = ref(null);
const loading = ref(false);
const tableHeight = ref(400);

// 上传对话框状态
const uploadDialog = ref({
  visible: false,
  reviewScheduleId: "",
  mode: "upload", // 'upload' | 'view'
});

const pagination = ref({
  pageNum: 1,
  pageSize: 20,
  total: 0,
});

// 方法
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      startTime: searchForm.dateRange?.[0],
      endTime: searchForm.dateRange?.[1],
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      orderBy: "scheduled_time",
      orderDirection: "DESC",
    };

    console.log("🔄 [抗遗忘复习查询] 请求参数:", params);

    const response = await getReviewPlansPageApi(params);
    console.log("🔄 [抗遗忘复习查询] 响应数据:", response);

    if (response.code === 200) {
      reviews.value = response.data.records || response.data || [];
      pagination.value.total = response.data.total || 0;
    } else {
      console.warn("🔄 [抗遗忘复习查询] API返回错误:", response.message);
      ElMessage.warning(response.message || "获取复习数据失败");
    }
  } catch (error) {
    console.error("获取复习数据失败:", error);
    ElMessage.error("获取复习数据失败，请检查网络连接");

    // 设置模拟数据用于测试
    reviews.value = [
      {
        id: "1",
        name: "5月1日课程的D2抗遗忘复习",
        studentId: "student1",
        studentName: "张三",
        courseId: "course1",
        reviewType: "D2",
        scheduledTime: "2024-05-03 10:00:00",
        actualStartTime: null,
        actualEndTime: null,
        status: "待开始",
        wordIds: ["word1", "word2", "word3"],
        textbookItemIds: ["item1", "item2"],
        version: 1,
      },
      {
        id: "2",
        name: "4月28日课程的D7抗遗忘复习",
        studentId: "student2",
        studentName: "李四",
        courseId: "course2",
        reviewType: "D7",
        scheduledTime: "2024-05-05 14:00:00",
        actualStartTime: "2024-05-05 14:05:00",
        actualEndTime: null,
        status: "进行中",
        wordIds: ["word4", "word5"],
        textbookItemIds: ["item3"],
        version: 1,
      },
    ];
    pagination.value.total = 2;
  } finally {
    loading.value = false;
  }
};

const fetchTeachers = async () => {
  try {
    // 使用新的权限控制API
    const response = await getTeacherSelectListsApi();
    if (response.code === 200) {
      teachers.value = response.data || [];
      console.log("🔄 [抗遗忘复习] 获取老师列表成功:", teachers.value);
    } else {
      console.warn("🔄 [抗遗忘复习] 获取老师列表失败:", response.message);
      teachers.value = [];
    }
  } catch (error) {
    console.error("获取教师列表失败:", error);
    // 如果新API失败，尝试使用课程模块API作为备用
    try {
      const fallbackResponse = await getCurriculumTeachersApi();
      if (fallbackResponse.code === 200) {
        teachers.value = fallbackResponse.data || [];
      }
    } catch (fallbackError) {
      console.error("获取教师列表失败(备用API):", fallbackError);
      // 设置空数据，不再使用模拟数据
      teachers.value = [];
      ElMessage.warning("获取教师列表失败，请检查权限或联系管理员");
    }
  }
};

const fetchStudents = async () => {
  try {
    // 尝试使用管理模块的API
    const response = await getStudentsApi({ pageSize: 1000 });
    if (response.code === 200) {
      students.value = response.data.rows || response.data.records || [];
    }
  } catch (error) {
    console.error("获取学生列表失败:", error);
    // 如果管理模块API失败，尝试使用课程模块API
    try {
      const fallbackResponse = await getCurriculumStudentsApi({ pageSize: 1000 });
      if (fallbackResponse.code === 200) {
        students.value = fallbackResponse.data.rows || fallbackResponse.data || [];
      }
    } catch (fallbackError) {
      console.error("获取学生列表失败(备用API):", fallbackError);
      // 设置模拟学生数据
      students.value = [
        { id: "student1", name: "张三" },
        { id: "student2", name: "李四" },
        { id: "student3", name: "王五" },
      ];
    }
  }
};

const handleSearch = () => {
  pagination.value.pageNum = 1;
  fetchData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: "",
    teacherId: "",
    studentId: "",
    reviewTypes: [],
    statuses: [],
    dateRange: [],
  });
  pagination.value.pageNum = 1;
  fetchData();
};

const handleView = (row) => {
  currentReview.value = { ...row };
  detailDialogVisible.value = true;
};

const handleSizeChange = (size) => {
  pagination.value.pageSize = size;
  pagination.value.pageNum = 1;
  fetchData();
};

const handleCurrentChange = (page) => {
  pagination.value.pageNum = page;
  fetchData();
};

const getReviewTypeTagType = (type) => {
  const typeMap = {
    D2: "success",
    D4: "primary",
    D7: "warning",
    D14: "info",
    D21: "danger",
  };
  return typeMap[type] || "info";
};

const getStatusTagType = (status) => {
  const statusMap = {
    待开始: "info",
    进行中: "warning",
    已完成: "success",
    已跳过: "danger",
  };
  return statusMap[status] || "info";
};

// 所有阶段都支持上传，不再限制D14、D21
const isUploadableReviewType = (reviewType) => {
  return true; // 所有复习类型都支持上传
};

// 处理上传点击
const handleUploadClick = (row) => {
  console.log("🔄 [抗遗忘复习列表] 点击上传完成情况:", row);
  uploadDialog.value = {
    visible: true,
    reviewScheduleId: row.id,
    mode: "upload",
  };
};

// 处理查看上传内容
const handleViewUpload = (row) => {
  console.log("🔄 [抗遗忘复习列表] 点击查看上传内容:", row);
  uploadDialog.value = {
    visible: true,
    reviewScheduleId: row.id,
    mode: "view",
  };
};

// 处理上传成功
const handleUploadSuccess = (result) => {
  console.log("🔄 [抗遗忘复习列表] 上传成功:", result);
  ElMessage.success("上传成功");
  // 刷新数据
  fetchData();
};

// 处理上传对话框关闭
const handleUploadDialogClose = () => {
  uploadDialog.value = {
    visible: false,
    reviewScheduleId: "",
    mode: "upload",
  };
};

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;

    // 根据屏幕尺寸调整各部分高度
    let searchCardHeight, paginationHeight, padding;

    if (windowWidth <= 480) {
      searchCardHeight = 100;
      paginationHeight = 60;
      padding = 20;
    } else if (windowWidth <= 768) {
      searchCardHeight = 110;
      paginationHeight = 70;
      padding = 30;
    } else {
      searchCardHeight = 120;
      paginationHeight = 80;
      padding = 40;
    }

    tableHeight.value = windowHeight - searchCardHeight - paginationHeight - padding;

    // 最小高度限制
    const minHeight = windowWidth <= 768 ? 250 : 300;
    if (tableHeight.value < minHeight) {
      tableHeight.value = minHeight;
    }
  });
};

// 窗口大小变化监听
const handleResize = () => {
  calculateTableHeight();
};

// 生命周期
onMounted(() => {
  Promise.all([fetchData(), fetchTeachers(), fetchStudents()]);

  // 计算初始表格高度
  calculateTableHeight();

  // 添加窗口大小变化监听
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
.review-management-container {
  padding: 16px;
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .search-card {
    margin-bottom: 16px;
    flex-shrink: 0;

    :deep(.el-card__body) {
      padding: 12px 16px;
    }

    .el-form {
      .el-form-item {
        margin-bottom: 12px;
      }
    }
  }

  .table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :deep(.el-card__body) {
      padding: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .el-table {
      flex: 1;

      :deep(.el-table__header) {
        background-color: #fafafa;
      }

      :deep(.el-table__body-wrapper) {
        overflow-y: auto;
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      padding: 16px;
      border-top: 1px solid #ebeef5;
      flex-shrink: 0;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .review-management-container {
    .search-card {
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 8px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .review-management-container {
    padding: 8px;

    .search-card {
      margin-bottom: 12px;

      :deep(.el-card__body) {
        padding: 8px 12px;
      }

      :deep(.el-form) {
        .el-form-item {
          display: block;
          margin-bottom: 8px;

          .el-form-item__content {
            margin-left: 0 !important;
          }
        }
      }
    }

    .table-card {
      .pagination-container {
        padding: 12px 8px;
      }
    }
  }
}

@media (max-width: 480px) {
  .review-management-container {
    padding: 4px;

    .search-card {
      :deep(.el-card__body) {
        padding: 6px 8px;
      }
    }

    .table-card {
      .pagination-container {
        padding: 8px 4px;
      }
    }
  }
}
</style>
