<template>
  <el-dialog
    v-model="dialogVisible"
    title="复习详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="review" class="review-detail">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>复习任务名称：</label>
              <span>{{ review.name }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>学生姓名：</label>
              <span>{{ review.studentName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>复习类型：</label>
              <el-tag :type="getReviewTypeTagType(review.reviewType)" size="small">
                {{ review.reviewType }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>复习状态：</label>
              <el-tag :type="getStatusTagType(review.status)" size="small">
                {{ review.status }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>计划复习时间：</label>
              <span>{{ formatDateTime(review.scheduledTime) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>关联课程ID：</label>
              <span>{{ review.courseId || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 复习进度 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>复习进度</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>实际开始时间：</label>
              <span>{{ review.actualStartTime ? formatDateTime(review.actualStartTime) : '未开始' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>实际完成时间：</label>
              <span>{{ review.actualEndTime ? formatDateTime(review.actualEndTime) : '未完成' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>单词数量：</label>
              <span>{{ review.wordIds?.length || 0 }} 个</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>词表项数量：</label>
              <span>{{ review.textbookItemIds?.length || 0 }} 个</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 统计信息 -->
      <el-card v-if="hasStats" class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>统计信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ review.statWordTotal || 0 }}</div>
              <div class="stat-label">总单词数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number correct">{{ review.statWordCorrect || 0 }}</div>
              <div class="stat-label">正确单词数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number incorrect">{{ review.statWordIncorrect || 0 }}</div>
              <div class="stat-label">错误单词数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ review.statStepTotal || 0 }}</div>
              <div class="stat-label">总步骤数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number correct">{{ review.statStepCorrect || 0 }}</div>
              <div class="stat-label">正确步骤数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number incorrect">{{ review.statStepIncorrect || 0 }}</div>
              <div class="stat-label">错误步骤数</div>
            </div>
          </el-col>
        </el-row>

        <!-- 正确率显示 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <div class="progress-item">
              <label>单词正确率：</label>
              <el-progress 
                :percentage="wordAccuracy" 
                :color="getProgressColor(wordAccuracy)"
                :show-text="true"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="progress-item">
              <label>步骤正确率：</label>
              <el-progress 
                :percentage="stepAccuracy" 
                :color="getProgressColor(stepAccuracy)"
                :show-text="true"
              />
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 其他信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>其他信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>版本号：</label>
              <span>{{ review.version || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>复习课程ID：</label>
              <span>{{ review.reviewCourseId || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>自主复习：</label>
              <el-tag :type="review.reviewByOneself ? 'success' : 'info'" size="small">
                {{ review.reviewByOneself ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>复习执行者：</label>
              <span>{{ review.reviewByUserId || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 复习内容 -->
      <el-card v-if="review.content" class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>复习内容</span>
          </div>
        </template>
        
        <el-input
          v-model="review.content"
          type="textarea"
          :rows="6"
          readonly
          placeholder="暂无复习内容"
        />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="review?.status === '待开始'" 
          type="primary" 
          @click="handleStartReview"
        >
          开始复习
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDateTime } from '@/utils/date'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  review: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'start-review'])

// 响应式数据
const dialogVisible = ref(false)

// 计算属性
const hasStats = computed(() => {
  return props.review && (
    props.review.statWordTotal > 0 || 
    props.review.statStepTotal > 0
  )
})

const wordAccuracy = computed(() => {
  if (!props.review || !props.review.statWordTotal) return 0
  return Math.round((props.review.statWordCorrect / props.review.statWordTotal) * 100)
})

const stepAccuracy = computed(() => {
  if (!props.review || !props.review.statStepTotal) return 0
  return Math.round((props.review.statStepCorrect / props.review.statStepTotal) * 100)
})

// 方法
const handleClose = () => {
  emit('update:modelValue', false)
}

const handleStartReview = () => {
  emit('start-review', props.review)
  handleClose()
}

const getReviewTypeTagType = (type) => {
  const typeMap = {
    'D2': 'success',
    'D4': 'primary',
    'D7': 'warning',
    'D14': 'info',
    'D21': 'danger'
  }
  return typeMap[type] || 'info'
}

const getStatusTagType = (status) => {
  const statusMap = {
    '待开始': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已跳过': 'danger'
  }
  return statusMap[status] || 'info'
}

const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 监听
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})
</script>

<style lang="scss" scoped>
.review-detail {
  .info-card {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .card-header {
      font-weight: 600;
      color: #303133;
    }

    .info-item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-weight: 500;
        color: #606266;
        min-width: 120px;
        margin-right: 8px;
      }

      span {
        color: #303133;
        flex: 1;
      }
    }

    .stat-item {
      text-align: center;
      padding: 16px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      margin-bottom: 12px;

      .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 4px;

        &.correct {
          color: #67c23a;
        }

        &.incorrect {
          color: #f56c6c;
        }
      }

      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }

    .progress-item {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-weight: 500;
        color: #606266;
        margin-bottom: 8px;
        display: block;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .review-detail {
    .info-card {
      .info-item {
        flex-direction: column;
        align-items: flex-start;

        label {
          min-width: auto;
          margin-bottom: 4px;
        }
      }
    }
  }
}
</style>
