<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`教师详情 - ${teacher?.name || ''}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="teacher" class="teacher-detail-container">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <div class="info-grid">
          <div class="info-item">
            <label>姓名：</label>
            <span>{{ teacher.name }}</span>
          </div>
          <div class="info-item">
            <label>昵称：</label>
            <span>{{ teacher.nickname || '-' }}</span>
          </div>
          <div class="info-item">
            <label>性别：</label>
            <el-tag :type="getGenderTagType(teacher.gender)" size="small">
              {{ getGenderText(teacher.gender) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>手机号码：</label>
            <span>{{ teacher.phone }}</span>
          </div>
          <div class="info-item">
            <label>邮箱：</label>
            <span>{{ teacher.email || '-' }}</span>
          </div>
          <div class="info-item">
            <label>教学组：</label>
            <span>{{ teacher.groupName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>工作性质：</label>
            <el-tag v-if="teacher.employmentType" size="small" :type="getEmploymentTypeTagType(teacher.employmentType)">
              {{ getEmploymentTypeText(teacher.employmentType) }}
            </el-tag>
            <span v-else>-</span>
          </div>
          <div class="info-item">
            <label>状态：</label>
            <el-tag :type="teacher.status === 'active' ? 'success' : 'danger'" size="small">
              {{ teacher.status === 'active' ? '在职' : '离职' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDateTime(teacher.createTime) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 教育背景 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>教育背景</span>
          </div>
        </template>
        <div class="info-grid">
          <div class="info-item">
            <label>最高学历：</label>
            <span>{{ teacher.education || '-' }}</span>
          </div>
          <div class="info-item">
            <label>毕业院校：</label>
            <span>{{ teacher.graduateSchool || '-' }}</span>
          </div>
          <div class="info-item">
            <label>毕业专业：</label>
            <span>{{ teacher.major || '-' }}</span>
          </div>
          <div class="info-item">
            <label>是否师范类：</label>
            <el-tag :type="teacher.isNormalUniversity ? 'success' : 'info'" size="small">
              {{ teacher.isNormalUniversity ? '是' : '否' }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 教学资质 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>教学资质</span>
          </div>
        </template>
        <div class="info-grid">
          <div class="info-item">
            <label>教资级别：</label>
            <span>{{ teacher.teachingCertificateLevel || '-' }}</span>
          </div>
          <div class="info-item">
            <label>英语资质：</label>
            <span>{{ teacher.englishQualification || '-' }}</span>
          </div>
          <div class="info-item">
            <label>普通话资质：</label>
            <span>{{ teacher.mandarinQualification || '-' }}</span>
          </div>
          <div class="info-item">
            <label>教龄：</label>
            <span>{{ teacher.teachingYears ? `${teacher.teachingYears}年` : '-' }}</span>
          </div>
          <div class="info-item">
            <label>暑期课上课时间：</label>
            <el-tag :type="getSummerScheduleTagType(teacher.summerScheduleType)" size="small">
              {{ getSummerScheduleText(teacher.summerScheduleType) }}
            </el-tag>
          </div>
          <div class="info-item full-width">
            <label>教授学科：</label>
            <div v-if="teacher.subjects && teacher.subjects.length > 0" class="subjects-container">
              <el-tag
                v-for="subject in teacher.subjects"
                :key="subject"
                size="small"
                class="subject-tag"
              >
                {{ subject }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </div>
        </div>
      </el-card>

      <!-- 教学经历和其他信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>教学经历和其他信息</span>
          </div>
        </template>
        <div class="text-info">
          <div class="text-item">
            <label>教学经历：</label>
            <div class="text-content">
              {{ teacher.teachingExperience || '暂无' }}
            </div>
          </div>
          <div class="text-item">
            <label>获奖奖项：</label>
            <div class="text-content">
              {{ teacher.awards || '暂无' }}
            </div>
          </div>
          <div class="text-item">
            <label>其他信息：</label>
            <div class="text-content">
              {{ teacher.other || '暂无' }}
            </div>
          </div>
          <div class="text-item">
            <label>个人简介：</label>
            <div class="text-content">
              {{ teacher.introduction || '暂无' }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">
          编辑信息
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { formatDateTime } from '@/utils/date'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit'])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleEdit = () => {
  emit('edit', props.teacher)
  handleClose()
}

const getGenderText = (gender) => {
  const genderMap = {
    0: '男',
    1: '女',
    2: '未知'
  }
  return genderMap[gender] || '未知'
}

const getGenderTagType = (gender) => {
  const typeMap = {
    0: 'primary',
    1: 'success',
    2: 'info'
  }
  return typeMap[gender] || 'info'
}

const getSummerScheduleText = (type) => {
  const typeMap = {
    'full': '全满档',
    'golden': '黄金档',
    'other': '其他档'
  }
  return typeMap[type] || '其他档'
}

const getSummerScheduleTagType = (type) => {
  const typeMap = {
    'full': 'danger',
    'golden': 'warning',
    'other': 'info'
  }
  return typeMap[type] || 'info'
}

// 获取工作性质标签类型
const getEmploymentTypeTagType = (type) => {
  const typeMap = {
    'full_time': 'success',
    'intended_full_time': 'primary',
    'part_time': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取工作性质文本
const getEmploymentTypeText = (type) => {
  const textMap = {
    'full_time': '全职',
    'intended_full_time': '意向全职',
    'part_time': '兼职'
  }
  return textMap[type] || type
}
</script>

<style lang="scss" scoped>
.teacher-detail-container {
  .info-card {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .card-header {
      font-weight: 600;
      color: #303133;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      .info-item {
        display: flex;
        align-items: center;

        &.full-width {
          grid-column: 1 / -1;
          align-items: flex-start;
        }

        label {
          font-weight: 500;
          color: #606266;
          min-width: 80px;
          margin-right: 8px;
        }

        .subjects-container {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;

          .subject-tag {
            margin: 0;
          }
        }
      }
    }

    .text-info {
      .text-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          font-weight: 500;
          color: #606266;
          display: block;
          margin-bottom: 8px;
        }

        .text-content {
          color: #303133;
          line-height: 1.6;
          padding: 8px 12px;
          background-color: #f8f9fa;
          border-radius: 4px;
          min-height: 40px;
          white-space: pre-wrap;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .teacher-detail-container {
    .info-card {
      .info-grid {
        grid-template-columns: 1fr;

        .info-item {
          flex-direction: column;
          align-items: flex-start;

          label {
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}
</style>
