<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`管理教师 - ${group?.name || ''}`"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="teacher-manage-container">
      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button type="primary" @click="handleAddTeacher">
            <el-icon><Plus /></el-icon>
            添加教师
          </el-button>
          <el-button
            type="danger"
            :disabled="selectedTeachers.length === 0"
            @click="handleRemoveTeachers"
          >
            <el-icon><Delete /></el-icon>
            移除选中
          </el-button>
        </div>
        <div class="right-actions">
          <el-radio-group v-model="currentView" @change="handleViewChange">
            <el-radio-button label="basic">教师信息</el-radio-button>
            <el-radio-button v-if="false" label="schedule">上课时间</el-radio-button>
            <el-radio-button v-if="false" label="teaching">带教情况</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索教师姓名或手机号"
          clearable
          style="width: 300px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 教师列表 -->
      <div class="teacher-list">
        <!-- 基础信息视图 -->
        <TeacherBasicView
          v-if="currentView === 'basic'"
          v-loading="teachersLoading"
          :teachers="filteredTeachers"
          :selected-teachers="selectedTeachers"
          @selection-change="handleSelectionChange"
          @view-detail="handleViewDetail"
          @view-schedule="handleViewCourseSchedule"
          @assign-students="handleAssignStudents"
        />

        <!-- 上课时间视图 -->
        <TeacherScheduleView
          v-else-if="currentView === 'schedule'"
          v-loading="teachersLoading"
          :teachers="filteredTeachers"
          @view-schedule="handleViewSchedule"
        />

        <!-- 带教情况视图 -->
        <TeacherTeachingView
          v-else-if="currentView === 'teaching'"
          v-loading="teachersLoading"
          :teachers="filteredTeachers"
          @view-teaching="handleViewTeaching"
        />
      </div>
    </div>

    <!-- 添加教师对话框 -->
    <AddTeacherDialog
      v-model="addTeacherDialogVisible"
      :group-id="group?.id"
      @success="handleAddTeacherSuccess"
    />

    <!-- 教师详情对话框 -->
    <TeacherDetailDialog
      v-model="detailDialogVisible"
      :teacher="currentTeacher"
      @edit="handleEditTeacher"
    />

    <!-- 教师时间表对话框 -->
    <TeacherScheduleDialog
      v-model="scheduleDialogVisible"
      :teacher="currentTeacher"
    />

    <!-- 教师带教信息对话框 -->
    <TeacherTeachingDialog
      v-model="teachingDialogVisible"
      :teacher="currentTeacher"
    />

    <!-- 教师编辑对话框 -->
    <TeacherEditDialog
      v-model="editDialogVisible"
      :teacher="currentTeacher"
      @success="handleEditTeacherSuccess"
    />

    <!-- 教师课表查看对话框 -->
    <TeacherCourseScheduleDialog
      v-model="courseScheduleDialogVisible"
      :teacher="currentTeacher"
      @schedule-course="handleScheduleCourse"
    />

    <!-- 分配学生对话框 -->
    <AssignStudentsDialog
      v-model="assignStudentsDialogVisible"
      :teacher="currentTeacher"
      @success="handleAssignStudentsSuccess"
      @add-student="handleAddStudent"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Search } from '@element-plus/icons-vue'
import { useTeachingGroupStore } from '@/stores/teachingGroup'
import TeacherBasicView from './TeacherBasicView.vue'
import TeacherScheduleView from './TeacherScheduleView.vue'
import TeacherTeachingView from './TeacherTeachingView.vue'
import AddTeacherDialog from './AddTeacherDialog.vue'
import TeacherDetailDialog from './TeacherDetailDialog.vue'
import TeacherScheduleDialog from './TeacherScheduleDialog.vue'
import TeacherTeachingDialog from './TeacherTeachingDialog.vue'
import TeacherEditDialog from './TeacherEditDialog.vue'
import TeacherCourseScheduleDialog from '../../teacher/components/TeacherCourseScheduleDialog.vue'
import AssignStudentsDialog from './AssignStudentsDialog.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 使用store
const teachingGroupStore = useTeachingGroupStore()

// 响应式数据
const currentView = ref('basic')
const searchKeyword = ref('')
const selectedTeachers = ref([])
const currentTeacher = ref(null)

// 对话框状态
const addTeacherDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const scheduleDialogVisible = ref(false)
const teachingDialogVisible = ref(false)
const editDialogVisible = ref(false)
const courseScheduleDialogVisible = ref(false)
const assignStudentsDialogVisible = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const groupTeachers = computed(() => teachingGroupStore.groupTeachers)
const teachersLoading = computed(() => teachingGroupStore.teachersLoading)

const filteredTeachers = computed(() => {
  if (!searchKeyword.value) {
    return groupTeachers.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return groupTeachers.value.filter(teacher => 
    teacher.name.toLowerCase().includes(keyword) ||
    teacher.phone.includes(keyword) ||
    (teacher.nickname && teacher.nickname.toLowerCase().includes(keyword))
  )
})

// 监听器
watch(() => props.group, (newGroup) => {
  if (newGroup && props.modelValue) {
    fetchGroupTeachers()
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && props.group) {
    fetchGroupTeachers()
  }
})

// 方法
const fetchGroupTeachers = async () => {
  if (props.group?.id) {
    await teachingGroupStore.fetchGroupTeachers(props.group.id)
  }
}

const handleClose = () => {
  dialogVisible.value = false
  selectedTeachers.value = []
  searchKeyword.value = ''
  currentView.value = 'basic'
}

const handleViewChange = (view) => {
  selectedTeachers.value = []
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleAddTeacher = () => {
  addTeacherDialogVisible.value = true
}

const handleRemoveTeachers = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要从教学组中移除选中的 ${selectedTeachers.value.length} 位教师吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const teacherIds = selectedTeachers.value.map(teacher => teacher.id)
    const success = await teachingGroupStore.removeTeachers(props.group.id, teacherIds)
    
    if (success) {
      selectedTeachers.value = []
      emit('success')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除教师失败:', error)
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedTeachers.value = selection
}

const handleViewDetail = (teacher) => {
  currentTeacher.value = teacher
  detailDialogVisible.value = true
}

const handleViewSchedule = (teacher) => {
  currentTeacher.value = teacher
  scheduleDialogVisible.value = true
}

const handleViewTeaching = (teacher) => {
  currentTeacher.value = teacher
  teachingDialogVisible.value = true
}

const handleAddTeacherSuccess = () => {
  addTeacherDialogVisible.value = false
  fetchGroupTeachers()
  emit('success')
}

const handleEditTeacher = (teacher) => {
  currentTeacher.value = teacher
  editDialogVisible.value = true
}

const handleEditTeacherSuccess = () => {
  editDialogVisible.value = false
  fetchGroupTeachers()
  emit('success')
}

const handleViewCourseSchedule = (teacher) => {
  currentTeacher.value = teacher
  courseScheduleDialogVisible.value = true
}

const handleAssignStudents = (teacher) => {
  currentTeacher.value = teacher
  assignStudentsDialogVisible.value = true
}

const handleScheduleCourse = (teacher) => {
  // 这里可以跳转到排课页面或打开排课弹窗
  console.log('排课:', teacher)
}

const handleAssignStudentsSuccess = () => {
  assignStudentsDialogVisible.value = false
  fetchGroupTeachers()
  emit('success')
}

const handleAddStudent = () => {
  // 这里可以跳转到学生管理页面或打开新增学生弹窗
  console.log('新增学生')
}
</script>

<style lang="scss" scoped>
.teacher-manage-container {
  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;

    .left-actions {
      display: flex;
      gap: 12px;
    }

    .right-actions {
      .el-radio-group {
        :deep(.el-radio-button__inner) {
          padding: 8px 16px;
        }
      }
    }
  }

  .search-bar {
    margin-bottom: 20px;
  }

  .teacher-list {
    min-height: 400px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

// 响应式设计
@media (max-width: 1400px) {
  :deep(.el-dialog) {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .teacher-manage-container {
    .action-bar {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .left-actions {
        justify-content: flex-start;
      }

      .right-actions {
        .el-radio-group {
          width: 100%;
          
          :deep(.el-radio-button) {
            flex: 1;
            
            .el-radio-button__inner {
              width: 100%;
              text-align: center;
            }
          }
        }
      }
    }

    .search-bar {
      .el-input {
        width: 100% !important;
      }
    }
  }
}
</style>
