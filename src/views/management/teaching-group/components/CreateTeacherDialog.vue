<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建教师"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入教师姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="formData.nickname" placeholder="请输入昵称（必填）" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入手机号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="formData.gender" placeholder="请选择性别">
              <el-option label="男" value="0" />
              <el-option label="女" value="1" />
              <el-option label="未知" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入邮箱地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最高学历" prop="education">
            <el-select v-model="formData.education" placeholder="请选择最高学历">
              <el-option label="高中" value="高中" />
              <el-option label="大专" value="大专" />
              <el-option label="本科" value="本科" />
              <el-option label="硕士" value="硕士" />
              <el-option label="博士" value="博士" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="毕业院校" prop="graduateSchool">
            <el-input v-model="formData.graduateSchool" placeholder="请输入毕业院校" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="毕业专业" prop="major">
            <el-input v-model="formData.major" placeholder="请输入毕业专业" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否师范类" prop="isNormalUniversity">
            <el-switch v-model="formData.isNormalUniversity" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="教龄" prop="teachingYears">
            <el-input-number 
              v-model="formData.teachingYears" 
              :min="0" 
              :max="50" 
              placeholder="请输入教龄"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="教资级别" prop="teachingCertificateLevel">
            <el-input v-model="formData.teachingCertificateLevel" placeholder="请输入教资级别" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="英语资质" prop="englishQualification">
            <el-input v-model="formData.englishQualification" placeholder="请输入英语资质" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="普通话资质" prop="mandarinQualification">
            <el-input v-model="formData.mandarinQualification" placeholder="请输入普通话资质" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="教授学科" prop="subjects">
            <el-select
              v-model="formData.subjects"
              multiple
              placeholder="请选择教授学科"
              style="width: 100%"
            >
              <el-option label="英语" value="英语" />
              <el-option label="语文" value="语文" />
              <el-option label="数学" value="数学" />
              <el-option label="物理" value="物理" />
              <el-option label="化学" value="化学" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="教学经历" prop="teachingExperience">
        <el-input 
          v-model="formData.teachingExperience" 
          type="textarea" 
          :rows="3"
          placeholder="请输入教学经历"
        />
      </el-form-item>

      <el-form-item label="获奖奖项" prop="awards">
        <el-input 
          v-model="formData.awards" 
          type="textarea" 
          :rows="2"
          placeholder="请输入获奖奖项"
        />
      </el-form-item>

      <el-form-item label="个人简介" prop="introduction">
        <el-input 
          v-model="formData.introduction" 
          type="textarea" 
          :rows="3"
          placeholder="请输入个人简介"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建教师
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useTeachingGroupStore } from '@/stores/teachingGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// Store
const teachingGroupStore = useTeachingGroupStore()

// 响应式数据
const formRef = ref()
const submitting = ref(false)

const formData = ref({
  name: '',
  nickname: '',
  phone: '',
  gender: '',
  email: '',
  education: '',
  graduateSchool: '',
  major: '',
  isNormalUniversity: false,
  teachingYears: null,
  teachingCertificateLevel: '',
  englishQualification: '',
  mandarinQualification: '',
  subjects: [],
  teachingExperience: '',
  awards: '',
  introduction: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入教师姓名', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.value = {
    name: '',
    nickname: '',
    phone: '',
    gender: '',
    email: '',
    education: '',
    graduateSchool: '',
    major: '',
    isNormalUniversity: false,
    teachingYears: null,
    teachingCertificateLevel: '',
    englishQualification: '',
    mandarinQualification: '',
    subjects: [],
    teachingExperience: '',
    awards: '',
    introduction: ''
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    // 准备提交数据
    const submitData = {
      ...formData.value,
      userName: formData.value.phone, // 手机号作为用户名
      password: formData.value.phone.slice(-6), // 手机号后六位作为默认密码
      groupId: props.groupId
    }
    
    const result = await teachingGroupStore.createTeacher(submitData)
    
    if (result) {
      ElMessage.success('教师创建成功')
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('创建教师失败:', error)
  } finally {
    submitting.value = false
  }
}

// 监听器
watch(() => props.modelValue, (visible) => {
  if (!visible) {
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
}

:deep(.el-select) {
  width: 100%;
}
</style>
