<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`编辑教师信息 - ${teacher?.name || ''}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="right"
      v-loading="loading"
    >
      <!-- 基本信息 -->
      <el-card class="form-section">
        <template #header>
          <span class="section-title">基本信息</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入姓名"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickname">
              <el-input
                v-model="form.nickname"
                placeholder="请输入昵称（可选）"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select
                v-model="form.gender"
                placeholder="请选择性别"
                style="width: 100%"
              >
                <el-option label="男" value="0" />
                <el-option label="女" value="1" />
                <el-option label="未知" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄" prop="age">
              <el-input-number
                v-model="form.age"
                :min="18"
                :max="70"
                placeholder="请输入年龄"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号码"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="请输入邮箱地址（可选）"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="目前所在地" prop="currentLocation">
              <el-input
                v-model="form.currentLocation"
                placeholder="请输入目前所在地"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作性质" prop="employmentType">
              <el-select
                v-model="form.employmentType"
                placeholder="请选择工作性质"
                style="width: 100%"
              >
                <el-option label="全职" value="full_time" />
                <el-option label="意向全职" value="intended_full_time" />
                <el-option label="兼职" value="part_time" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="目前状态" prop="currentStatus">
          <el-input
            v-model="form.currentStatus"
            placeholder="请输入目前状态（如：上班族、学生、居家办公等）"
            maxlength="50"
          />
        </el-form-item>
      </el-card>

      <!-- 教育背景 -->
      <el-card class="form-section">
        <template #header>
          <span class="section-title">教育背景</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="学历" prop="education">
              <el-select
                v-model="form.education"
                placeholder="请选择学历"
                style="width: 100%"
              >
                <el-option label="本科" value="本科" />
                <el-option label="硕士" value="硕士" />
                <el-option label="博士" value="博士" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="毕业院校" prop="graduateSchool">
              <el-input
                v-model="form.graduateSchool"
                placeholder="请输入毕业院校"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专业" prop="major">
              <el-input v-model="form.major" placeholder="请输入专业" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="大学属性" prop="universityType">
              <el-select
                v-model="form.universityType"
                placeholder="请选择大学属性"
                style="width: 100%"
              >
                <el-option label="双一流" value="双一流" />
                  <el-option label="985" value="985" />
                  <el-option label="211" value="211" />
                  <el-option label="一本" value="一本" />
                  <el-option label="普通" value="普通" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="师范院校" prop="isNormalUniversity">
              <el-switch
                v-model="form.isNormalUniversity"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否留学" prop="studyAbroad">
              <el-switch
                v-model="form.studyAbroad"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.studyAbroad">
          <el-col :span="12">
            <el-form-item label="留学国家" prop="studyAbroadCountry">
              <el-input
                v-model="form.studyAbroadCountry"
                placeholder="请输入留学国家"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 教学资质 -->
      <el-card class="form-section">
        <template #header>
          <span class="section-title">教学资质</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="教资级别" prop="teachingCertificateLevel">
              <el-select
                v-model="form.teachingCertificateLevel"
                placeholder="请选择教资级别"
                style="width: 100%"
              >
                <el-option label="小学" value="小学" />
                <el-option label="初中" value="初中" />
                <el-option label="高中" value="高中" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="教龄" prop="teachingYears">
              <el-input-number
                v-model="form.teachingYears"
                :min="0"
                :max="50"
                placeholder="请输入教龄"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="英语资质" prop="englishQualification">
              <el-select
                v-model="form.englishQualification"
                placeholder="请选择英语资质"
                style="width: 100%"
              >
                    <el-option label="四级" value="四级" />
                      <el-option label="六级" value="六级" />
                      <el-option label="专四" value="专四" />
                      <el-option label="专八" value="专八" />
                      <el-option label="雅思" value="雅思" />
                      <el-option label="托福" value="托福" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="普通话资质" prop="mandarinQualification">
              <el-select
                v-model="form.mandarinQualification"
                placeholder="请选择普通话资质"
                style="width: 100%"
              >
                <el-option label="一级甲等" value="一级甲等" />
                <el-option label="一级乙等" value="一级乙等" />
                <el-option label="二级甲等" value="二级甲等" />
                <el-option label="二级乙等" value="二级乙等" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="沟通能力" prop="communicationAbility">
              <el-select
                v-model="form.communicationAbility"
                placeholder="请选择沟通能力"
                style="width: 100%"
              >
                <el-option label="优秀" value="优秀" />
                <el-option label="良好" value="良好" />
                <el-option label="一般" value="一般" />
                <el-option label="薄弱" value="薄弱" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英语发音" prop="englishPronunciation">
              <el-select
                v-model="form.englishPronunciation"
                placeholder="请选择英语发音水平"
                style="width: 100%"
              >
                <el-option label="优秀（母语水平）" value="优秀（母语水平）" />
                        <el-option label="良好" value="良好" />
                        <el-option label="正常" value="正常" />
                        <el-option label="一般" value="一般" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="教授学科" prop="subjects">
          <el-select
            v-model="form.subjects"
            multiple
            placeholder="请选择教授学科"
            style="width: 100%"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>

        <el-form-item label="已通过培训科目" prop="trainingSubjects">
          <el-select
            v-model="form.trainingSubjects"
            multiple
            placeholder="请选择已通过培训的科目"
            style="width: 100%"
          >
            <el-option label="音标" value="音标" />
            <el-option label="语法" value="语法" />
            <el-option label="阅读" value="阅读" />
            <el-option label="听说" value="听说" />
            <el-option label="写作" value="写作" />
            <el-option label="完型" value="完型" />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 其他信息 -->
      <el-card class="form-section">
        <template #header>
          <span class="section-title">其他信息</span>
        </template>

        <el-form-item label="教过课程" prop="taughtCourses">
          <el-select
            v-model="form.taughtCourses"
            multiple
            placeholder="请选择教过的课程"
            style="width: 100%"
          >
            <el-option label="音标课" value="音标课" />
            <el-option label="语法课" value="语法课" />
            <el-option label="阅读课" value="阅读课" />
            <el-option label="写作课" value="写作课" />
            <el-option label="口语课" value="口语课" />
            <el-option label="听力课" value="听力课" />
            <el-option label="高考课" value="高考课" />
            <el-option label="中考课" value="中考课" />
            <el-option label="小升初课" value="小升初课" />
          </el-select>
        </el-form-item>

        <el-form-item label="上课风格" prop="teachingStyle">
          <el-select
            v-model="form.teachingStyle"
            multiple
            placeholder="请选择上课风格"
            style="width: 100%"
          >
            <el-option label="温柔" value="温柔" />
            <el-option label="亲切" value="亲切" />
            <el-option label="幽默" value="幽默" />
            <el-option label="严肃" value="严肃" />
            <el-option label="活泼" value="活泼" />
            <el-option label="耐心" value="耐心" />
            <el-option label="鼓励式" value="鼓励式" />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="适合学生年级" prop="suitableGrades">
              <el-select
                v-model="form.suitableGrades"
                multiple
                placeholder="请选择适合的学生年级"
                style="width: 100%"
              >
                <el-option label="一年级" value="一年级" />
                <el-option label="二年级" value="二年级" />
                <el-option label="三年级" value="三年级" />
                <el-option label="四年级" value="四年级" />
                <el-option label="五年级" value="五年级" />
                <el-option label="六年级" value="六年级" />
                <el-option label="初一" value="初一" />
                <el-option label="初二" value="初二" />
                <el-option label="初三" value="初三" />
                <el-option label="高一" value="高一" />
                <el-option label="高二" value="高二" />
                <el-option label="高三" value="高三" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适合学生程度" prop="suitableLevels">
              <el-select
                v-model="form.suitableLevels"
                multiple
                placeholder="请选择适合的学生程度"
                style="width: 100%"
              >
                <el-option label="学霸" value="学霸" />
                <el-option label="中等生" value="中等生" />
                <el-option label="学困生" value="学困生" />
                <el-option label="零基础" value="零基础" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="适合学生性格" prop="suitablePersonality">
          <el-select
            v-model="form.suitablePersonality"
            placeholder="请选择适合的学生性格"
            style="width: 100%"
          >
            <el-option label="外向活泼" value="外向活泼" />
            <el-option label="内向腼腆" value="内向腼腆" />
            <el-option label="都适合" value="都适合" />
          </el-select>
        </el-form-item>

        <el-form-item label="暑期课上课时间" prop="summerScheduleType">
          <el-select
            v-model="form.summerScheduleType"
            placeholder="请选择暑期课上课时间"
            style="width: 100%"
          >
            <el-option label="全满档（全天，一周6-7天，均可排课）" value="full" />
            <el-option label="黄金档（周一到周五晚上，周末2天，均可排课）" value="golden" />
            <el-option label="其他档（其他指定时间可排课）" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="教学经验" prop="teachingExperience">
          <el-input
            v-model="form.teachingExperience"
            type="textarea"
            :rows="3"
            placeholder="请描述教学经验"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="获奖情况" prop="awards">
          <el-input
            v-model="form.awards"
            type="textarea"
            :rows="3"
            placeholder="请描述获奖情况（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="个人简介" prop="introduction">
          <el-input
            v-model="form.introduction"
            type="textarea"
            :rows="4"
            placeholder="请输入个人简介"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="其他说明" prop="other">
          <el-input
            v-model="form.other"
            type="textarea"
            :rows="2"
            placeholder="其他需要说明的信息（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 资质证书上传 -->
        <el-form-item label="资质证书" prop="qualificationCertificates">
          <BatchFileUpload
            ref="certificateUploadRef"
            v-model="form.qualificationCertificates"
            :upload-url="certificateUploadUrl"
            :limit="5"
            :file-size="10"
            :file-types="['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']"
            tip-text="可同时选择多个资质证书文件，然后一起上传"
          />
        </el-form-item>

        <!-- 示范上课视频上传 -->
        <el-form-item label="示范上课视频" prop="demoVideos">
          <BatchFileUpload
            ref="videoUploadRef"
            v-model="form.demoVideos"
            :upload-url="videoUploadUrl"
            :limit="3"
            :file-size="100"
            :file-types="['mp4', 'avi', 'mov', 'wmv', 'flv']"
            tip-text="可同时选择多个示范视频文件，然后一起上传"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useTeachingGroupStore } from "@/stores/teachingGroup";
import BatchFileUpload from "@/components/BatchFileUpload/index.vue";

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  teacher: {
    type: Object,
    default: null,
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "success"]);

// Store
const teachingGroupStore = useTeachingGroupStore();

// 上传URL
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const certificateUploadUrl = `${baseUrl}/word/file/teacher/certificate`;
const videoUploadUrl = `${baseUrl}/word/file/teacher/video`;

// 响应式数据
const formRef = ref(null);
const certificateUploadRef = ref();
const videoUploadRef = ref();
const submitting = ref(false);
const loading = ref(false);
const teacherDetail = ref(null);

// 表单数据
const form = reactive({
  name: "",
  nickname: "",
  gender: "",
  age: null,
  phone: "",
  email: "",
  currentLocation: "",
  employmentType: "",
  currentStatus: "",

  // 教育背景
  education: "",
  graduateSchool: "",
  major: "",
  universityType: "",
  isNormalUniversity: false,
  studyAbroad: false,
  studyAbroadCountry: "",

  // 教学资质
  teachingCertificateLevel: "",
  teachingYears: null,
  englishQualification: "",
  mandarinQualification: "",
  communicationAbility: "",
  englishPronunciation: "",
  subjects: [],
  trainingSubjects: [],

  // 教学经历和风格
  taughtCourses: [],
  teachingStyle: [],
  suitableGrades: [],
  suitableLevels: [],
  suitablePersonality: "",

  // 暑期课上课时间
  summerScheduleType: "other",

  teachingExperience: "",
  awards: "",
  introduction: "",
  other: "",

  // 文件上传字段
  qualificationCertificates: [],
  demoVideos: [],
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    { min: 2, max: 20, message: "姓名长度在 2 到 20 个字符", trigger: "blur" },
  ],
  phone: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" },
  ],
  email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }],
  gender: [{ required: true, message: "请选择性别", trigger: "change" }],
};

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 获取教师详细信息
const fetchTeacherDetail = async () => {
  if (!props.teacher?.id) return;

  loading.value = true;
  try {
    // 获取教师详细信息
    const detail = await teachingGroupStore.fetchTeacherDetail(props.teacher.id);
    if (detail) {
      teacherDetail.value = detail;
      // 使用最新的详细信息填充表单
      fillForm(detail);
    }
  } catch (error) {
    console.error("获取教师详细信息失败:", error);
    ElMessage.error("获取教师详细信息失败");
    // 如果获取失败，使用传入的教师信息
    fillForm(props.teacher);
  } finally {
    loading.value = false;
  }
};

// 填充表单数据
const fillForm = (teacher) => {
  if (teacher) {
    Object.assign(form, {
      name: teacher.name || "",
      nickname: teacher.nickname || "",
      gender: teacher.gender || "",
      age: teacher.age || null,
      phone: teacher.phone || "",
      email: teacher.email || "",
      currentLocation: teacher.currentLocation || "",
      employmentType: teacher.employmentType || "",
      currentStatus: teacher.currentStatus || "",

      // 教育背景
      education: teacher.education || "",
      graduateSchool: teacher.graduateSchool || "",
      major: teacher.major || "",
      universityType: teacher.universityType || "",
      isNormalUniversity: teacher.isNormalUniversity || false,
      studyAbroad: teacher.studyAbroad || false,
      studyAbroadCountry: teacher.studyAbroadCountry || "",

      // 教学资质
      teachingCertificateLevel: teacher.teachingCertificateLevel || "",
      teachingYears: teacher.teachingYears || null,
      englishQualification: teacher.englishQualification || "",
      mandarinQualification: teacher.mandarinQualification || "",
      communicationAbility: teacher.communicationAbility || "",
      englishPronunciation: teacher.englishPronunciation || "",
      subjects: teacher.subjects || [],
      trainingSubjects: teacher.trainingSubjects || [],

      // 教学经历和风格
      taughtCourses: teacher.taughtCourses || [],
      teachingStyle: teacher.teachingStyle || [],
      suitableGrades: teacher.suitableGrades || [],
      suitableLevels: teacher.suitableLevels || [],
      suitablePersonality: teacher.suitablePersonality || "",

      // 暑期课上课时间
      summerScheduleType: teacher.summerScheduleType || "other",

      teachingExperience: teacher.teachingExperience || "",
      awards: teacher.awards || "",
      introduction: teacher.introduction || "",
      other: teacher.other || "",

      // 文件上传字段
      qualificationCertificates: teacher.qualificationCertificates || [],
      demoVideos: teacher.demoVideos || [],
    });

    console.log('表单数据更新后:', form.qualificationCertificates, form.demoVideos);

    // 确保文件上传组件能够正确显示已有文件
    nextTick(() => {
      console.log('nextTick 中检查组件状态');
      if (certificateUploadRef.value) {
        console.log('证书上传组件存在，强制刷新证书文件');
        certificateUploadRef.value.refreshUploadedFiles(form.qualificationCertificates);
      }
      if (videoUploadRef.value) {
        console.log('视频上传组件存在，强制刷新视频文件');
        videoUploadRef.value.refreshUploadedFiles(form.demoVideos);
      }
    });
  }
};

// 监听器
watch(
  () => props.teacher,
  (newTeacher) => {
    if (newTeacher) {
      // 重置详细信息
      teacherDetail.value = null;
      // 获取最新的教师详细信息
      fetchTeacherDetail();
    }
  },
  { immediate: true }
);

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible && props.teacher) {
      // 重置详细信息
      teacherDetail.value = null;
      // 获取最新的教师详细信息
      fetchTeacherDetail();
    }
  }
);

// 方法
const handleClose = () => {
  dialogVisible.value = false;
  // 清除表单验证
  formRef.value?.clearValidate();

  // 清理文件上传组件
  nextTick(() => {
    if (certificateUploadRef.value) {
      certificateUploadRef.value.clearAllFiles();
    }
    if (videoUploadRef.value) {
      videoUploadRef.value.clearAllFiles();
    }
  });
};

const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    submitting.value = true;

    // 调用API更新教师信息
    const success = await teachingGroupStore.updateTeacherInfo({
      teacherId: props.teacher.id,
      name: form.name,
      nickname: form.nickname,
      gender: form.gender,
      age: form.age,
      phone: form.phone,
      email: form.email,
      currentLocation: form.currentLocation,
      employmentType: form.employmentType,
      currentStatus: form.currentStatus,

      // 教育背景
      education: form.education,
      graduateSchool: form.graduateSchool,
      major: form.major,
      universityType: form.universityType,
      isNormalUniversity: form.isNormalUniversity,
      studyAbroad: form.studyAbroad,
      studyAbroadCountry: form.studyAbroadCountry,

      // 教学资质
      teachingCertificateLevel: form.teachingCertificateLevel,
      teachingYears: form.teachingYears,
      englishQualification: form.englishQualification,
      mandarinQualification: form.mandarinQualification,
      communicationAbility: form.communicationAbility,
      englishPronunciation: form.englishPronunciation,
      subjects: form.subjects,
      trainingSubjects: form.trainingSubjects,

      // 教学经历和风格
      taughtCourses: form.taughtCourses,
      teachingStyle: form.teachingStyle,
      suitableGrades: form.suitableGrades,
      suitableLevels: form.suitableLevels,
      suitablePersonality: form.suitablePersonality,

      // 暑期课上课时间
      summerScheduleType: form.summerScheduleType,

      teachingExperience: form.teachingExperience,
      awards: form.awards,
      introduction: form.introduction,
      other: form.other,

      // 文件上传字段
      qualificationCertificates: form.qualificationCertificates,
      demoVideos: form.demoVideos,
    });

    if (success) {
      ElMessage.success("教师信息更新成功");
      emit("success");
      handleClose();
    }
  } catch (error) {
    console.error("更新教师信息失败:", error);
    ElMessage.error("更新教师信息失败");
  } finally {
    submitting.value = false;
  }
};
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-weight: 600;
    color: #303133;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .form-section {
    :deep(.el-row) {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
