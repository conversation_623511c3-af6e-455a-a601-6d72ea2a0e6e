<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`分配学生 - ${teacher?.name || ''}`"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="teacher" class="assign-students-container">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索学生姓名、手机号"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleAddStudent">
          <el-icon><Plus /></el-icon>
          新增学生
        </el-button>
      </div>

      <!-- 已分配学生列表 -->
      <div class="assigned-students">
        <h4>已分配学生 ({{ assignedStudents.length }})</h4>
        <div v-if="assignedStudents.length > 0" class="student-grid">
          <div
            v-for="(student, index) in assignedStudents"
            :key="`${student.id}_${student.subject}_${student.courseType}`"
            class="student-card assigned"
          >
            <div class="student-info">
              <div class="student-name">{{ student.name }}</div>
              <div class="student-grade">{{ getGradeText(student.grade) }}</div>
              <div class="student-subject">{{ student.subject }} - {{ student.courseType }}</div>
            </div>
            <div class="student-actions">
              <el-button
                type="danger"
                size="small"
                @click="handleRemoveStudent(index)"
                title="移除"
              >
                移除
              </el-button>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <el-empty description="暂无分配的学生" :image-size="80" />
        </div>
      </div>

      <!-- 可分配学生列表 -->
      <div class="available-students">
        <h4>可分配学生 ({{ filteredAvailableStudents.length }})</h4>
        <div v-loading="studentsLoading" class="student-grid">
          <div
            v-for="student in filteredAvailableStudents"
            :key="student.id"
            class="student-card available"
          >
            <div class="student-info">
              <div class="student-name">{{ student.name }}</div>
              <div class="student-grade">{{ getGradeText(student.grade) }}</div>
            </div>
            <div class="student-actions">
              <el-button
                type="primary"
                size="small"
                @click="handleAssignStudent(student)"
                title="分配"
              >
                分配
              </el-button>
            </div>
          </div>

          <div v-if="!studentsLoading && filteredAvailableStudents.length === 0" class="empty-state">
            <el-empty description="暂无可分配的学生" :image-size="80" />
          </div>
        </div>
      </div>
    </div>

    <!-- 分配学生表单对话框 -->
    <el-dialog
      v-model="assignFormVisible"
      title="分配学生"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="assignFormRef"
        :model="assignForm"
        :rules="assignFormRules"
        label-width="80px"
      >
        <el-form-item label="学生" prop="studentId">
          <el-input v-model="currentStudent.name" disabled />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select v-model="assignForm.subject" placeholder="请选择学科">
            <el-option label="英语" value="英语" />
            <el-option label="数学" value="数学" />
            <el-option label="语文" value="语文" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
            <el-option label="生物" value="生物" />
          </el-select>
        </el-form-item>
        <el-form-item label="课型" prop="courseType">
          <el-select v-model="assignForm.courseType" placeholder="请选择课型">
            <el-option label="单词课" value="单词课" />
            <el-option label="音标拼读课" value="音标拼读课" />
            <el-option label="语法课" value="语法课" />
            <el-option label="题型课" value="题型课" />
            <el-option label="听说课" value="听说课" />
            <el-option label="通用课（非英语）" value="通用课（非英语）" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignFormVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAssign" :loading="assigning">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Close } from '@element-plus/icons-vue'
import { useTeachingGroupStore } from '@/stores/teachingGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success', 'add-student'])

// Store
const teachingGroupStore = useTeachingGroupStore()

// 响应式数据
const searchKeyword = ref('')
const assignedStudents = ref([])
const availableStudents = ref([])
const studentsLoading = ref(false)
const saving = ref(false)
const originalAssignedStudents = ref([])

// 分配表单相关
const assignFormVisible = ref(false)
const assignFormRef = ref()
const currentStudent = ref({})
const assigning = ref(false)
const assignForm = ref({
  subject: '',
  courseType: ''
})

const assignFormRules = {
  subject: [
    { required: true, message: '请选择学科', trigger: 'change' }
  ],
  courseType: [
    { required: true, message: '请选择课型', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const filteredAvailableStudents = computed(() => {
  if (!searchKeyword.value) {
    return availableStudents.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return availableStudents.value.filter(student => 
    student.name.toLowerCase().includes(keyword) ||
    student.phone.includes(keyword)
  )
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleAddStudent = () => {
  emit('add-student')
}

const handleAssignStudent = (student) => {
  // 打开分配表单
  currentStudent.value = student
  assignForm.value = {
    subject: '英语', // 默认选择英语
    courseType: '单词课' // 默认选择正式课
  }
  assignFormVisible.value = true
}

const confirmAssign = async () => {
  if (!assignFormRef.value) return

  try {
    await assignFormRef.value.validate()

    // 创建分配记录
    const assignmentRecord = {
      id: currentStudent.value.id,
      name: currentStudent.value.name,
      phone: currentStudent.value.phone,
      grade: currentStudent.value.grade,
      school: currentStudent.value.school,
      subject: assignForm.value.subject,
      courseType: assignForm.value.courseType
    }

    // 检查是否已经存在相同的分配
    const existingIndex = assignedStudents.value.findIndex(s =>
      s.id === currentStudent.value.id &&
      s.subject === assignForm.value.subject &&
      s.courseType === assignForm.value.courseType
    )

    if (existingIndex > -1) {
      ElMessage.warning('该学生已分配相同的学科和课型')
      return
    }

    // 添加到已分配列表
    assignedStudents.value.push(assignmentRecord)

    // 关闭表单
    assignFormVisible.value = false
    ElMessage.success('分配成功')

  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleRemoveStudent = (index) => {
  // 从已分配列表中移除
  assignedStudents.value.splice(index, 1)
}

const handleSave = async () => {
  if (!props.teacher?.id) return

  try {
    saving.value = true

    // 构建新的分配数据结构
    const studentAssignments = assignedStudents.value.map(student => ({
      studentId: student.id,
      subject: student.subject,
      courseType: student.courseType
    }))

    // 发送分配数据到后端
    await teachingGroupStore.assignStudentsToTeacher(props.teacher.id, studentAssignments)

    ElMessage.success('学生分配保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存学生分配失败:', error)
    ElMessage.error('保存学生分配失败')
  } finally {
    saving.value = false
  }
}

// 年级转换函数
const getGradeText = (grade) => {
  const gradeMap = {
    '1': '一年级',
    '2': '二年级',
    '3': '三年级',
    '4': '四年级',
    '5': '五年级',
    '6': '六年级',
    '7': '初一',
    '8': '初二',
    '9': '初三',
    '10': '高一',
    '11': '高二',
    '12': '高三'
  }
  return gradeMap[String(grade)] || '未设置年级'
}

const fetchStudentData = async () => {
  if (!props.teacher?.id) return

  studentsLoading.value = true
  try {
    // 获取教师已分配的学生 - 直接调用获取教师学生列表的API
    const { teachingGroupApi } = await import('@/api/management/teachingGroup')
    const teacherStudentsResponse = await teachingGroupApi.getTeacherStudents(props.teacher.id)
    if (teacherStudentsResponse.code === 200) {
      assignedStudents.value = teacherStudentsResponse.data || []
      originalAssignedStudents.value = JSON.parse(JSON.stringify(assignedStudents.value))
    } else {
      console.warn('获取教师已分配学生失败:', teacherStudentsResponse.message)
      assignedStudents.value = []
      originalAssignedStudents.value = []
    }

    // 获取所有可分配的学生 - 使用真实API
    const { studentApi } = await import('@/api/management/student')
    const response = await studentApi.getAvailableStudents()
    if (response.code === 200) {
      availableStudents.value = response.data || []
    } else {
      ElMessage.error(response.message || '获取可分配学生列表失败')
      availableStudents.value = []
    }

    // 从可分配列表中移除已分配的学生
    const assignedIds = assignedStudents.value.map(s => s.id)
    availableStudents.value = availableStudents.value.filter(s => !assignedIds.includes(s.id))

  } catch (error) {
    console.error('获取学生数据失败:', error)
    ElMessage.error('获取学生数据失败')
    availableStudents.value = []
  } finally {
    studentsLoading.value = false
  }
}

// 防止重复请求的标志
const isDataLoaded = ref(false)

// 监听器
watch(() => [props.modelValue, props.teacher], ([visible, teacher]) => {
  if (visible && teacher && !isDataLoaded.value) {
    // 只有在对话框打开且有教师且数据未加载时才请求
    isDataLoaded.value = true
    fetchStudentData()
  } else if (!visible) {
    // 对话框关闭时重置数据和标志
    searchKeyword.value = ''
    assignedStudents.value = []
    availableStudents.value = []
    originalAssignedStudents.value = []
    isDataLoaded.value = false
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.assign-students-container {
  .search-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    
    .el-input {
      flex: 1;
    }
  }

  .assigned-students,
  .available-students {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .student-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
    max-height: 280px;
    overflow-y: auto;
    padding: 8px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fafafa;

    .student-card {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 8px 10px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;
      position: relative;
      cursor: pointer;
      min-height: 50px;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &.assigned {
        border: 1px solid #67c23a;
        background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);

        &::before {
          content: "✓";
          position: absolute;
          top: 2px;
          right: 2px;
          background: #67c23a;
          color: white;
          font-size: 8px;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1;
        }
      }

      &.available {
        border: 1px solid #e4e7ed;

        &:hover {
          border-color: #409eff;
        }
      }

      .student-info {
        flex: 1;
        min-width: 0;

        .student-name {
          font-size: 13px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 2px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .student-grade {
          font-size: 10px;
          color: #409eff;
          background: #ecf5ff;
          padding: 1px 4px;
          border-radius: 8px;
          display: inline-block;
        }

        .student-subject {
          font-size: 10px;
          color: #67c23a;
          background: #f0f9ff;
          padding: 1px 4px;
          border-radius: 8px;
          display: inline-block;
          margin-top: 2px;
        }
      }

      .student-actions {
        flex-shrink: 0;
        margin-left: 6px;

        .el-button {
          &.is-circle {
            width: 24px;
            height: 24px;

            .el-icon {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .empty-state {
    padding: 20px;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
