<template>
  <div class="weekly-calendar-time-selector">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <span class="title">可上课时间设置</span>
      </div>
      <div class="toolbar-right">
        <el-button size="small" @click="clearAll">
          <el-icon><Delete /></el-icon>
          清空所有
        </el-button>
        <el-button size="small" type="primary" @click="applyWorkdayTemplate">
          <el-icon><Clock /></el-icon>
          工作日模板
        </el-button>
      </div>
    </div>

    <!-- 日历网格 -->
    <div class="calendar-container">
      <!-- 表头 -->
      <div class="calendar-header">
        <div class="time-column-header">时间</div>
        <div
          v-for="day in weekDays"
          :key="day.value"
          class="day-header"
        >
          {{ day.label }}
        </div>
      </div>

      <!-- 时间网格 -->
      <div class="calendar-body">
        <div
          v-for="hour in timeHours"
          :key="hour"
          class="time-row"
        >
          <!-- 时间标签 -->
          <div class="time-label">
            {{ formatHour(hour) }}
          </div>

          <!-- 每天的时间格子 -->
          <div
            v-for="day in weekDays"
            :key="`${day.value}-${hour}`"
            class="time-cell"
            :class="getTimeCellClass(day.value, hour)"
            @mousedown="startSelection(day.value, hour, $event)"
            @mouseenter="updateSelection(day.value, hour)"
            @mouseup="endSelection"
          >
            <div class="cell-content">
              <div v-if="isTimeSlotStart(day.value, hour)" class="time-slot-label">
                {{ getTimeSlotLabel(day.value, hour) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中的时间段列表 -->
    <div class="selected-slots">
      <div class="slots-header">
        <span>已选择的时间段 ({{ timeSlots.length }}个)</span>
      </div>
      <div class="slots-list">
        <div
          v-for="(slot, index) in timeSlots"
          :key="slot.id || index"
          class="slot-item"
        >
          <div class="slot-info">
            <span class="slot-day">{{ getWeekDayName(slot.weekday) }}</span>
            <span class="slot-time">{{ slot.startTime }} - {{ slot.endTime }}</span>
            <span class="slot-status" :class="`status-${slot.status}`">
              {{ getStatusText(slot.status) }}
            </span>
          </div>
          <div class="slot-actions">
            <el-button
              type="danger"
              link
              size="small"
              @click="removeTimeSlot(index)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Clock } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const timeSlots = ref([])
const isSelecting = ref(false)
const selectionStart = ref(null)
const selectionEnd = ref(null)
const currentWeekday = ref(null)

// 常量 - 使用系统格式（1-7，周日=7）与后端保持一致
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

const timeHours = Array.from({ length: 18 }, (_, i) => i + 6) // 6-23点

// 监听器
watch(() => props.modelValue, (newValue) => {
  timeSlots.value = [...newValue]
}, { immediate: true, deep: true })

watch(timeSlots, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
}, { deep: true })

// 计算属性
const timeGrid = computed(() => {
  const grid = {}
  timeSlots.value.forEach(slot => {
    const startHour = parseInt(slot.startTime.split(':')[0])
    const endHour = parseInt(slot.endTime.split(':')[0])
    
    for (let hour = startHour; hour <= endHour; hour++) {
      const key = `${slot.weekday}-${hour}`
      if (!grid[key]) {
        grid[key] = []
      }
      grid[key].push(slot)
    }
  })
  return grid
})

// 方法
const formatHour = (hour) => {
  return `${hour.toString().padStart(2, '0')}:00`
}

const getWeekDayName = (weekday) => {
  const day = weekDays.find(d => d.value === weekday)
  return day ? day.label : ''
}

const getStatusText = (status) => {
  const statusMap = {
    available: '可上课',
    scheduled: '已排课'
  }
  return statusMap[status] || '未知'
}

const getTimeCellClass = (weekday, hour) => {
  const classes = ['selectable']
  
  if (props.readonly) {
    classes.push('readonly')
  }
  
  const key = `${weekday}-${hour}`
  const slots = timeGrid.value[key] || []
  
  if (slots.length > 0) {
    classes.push('selected')
    classes.push(`status-${slots[0].status}`)
  }
  
  // 选择状态
  if (isSelecting.value && currentWeekday.value === weekday) {
    const start = Math.min(selectionStart.value, selectionEnd.value || selectionStart.value)
    const end = Math.max(selectionStart.value, selectionEnd.value || selectionStart.value)
    
    if (hour >= start && hour <= end) {
      classes.push('selecting')
    }
  }
  
  return classes
}

const isTimeSlotStart = (weekday, hour) => {
  return timeSlots.value.some(slot => {
    const startHour = parseInt(slot.startTime.split(':')[0])
    return slot.weekday === weekday && startHour === hour
  })
}

const getTimeSlotLabel = (weekday, hour) => {
  const slot = timeSlots.value.find(slot => {
    const startHour = parseInt(slot.startTime.split(':')[0])
    return slot.weekday === weekday && startHour === hour
  })
  return slot ? `${slot.startTime}-${slot.endTime}` : ''
}

const startSelection = (weekday, hour, event) => {
  if (props.readonly) return
  
  event.preventDefault()
  isSelecting.value = true
  selectionStart.value = hour
  selectionEnd.value = hour
  currentWeekday.value = weekday
  
  // 添加全局事件监听
  document.addEventListener('mouseup', endSelection)
  document.addEventListener('mouseleave', endSelection)
}

const updateSelection = (weekday, hour) => {
  if (!isSelecting.value || currentWeekday.value !== weekday) return
  
  selectionEnd.value = hour
}

const endSelection = () => {
  if (!isSelecting.value) return
  
  const weekday = currentWeekday.value
  const startHour = Math.min(selectionStart.value, selectionEnd.value)
  const endHour = Math.max(selectionStart.value, selectionEnd.value)
  
  // 创建新的时间段
  const newSlot = {
    id: `slot_${Date.now()}`,
    weekday,
    startTime: `${startHour.toString().padStart(2, '0')}:00`,
    endTime: `${(endHour + 1).toString().padStart(2, '0')}:00`,
    status: 'available',
    remark: ''
  }
  
  // 合并重叠的时间段
  mergeTimeSlots(newSlot)
  
  // 重置选择状态
  isSelecting.value = false
  selectionStart.value = null
  selectionEnd.value = null
  currentWeekday.value = null
  
  // 移除全局事件监听
  document.removeEventListener('mouseup', endSelection)
  document.removeEventListener('mouseleave', endSelection)
}

const mergeTimeSlots = (newSlot) => {
  // 找到同一天的所有时间段
  const sameDaySlots = timeSlots.value.filter(slot => slot.weekday === newSlot.weekday)
  const otherDaySlots = timeSlots.value.filter(slot => slot.weekday !== newSlot.weekday)
  
  // 添加新时间段
  sameDaySlots.push(newSlot)
  
  // 按开始时间排序
  sameDaySlots.sort((a, b) => a.startTime.localeCompare(b.startTime))
  
  // 合并重叠的时间段
  const merged = []
  for (const slot of sameDaySlots) {
    if (merged.length === 0) {
      merged.push(slot)
    } else {
      const lastSlot = merged[merged.length - 1]
      
      // 检查是否重叠或相邻
      if (lastSlot.endTime >= slot.startTime) {
        // 合并时间段
        lastSlot.endTime = slot.endTime > lastSlot.endTime ? slot.endTime : lastSlot.endTime
        lastSlot.id = `slot_${Date.now()}_merged`
      } else {
        merged.push(slot)
      }
    }
  }
  
  // 更新时间段列表
  timeSlots.value = [...otherDaySlots, ...merged]
}

const removeTimeSlot = (index) => {
  timeSlots.value.splice(index, 1)
}

const clearAll = () => {
  timeSlots.value = []
  ElMessage.success('已清空所有时间段')
}

const applyWorkdayTemplate = () => {
  const template = []
  
  // 工作日模板：周一到周五，上午9-12点，下午14-18点
  for (let weekday = 1; weekday <= 5; weekday++) {
    template.push({
      id: `template_${weekday}_morning`,
      weekday,
      startTime: '09:00',
      endTime: '12:00',
      status: 'available',
      remark: '上午时段'
    })
    
    template.push({
      id: `template_${weekday}_afternoon`,
      weekday,
      startTime: '14:00',
      endTime: '18:00',
      status: 'available',
      remark: '下午时段'
    })
  }
  
  timeSlots.value = template
  ElMessage.success('已应用工作日模板')
}

// 组件卸载时清理事件监听
import { onUnmounted } from 'vue'
onUnmounted(() => {
  document.removeEventListener('mouseup', endSelection)
  document.removeEventListener('mouseleave', endSelection)
})
</script>

<style lang="scss" scoped>
.weekly-calendar-time-selector {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .calendar-container {
    border: 1px solid #ebeef5;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 20px;

    .calendar-header {
      display: grid;
      grid-template-columns: 80px repeat(7, 1fr);
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;

      .time-column-header,
      .day-header {
        padding: 12px 8px;
        text-align: center;
        font-weight: 600;
        color: #606266;
        border-right: 1px solid #ebeef5;

        &:last-child {
          border-right: none;
        }
      }
    }

    .calendar-body {
      .time-row {
        display: grid;
        grid-template-columns: 80px repeat(7, 1fr);
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .time-label {
          padding: 8px;
          text-align: center;
          font-size: 12px;
          color: #909399;
          background-color: #fafafa;
          border-right: 1px solid #ebeef5;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .time-cell {
          min-height: 32px;
          border-right: 1px solid #f0f0f0;
          position: relative;
          cursor: pointer;
          user-select: none;

          &:last-child {
            border-right: none;
          }

          &.selectable:hover {
            background-color: #f0f9ff;
          }

          &.selecting {
            background-color: #e1f5fe !important;
          }

          &.selected {
            &.status-available {
              background-color: #f0f9ff;
              border: 1px solid #409eff;
            }

            &.status-scheduled {
              background-color: #fdf6ec;
              border: 1px solid #e6a23c;
            }
          }

          &.readonly {
            cursor: not-allowed;
          }

          .cell-content {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px;

            .time-slot-label {
              font-size: 10px;
              color: #409eff;
              font-weight: 600;
              text-align: center;
              line-height: 1.2;
            }
          }
        }
      }
    }
  }

  .selected-slots {
    .slots-header {
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .slots-list {
      max-height: 200px;
      overflow-y: auto;

      .slot-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #ebeef5;

        .slot-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .slot-day {
            font-weight: 600;
            color: #303133;
            min-width: 40px;
          }

          .slot-time {
            color: #606266;
            font-family: monospace;
          }

          .slot-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;

            &.status-available {
              background-color: #f0f9ff;
              color: #409eff;
            }

            &.status-scheduled {
              background-color: #fdf6ec;
              color: #e6a23c;
            }
          }
        }

        .slot-actions {
          .el-button {
            padding: 4px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .weekly-calendar-time-selector {
    .calendar-container {
      .calendar-header,
      .time-row {
        grid-template-columns: 60px repeat(7, 1fr);
      }

      .time-label {
        font-size: 10px;
        padding: 4px;
      }

      .time-cell {
        min-height: 28px;

        .cell-content .time-slot-label {
          font-size: 8px;
        }
      }
    }

    .selected-slots .slots-list .slot-item {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;

      .slot-info {
        justify-content: space-between;
      }

      .slot-actions {
        text-align: center;
      }
    }
  }
}
</style>
