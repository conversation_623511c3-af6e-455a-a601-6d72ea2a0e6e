<template>
  <div class="teacher-teaching-view">
    <el-table
      :data="teachersWithTeachingInfo"
      stripe
      style="width: 100%"
      max-height="500px"
    >
      <el-table-column prop="name" label="教师姓名" width="120" fixed="left" />
      <el-table-column prop="phone" label="手机号码" width="120" />
      <el-table-column prop="currentStudents" label="在教学生数" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="getStudentCountTagType(row.currentStudents)" size="small">
            {{ row.currentStudents || 0 }}人
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="unconsumedHours" label="未消课时" width="120" align="center">
        <template #default="{ row }">
          <span class="hours-text unconsumed">
            {{ row.unconsumedHours || 0 }}小时
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="consumedHours" label="已消课时" width="120" align="center">
        <template #default="{ row }">
          <span class="hours-text consumed">
            {{ row.consumedHours || 0 }}小时
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="totalHours" label="总课时" width="120" align="center">
        <template #default="{ row }">
          <span class="hours-text total">
            {{ row.totalHours || 0 }}小时
          </span>
        </template>
      </el-table-column>
      <el-table-column label="课时进度" width="200">
        <template #default="{ row }">
          <div class="progress-container">
            <el-progress
              :percentage="getProgressPercentage(row)"
              :color="getProgressColor(row)"
              :stroke-width="8"
              text-inside
            />
            <div class="progress-text">
              {{ getProgressText(row) }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="教学状态" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="getTeachingStatusTagType(row)" size="small">
            {{ getTeachingStatusText(row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="平均课时/周" width="120" align="center">
        <template #default="{ row }">
          <span>{{ getAverageHoursPerWeek(row) }}小时</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleViewTeaching(row)">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态 -->
    <div v-if="teachers.length === 0" class="empty-state">
      <el-empty description="暂无教师数据" />
    </div>

    <!-- 统计信息 -->
    <div v-if="teachers.length > 0" class="statistics">
      <el-card>
        <template #header>
          <span>带教统计</span>
        </template>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ totalCurrentStudents }}</div>
            <div class="stat-label">总在教学生</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ totalUnconsumedHours }}</div>
            <div class="stat-label">总未消课时</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ totalConsumedHours }}</div>
            <div class="stat-label">总已消课时</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ averageStudentsPerTeacher }}</div>
            <div class="stat-label">平均学生数/教师</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// Props
const props = defineProps({
  teachers: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['view-teaching'])

// 响应式数据
const teachingInfoMap = ref(new Map())

// 计算属性
const teachersWithTeachingInfo = computed(() => {
  return props.teachers.map(teacher => {
    const teachingInfo = teachingInfoMap.value.get(teacher.id) || {}
    return {
      ...teacher,
      ...teachingInfo
    }
  })
})

const totalCurrentStudents = computed(() => {
  return teachersWithTeachingInfo.value.reduce((sum, teacher) => 
    sum + (teacher.currentStudents || 0), 0
  )
})

const totalUnconsumedHours = computed(() => {
  return teachersWithTeachingInfo.value.reduce((sum, teacher) => 
    sum + (teacher.unconsumedHours || 0), 0
  )
})

const totalConsumedHours = computed(() => {
  return teachersWithTeachingInfo.value.reduce((sum, teacher) => 
    sum + (teacher.consumedHours || 0), 0
  )
})

const averageStudentsPerTeacher = computed(() => {
  const activeTeachers = teachersWithTeachingInfo.value.filter(t => t.currentStudents > 0)
  if (activeTeachers.length === 0) return 0
  return Math.round(totalCurrentStudents.value / activeTeachers.length * 10) / 10
})

// 方法
const handleViewTeaching = (teacher) => {
  emit('view-teaching', teacher)
}

const getStudentCountTagType = (count) => {
  if (count === 0) return 'info'
  if (count <= 3) return 'success'
  if (count <= 6) return 'warning'
  return 'danger'
}

const getProgressPercentage = (teacher) => {
  const total = teacher.totalHours || 0
  const consumed = teacher.consumedHours || 0
  if (total === 0) return 0
  return Math.round((consumed / total) * 100)
}

const getProgressColor = (teacher) => {
  const percentage = getProgressPercentage(teacher)
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const getProgressText = (teacher) => {
  const consumed = teacher.consumedHours || 0
  const total = teacher.totalHours || 0
  return `${consumed}/${total}`
}

const getTeachingStatusText = (teacher) => {
  const students = teacher.currentStudents || 0
  const unconsumed = teacher.unconsumedHours || 0
  
  if (students === 0) return '空闲'
  if (unconsumed === 0) return '课时用完'
  if (students <= 3) return '正常'
  if (students <= 6) return '繁忙'
  return '超负荷'
}

const getTeachingStatusTagType = (teacher) => {
  const status = getTeachingStatusText(teacher)
  const typeMap = {
    '空闲': 'info',
    '课时用完': 'warning',
    '正常': 'success',
    '繁忙': 'warning',
    '超负荷': 'danger'
  }
  return typeMap[status] || 'info'
}

const getAverageHoursPerWeek = (teacher) => {
  // 这里应该根据实际的课程安排计算
  // 现在使用模拟数据
  const students = teacher.currentStudents || 0
  return Math.round(students * 2.5 * 10) / 10 // 假设每个学生每周2.5小时
}

// 生命周期
onMounted(async () => {
  // 获取所有教师的带教信息
  if (props.teachers && props.teachers.length > 0) {
    try {
      const teacherIds = props.teachers.map(t => t.id)
      const teachingInfoList = await teachingGroupStore.fetchTeachingInfoBatch(teacherIds)

      // 将结果存储到Map中
      teachingInfoList.forEach(info => {
        teachingInfoMap.value.set(info.teacherId, info)
      })
    } catch (error) {
      console.error('获取教师带教信息失败:', error)
      // 如果API调用失败，设置默认值
      props.teachers.forEach(teacher => {
        teachingInfoMap.value.set(teacher.id, {
          teacherId: teacher.id,
          teacherName: teacher.name,
          currentStudents: 0,
          unconsumedHours: 0,
          consumedHours: 0,
          totalHours: 0
        })
      })
    }
  }
})
</script>

<style lang="scss" scoped>
.teacher-teaching-view {
  .hours-text {
    font-weight: 500;

    &.unconsumed {
      color: #e6a23c;
    }

    &.consumed {
      color: #67c23a;
    }

    &.total {
      color: #409eff;
    }
  }

  .progress-container {
    .progress-text {
      font-size: 12px;
      color: #909399;
      text-align: center;
      margin-top: 4px;
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }

  .statistics {
    margin-top: 20px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;

      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  :deep(.el-table) {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 600;
      }
    }

    .el-table__body {
      tr:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .teacher-teaching-view {
    :deep(.el-table) {
      font-size: 12px;

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }

    .statistics {
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;

        .stat-item {
          .stat-value {
            font-size: 20px;
          }
        }
      }
    }
  }
}
</style>
