<template>
  <div class="teacher-schedule-view">
    <div class="teacher-cards">
      <div
        v-for="teacher in teachers"
        :key="teacher.id"
        class="teacher-card"
      >
        <el-card>
          <template #header>
            <div class="card-header">
              <div class="teacher-info">
                <span class="teacher-name">{{ teacher.name }}</span>
                <span class="teacher-phone">{{ teacher.phone }}</span>
              </div>
              <el-button type="primary" link @click="handleViewSchedule(teacher)">
                查看详情
              </el-button>
            </div>
          </template>

          <!-- 时间表格 -->
          <div class="schedule-table">
            <div class="time-header">
              <div class="time-cell header-cell">时间</div>
              <div
                v-for="day in weekDays"
                :key="day.value"
                class="day-cell header-cell"
              >
                {{ day.label }}
              </div>
            </div>

            <div
              v-for="hour in hours"
              :key="hour"
              class="time-row"
            >
              <div class="time-cell">{{ formatHour(hour) }}</div>
              <div
                v-for="day in weekDays"
                :key="`${day.value}-${hour}`"
                class="schedule-cell"
                :class="getScheduleCellClass(teacher.id, day.value, hour)"
              >
                <div class="cell-content">
                  {{ getScheduleCellText(teacher.id, day.value, hour) }}
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="teachers.length === 0" class="empty-state">
      <el-empty description="暂无教师数据" />
    </div>

    <!-- 图例 -->
    <div class="legend">
      <div class="legend-item">
        <div class="legend-color unavailable"></div>
        <span>不可上课</span>
      </div>
      <div class="legend-item">
        <div class="legend-color available"></div>
        <span>可上课</span>
      </div>
      <div class="legend-item">
        <div class="legend-color scheduled"></div>
        <span>已排课</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Props
const props = defineProps({
  teachers: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['view-schedule'])

// 响应式数据
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

const hours = Array.from({ length: 18 }, (_, i) => i + 6) // 6-23点

// 模拟的教师时间表数据（实际应该从API获取）
const teacherSchedules = ref(new Map())

// 方法
const handleViewSchedule = (teacher) => {
  emit('view-schedule', teacher)
}

const formatHour = (hour) => {
  return `${hour.toString().padStart(2, '0')}:00`
}

const getScheduleCellClass = (teacherId, weekday, hour) => {
  const status = getScheduleStatus(teacherId, weekday, hour)
  return `status-${status}`
}

const getScheduleCellText = (teacherId, weekday, hour) => {
  const status = getScheduleStatus(teacherId, weekday, hour)
  const textMap = {
    unavailable: '×',
    available: '○',
    scheduled: '●'
  }
  return textMap[status] || '×'
}

const getScheduleStatus = (teacherId, weekday, hour) => {
  // 这里应该从实际的时间表数据中获取状态
  // 现在使用模拟数据
  const schedule = teacherSchedules.value.get(teacherId)
  if (!schedule) {
    return 'unavailable'
  }
  
  const timeSlot = schedule.find(slot => 
    slot.weekday === weekday && slot.hour === hour
  )
  
  return timeSlot?.status || 'unavailable'
}

// 生命周期
onMounted(async () => {
  // 获取所有教师的时间表数据
  if (props.teachers && props.teachers.length > 0) {
    try {
      // 为每个教师获取时间表
      for (const teacher of props.teachers) {
        const timeSlots = await teachingGroupStore.fetchTeacherTimeSlots(teacher.id)

        // 将时间段数据转换为时间表格式
        const schedule = convertTimeSlotsToSchedule(timeSlots)
        teacherSchedules.value.set(teacher.id, schedule)
      }
    } catch (error) {
      console.error('获取教师时间表失败:', error)
      // 如果API调用失败，设置默认值
      props.teachers.forEach(teacher => {
        teacherSchedules.value.set(teacher.id, [])
      })
    }
  }
})

// 将时间段数据转换为时间表格式
const convertTimeSlotsToSchedule = (timeSlots) => {
  const schedule = []

  // 初始化所有时间槽为不可用
  for (let weekday = 0; weekday <= 6; weekday++) {
    for (let hour = 6; hour <= 23; hour++) {
      schedule.push({
        weekday,
        hour,
        status: 'unavailable'
      })
    }
  }

  // 根据时间段数据更新状态
  if (timeSlots && timeSlots.length > 0) {
    timeSlots.forEach(slot => {
      const startHour = parseInt(slot.startTime.split(':')[0])
      const endHour = parseInt(slot.endTime.split(':')[0])

      for (let hour = startHour; hour < endHour; hour++) {
        const scheduleItem = schedule.find(s => s.weekday === slot.weekday && s.hour === hour)
        if (scheduleItem) {
          scheduleItem.status = slot.status || 'available'
        }
      }
    })
  }

  return schedule
}
</script>

<style lang="scss" scoped>
.teacher-schedule-view {
  .teacher-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .teacher-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .teacher-info {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .teacher-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .teacher-phone {
            font-size: 12px;
            color: #909399;
          }
        }
      }

      .schedule-table {
        .time-header {
          display: grid;
          grid-template-columns: 60px repeat(7, 1fr);
          gap: 1px;
          margin-bottom: 1px;

          .header-cell {
            background-color: #f5f7fa;
            padding: 8px 4px;
            text-align: center;
            font-size: 12px;
            font-weight: 600;
            color: #606266;
            border: 1px solid #ebeef5;
          }
        }

        .time-row {
          display: grid;
          grid-template-columns: 60px repeat(7, 1fr);
          gap: 1px;
          margin-bottom: 1px;

          .time-cell {
            background-color: #fafafa;
            padding: 4px;
            text-align: center;
            font-size: 11px;
            color: #606266;
            border: 1px solid #ebeef5;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .schedule-cell {
            border: 1px solid #ebeef5;
            min-height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;

            .cell-content {
              font-size: 12px;
              font-weight: 600;
            }

            &.status-unavailable {
              background-color: #f56c6c;
              color: white;
            }

            &.status-available {
              background-color: #67c23a;
              color: white;
            }

            &.status-scheduled {
              background-color: #409eff;
              color: white;
            }
          }
        }
      }
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }

  .legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #606266;

      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 2px;

        &.unavailable {
          background-color: #f56c6c;
        }

        &.available {
          background-color: #67c23a;
        }

        &.scheduled {
          background-color: #409eff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .teacher-schedule-view {
    .teacher-cards {
      grid-template-columns: 1fr;

      .teacher-card {
        .schedule-table {
          .time-header,
          .time-row {
            grid-template-columns: 50px repeat(7, 1fr);
          }

          .time-cell {
            font-size: 10px;
            padding: 2px;
          }

          .schedule-cell {
            min-height: 20px;

            .cell-content {
              font-size: 10px;
            }
          }
        }
      }
    }

    .legend {
      flex-wrap: wrap;
      gap: 12px;
    }
  }
}
</style>
