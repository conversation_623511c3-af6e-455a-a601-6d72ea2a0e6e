<template>
  <div class="app-container">
    <el-card class="box-card">

      <!-- 锁定信息提示 -->
      <el-alert
        v-if="lockedInfo"
        :title="lockedInfo"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />

      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
        <el-form-item label="学生姓名" prop="studentName">
          <el-input
            v-model="queryParams.studentName"
            placeholder="请输入学生姓名"
            clearable
            :disabled="isStudentLocked"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学生手机" prop="studentPhone">
          <el-input
            v-model="queryParams.studentPhone"
            placeholder="请输入学生手机号"
            clearable
            :disabled="isStudentLocked"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select
            v-model="queryParams.subject"
            placeholder="请选择学科"
            clearable
            :disabled="isCourseLocked"
            style="width: 150px"
            @change="handleSubjectChange"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        <el-form-item label="课型" prop="specification">
          <el-select
            v-model="queryParams.specification"
            placeholder="请选择课型"
            clearable
            :disabled="isCourseLocked"
            style="width: 150px"
          >
            <el-option
              v-for="option in availableSpecifications"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="老师" prop="teacherName">
          <el-input
            v-model="queryParams.teacherName"
            placeholder="请输入老师姓名"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="课消类型" prop="courseHoursType">
          <el-select
            v-model="queryParams.courseHoursType"
            placeholder="请选择课消类型"
            clearable
            multiple
            style="width: 200px"
          >
            <el-option label="试听" value="试听" />
            <el-option label="正式" value="正式" />
            <el-option label="续费" value="续费" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-select
            v-model="queryParams.grade"
            placeholder="请选择年级"
            clearable
            multiple
            style="width: 200px"
          >
            <el-option
              v-for="grade in gradeOptions"
              :key="grade.value"
              :label="grade.label"
              :value="grade.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="课消时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="Download" @click="handleExport" :loading="exportLoading">导出</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="consumptionList" border>
        <el-table-column label="学生姓名" prop="studentName" width="100" />
        <el-table-column label="学生手机" prop="studentPhone" width="120" />
        <el-table-column label="年级" prop="grade" width="80" align="center">
          <template #default="scope">
            <span v-if="scope.row.grade">{{ GRADE_MAP[scope.row.grade] }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column label="学科" prop="subject" width="80" />
        <el-table-column label="课型" prop="specification" width="100" />
        <el-table-column label="性质" prop="nature" width="80" />
        <el-table-column label="课消类型" prop="courseHoursType" width="100" align="center">
          <template #default="scope">
            <el-tag
              :type="getCourseHoursTypeTagType(scope.row.courseHoursType)"
              size="small"
            >
              {{ scope.row.courseHoursType || '-' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="消费课时"
          prop="consumedHours"
          width="100"
          align="right"
        />
        <el-table-column label="课时批次" prop="batchNo" width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.batchNo">{{ scope.row.batchNo }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column label="老师" prop="teacherName" width="100" />
        <el-table-column label="教学组" prop="teachingGroupName" width="120" />
        <el-table-column label="教学组组长" prop="teachingGroupLeaderName" width="120" />
        <el-table-column label="销售" prop="salesName" width="100" />
        <el-table-column label="销售组" prop="salesGroupName" width="120" />
        <el-table-column label="销售组组长" prop="salesGroupLeaderName" width="120" />
        <el-table-column label="课程ID" prop="courseId" width="120" />
        <el-table-column label="课消时间" prop="consumptionTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.consumptionTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          prop="remark"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="创建时间" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="CourseConsumptionQuery">
import { ref, computed, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { parseTime } from "@/utils/ruoyi";
import { listConsumptionRecords, exportConsumptionRecords } from "@/api/management/studentCourseHours";
import { useRoute } from 'vue-router';
import { GRADE_OPTIONS, GRADE_MAP } from "@/utils/gradeUtils";
import { exportExcel, formatExportParams, generateExportFileName } from "@/utils/download";

// 接收props参数（对话框模式）
const props = defineProps({
  studentId: {
    type: [String, Number],
    default: null
  },
  courseHoursId: {
    type: String,
    default: ''
  },
  studentName: {
    type: String,
    default: ''
  },
  studentPhone: {
    type: String,
    default: ''
  },
  subject: {
    type: String,
    default: ''
  },
  specification: {
    type: String,
    default: ''
  },
  isDialogMode: {
    type: Boolean,
    default: false
  }
});

// 获取路由参数（独立页面模式）
const route = useRoute();
const routeParams = {
  studentId: route.query.studentId || null,
  courseHoursId: route.query.courseHoursId || null,
  studentName: route.query.studentName || '',
  studentPhone: route.query.studentPhone || '',
  subject: route.query.subject || '',
  specification: route.query.specification || ''
};

// 根据模式选择参数来源
const effectiveParams = computed(() => {
  if (props.isDialogMode) {
    return {
      studentId: props.studentId,
      courseHoursId: props.courseHoursId,
      studentName: props.studentName,
      studentPhone: props.studentPhone,
      subject: props.subject,
      specification: props.specification
    };
  }
  return routeParams;
});

// 响应式数据
const loading = ref(true);
const exportLoading = ref(false);
const consumptionList = ref([]);
const total = ref(0);
const dateRange = ref([]);

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  queryParams.value.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  studentId: null,
  courseHoursId: "",
  studentName: "",
  studentPhone: "",
  subject: "",
  specification: "",
  teacherName: "",
  courseHoursType: [],
  grade: [],
  status: "",
  startTime: "",
  endTime: "",
});

// 年级选项
const gradeOptions = ref(GRADE_OPTIONS);

// 计算属性
const isStudentLocked = computed(() => !!effectiveParams.value.studentId);
const isCourseLocked = computed(() => !!(effectiveParams.value.subject && effectiveParams.value.specification));
const isCourseHoursLocked = computed(() => !!effectiveParams.value.courseHoursId);

const lockedInfo = computed(() => {
  if (isCourseHoursLocked.value) {
    return `已锁定查询条件：学生 ${effectiveParams.value.studentName}，课程 ${effectiveParams.value.subject} - ${effectiveParams.value.specification}，课时记录 ${effectiveParams.value.courseHoursId}`;
  } else if (isCourseLocked.value) {
    return `已锁定查询条件：学生 ${effectiveParams.value.studentName}，课程 ${effectiveParams.value.subject} - ${effectiveParams.value.specification}`;
  } else if (isStudentLocked.value) {
    return `已锁定查询条件：学生 ${effectiveParams.value.studentName}`;
  }
  return null;
});

// 初始化查询参数
const initQueryParams = () => {
  queryParams.value.studentId = effectiveParams.value.studentId;
  queryParams.value.courseHoursId = effectiveParams.value.courseHoursId;
  queryParams.value.studentName = effectiveParams.value.studentName;
  queryParams.value.studentPhone = effectiveParams.value.studentPhone;
  queryParams.value.subject = effectiveParams.value.subject;
  queryParams.value.specification = effectiveParams.value.specification;
};

// 查询表单引用
const queryRef = ref();

// 获取列表数据
const getList = async () => {
  loading.value = true;

  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.value.startTime = dateRange.value[0];
    queryParams.value.endTime = dateRange.value[1];
  } else {
    queryParams.value.startTime = "";
    queryParams.value.endTime = "";
  }

  try {
    const response = await listConsumptionRecords(queryParams.value);
    consumptionList.value = response.rows;
    total.value = response.total;
    console.log('课消记录查询结果:', response);
    console.log('数据条数:', response.total);
  } catch (error) {
    console.error("获取课消记录失败:", error);
    ElMessage.error("获取课消记录失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  queryRef.value?.resetFields();
  dateRange.value = [];

  // 重置时保持锁定的字段
  initQueryParams();

  handleQuery();
};

// 监听有效参数变化，重新初始化查询参数并刷新数据
watch(() => effectiveParams.value, (newParams, oldParams) => {
  // 检查关键参数是否发生变化
  const keyChanged =
    newParams.studentId !== oldParams?.studentId ||
    newParams.courseHoursId !== oldParams?.courseHoursId ||
    newParams.subject !== oldParams?.subject ||
    newParams.specification !== oldParams?.specification;

  if (keyChanged) {
    initQueryParams();
    getList();
  }
}, { deep: true, immediate: false });

// 获取课消类型标签类型
const getCourseHoursTypeTagType = (type) => {
  switch (type) {
    case '试听':
      return 'info';
    case '正式':
      return 'success';
    case '续费':
      return 'warning';
    default:
      return '';
  }
};

// 导出课消记录
const handleExport = async () => {
  try {
    // 确认导出
    await ElMessageBox.confirm(
      `确认导出当前查询条件下的课消记录吗？`,
      '导出确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    exportLoading.value = true;

    // 准备导出参数
    const exportParams = { ...queryParams.value };
    
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      exportParams.startTime = dateRange.value[0];
      exportParams.endTime = dateRange.value[1];
    } else {
      exportParams.startTime = "";
      exportParams.endTime = "";
    }

    // 移除分页参数，导出全部数据
    delete exportParams.pageNum;
    delete exportParams.pageSize;

    // 格式化导出参数
    const formattedParams = formatExportParams(exportParams);

    // 生成文件名
    const fileName = generateExportFileName('课消记录', {
      createTimeStart: formattedParams.startTime,
      createTimeEnd: formattedParams.endTime
    });

    // 添加调试信息
    console.log('导出参数:', formattedParams);
    
    // 参考课程查询的导出实现
    try {
      const response = await exportConsumptionRecords(formattedParams);
      console.log('API响应:', response);
      
      // 创建下载链接 - 直接使用response而不是response.data
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      
      if (blob.size === 0) {
        ElMessage.error('导出的文件为空，可能没有符合条件的数据');
        return;
      }
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // 生成文件名
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '');
      link.download = `课消记录_${dateStr}_${timeStr}.xlsx`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      ElMessage.success('导出成功');
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      ElMessage.error('导出失败：' + (apiError.message || '未知错误'));
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出操作失败:', error);
      ElMessage.error('导出操作失败');
    }
  } finally {
    exportLoading.value = false;
  }
};

// 初始化
onMounted(() => {
  initQueryParams();
  getList();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}
</style>
