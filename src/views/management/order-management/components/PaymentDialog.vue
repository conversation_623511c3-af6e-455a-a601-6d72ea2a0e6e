<template>
  <el-dialog
    title="支付确认"
    v-model="visible"
    width="600px"
    append-to-body
    :before-close="handleClose"
    class="payment-dialog"
  >
    <div v-if="transaction" class="payment-content">
      <!-- 交易信息 -->
      <el-card class="transaction-info" shadow="never">
        <template #header>
          <span class="card-title">交易信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="流水号">
            <el-tag type="info" size="small">{{ transaction.cusTrxSeq }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="期数">
            <el-tag type="primary" size="small">第{{ transaction.trxIdx }}期</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付金额">
            <span class="amount-text">¥{{ (transaction.trxAmt / 100).toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="交易状态">
            <el-tag :type="getStatusType(transaction.trxStatus)" size="small">
              {{ transaction.trxStatus }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(transaction.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 使用统一的支付方式组件 -->
      <PaymentMethodSelector
        ref="paymentMethodRef"
        :amount="transaction ? transaction.trxAmt : 0"
        :transaction="transaction"
        :show-confirm-button="false"
        @generate-payment="handleGeneratePayment"
        @copy-link="handleCopyLink"
        @send-wechat="handleSendWechat"
        @method-change="handleMethodChange"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="generatePayment" :loading="loading" v-hasPermi="['order:pay']">
          {{ qrCodeUrl || paymentLink ? '重新生成' : '生成支付' }}
        </el-button>
        <el-button v-if="qrCodeUrl || paymentLink" type="success" @click="confirmPayment">
          确认支付完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateTime } from '@/utils/date.js'
import PaymentMethodSelector from '@/components/PaymentMethodSelector.vue'
import {generatePaymentApi, sendWechatPaymentTrxApi} from '@/api/management/order.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  transaction: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success', 'generate-qr', 'copy-link', 'send-wechat'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const paymentMethodRef = ref(null)
const loading = ref(false)

// 方法
const handleClose = () => {
  visible.value = false
}

const getStatusType = (status) => {
  const statusMap = {
    '未支付': 'warning',
    '已付款': 'success',
    '已取消': 'danger',
    '已退款': 'info',
    '部分支付': 'primary'
  }
  return statusMap[status] || 'info'
}

const generatePayment = async () => {
  if (paymentMethodRef.value) {
    await paymentMethodRef.value.generatePayment()
  }
}

const confirmPayment = async () => {
  try {
    await ElMessageBox.confirm(
      '请确认用户已完成支付，确认后将更新订单状态。',
      '确认支付',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    emit('success', props.transaction)
    visible.value = false
  } catch (error) {
    // 用户取消确认
  }
}

// 支付组件事件处理
const handleGeneratePayment = async (paymentInfo) => {
  if (!props.transaction) return

  try {
    const response = await generatePaymentApi(props.transaction.id)
    const paymentData = response.data

    emit('generate-qr', {
      transaction: props.transaction,
      payMethod: paymentInfo.payMethod,
      qrCodeUrl: `data:image/png;base64,${paymentData.qrCodeBase64}`,
      paymentLink: paymentData.payUrl
    })

    const result =  {
      qrCodeUrl: `data:image/png;base64,${paymentData.qrCodeBase64}`,
      paymentLink: paymentData.payUrl
    }
    // 子节点
    paymentMethodRef.value.generatePaymentResult(result)
    return result;
  } catch (error) {
    ElMessage.error('生成支付信息失败')
    throw error
  }
}

const handleCopyLink = (paymentLink) => {
  emit('copy-link', paymentLink)
}

const handleSendWechat = async (wechatInfo) => {
  // emit('send-wechat', {
  //   paymentLink: wechatInfo.paymentLink,
  //   payMethod: wechatInfo.payMethod,
  //   transaction: props.transaction
  // })
  await sendWechatPaymentTrxApi(props.transaction.id)
  ElMessage.success('微信消息发送成功')

}

const handleMethodChange = (method) => {
  // 支付方式变化处理
}
</script>

<style scoped>
.payment-dialog {
  .payment-content {
    max-height: 60vh;
    overflow-y: auto;
  }
}

.transaction-info {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.amount-text {
  font-size: 18px;
  font-weight: bold;
  color: #67C23A;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>