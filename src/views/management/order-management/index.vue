<template>
  <div class="order-management-container">
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline label-width="80px">
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="学生姓名">
          <el-input
            v-model="searchForm.studentName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="销售员">
          <el-input
            v-model="searchForm.salerName"
            placeholder="请输入销售员姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="请选择订单状态"
            clearable
            style="width: 150px"
          >
            <el-option label="未付款" value="未付款" />
            <el-option label="已全额支付" value="已全额支付" />
            <el-option label="已部分支付" value="已部分支付" />
            <el-option label="已取消" value="已取消" />
            <el-option label="已退款" value="已退款" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select
            v-model="searchForm.trxMethod"
            placeholder="请选择支付方式"
            clearable
            style="width: 150px"
          >
            <el-option label="一次性支付" value="一次性支付" />
            <el-option label="分期支付" value="分期支付" />
          </el-select>
        </el-form-item>
        <el-form-item label="核销状态">
          <el-select
              v-model="searchForm.writerOffStatus"
              placeholder="请选择核销状态"
              clearable
              style="width: 150px"
          >
            <el-option label="待核销" value="待核销" />
            <el-option label="核销成功" value="核销成功" />
            <el-option label="核销失败" value="核销失败" />
            <el-option label="自动核销" value="自动核销" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新建订单
          </el-button>
          <el-button type="success" @click="handleExportOrders"
          :loading="exportLoading"
                     v-hasPermi="['order:manager:export']">
            <el-icon><Download /></el-icon>
            导出订单
          </el-button>
<!--          <el-button type="info" @click="handleExportTrx">-->
<!--            <el-icon><Document /></el-icon>-->
<!--            导出流水-->
<!--          </el-button>-->
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <!-- 批量操作工具栏 -->
      <div class="batch-operations" style="margin-bottom: 16px;">
<!--        <el-button-->
<!--          type="danger"-->
<!--          :disabled="selectedOrders.length === 0"-->
<!--          @click="handleBatchCancel"-->
<!--        >-->
<!--          批量取消 ({{ selectedOrders.length }})-->
<!--        </el-button>-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          @click="handleWriteOff"-->
<!--        >-->
<!--          <el-icon><Printer /></el-icon>&nbsp;订单核销-->
<!--        </el-button>-->
<!--        <el-button-->
<!--            type="success"-->
<!--            @click="handleWriteOff"-->
<!--        >-->
<!--          <el-icon><RefreshLeft /></el-icon>&nbsp;抖店订单同步-->
<!--        </el-button>-->
      </div>

      <el-table
        v-loading="loading"
        :data="orderList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
<!--        <el-table-column type="selection" width="55" align="center" />-->
        <el-table-column label="订单号" prop="no" width="300" >
          <template #default="{ row }">
            <el-tag type="success" v-if="row.orderType === '首购'">{{ row.orderType }}</el-tag>
            <el-tag type="danger" v-else>{{ row.orderType }}</el-tag>
            {{ row.no }}
          </template>
        </el-table-column>
        <el-table-column label="订单标题" prop="body" min-width="200" show-overflow-tooltip />
        <el-table-column label="学生姓名" prop="studentName" width="120" />
        <el-table-column label="学生手机号" prop="studentPhone" width="120" />
        <el-table-column label="老师" prop="teacherName" width="120" />
        <el-table-column label="教学组长" prop="groupLeaderName" width="120" />
        <el-table-column label="总课时" prop="studentPhone" width="120" >
          <template #default="{ row }">
            {{ (Number(row.buyHours) + Number(row.bonusHours)) }}
          </template>
        </el-table-column>
        <el-table-column label="购买课时" prop="buyHours" width="120" />
        <el-table-column label="赠送课时" prop="bonusHours" width="120" />
        <el-table-column label="是否含教材费" prop="studentPhone" width="120" >
          <template #default="{ row }">
            {{ row.hasMaterialFee ? '是' : '否'}}
            <span v-if="row.hasMaterialFee">（￥{{ (row.materialFee / 100).toFixed(2) }}）</span>
          </template>
        </el-table-column>
        <el-table-column label="销售员" prop="salerName" width="120" />
        <el-table-column label="订单金额" prop="totalAmt" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.totalAmt / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="已付款金额" prop="amtPaid" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.amtPaid / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="未付款金额" prop="amtUnpaid" width="120" align="center">
          <template #default="{ row }">
            ¥{{ (row.amtUnpaid / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="支付方式" prop="trxMethod" width="120" align="center" />
        <el-table-column label="订单状态" prop="orderStatus" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.orderStatus)" size="small">
              {{ row.orderStatus }}
          </el-tag>
        </template>
      </el-table-column>
        <el-table-column label="核销状态" prop="writerOffStatus" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getWriterOffStatusTagType(row.writerOffStatus)" size="small">
              {{ row.writerOffStatus }}
            </el-tag>
          </template>
        </el-table-column>
      <el-table-column label="签署状态" prop="signStatus" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getSignStatusType(row.signStatus)" size="small">
            {{ row.signStatus || '未签署' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180">
        <template #default="{ row }">
          {{ row.createTime }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="320" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            size="small"
            @click="handleView(row)"
          >
            查看详情
          </el-button>
          <el-button
            v-if="row.orderStatus === '未付款'"
            type="success"
            link
            size="small"
            @click="handlePay(row)"
            v-hasPermi="['order:pay']"
          >
            支付
          </el-button>
          <el-button
              v-if="row.orderStatus === '已部分支付'"
              type="success"
              link
              size="small"
              @click="handlePay(row)"
              v-hasPermi="['order:pay']"
          >
            继续支付
          </el-button>
          <el-button
            v-if="canSign(row)"
            type="warning"
            link
            size="small"
            @click="handleSign(row)"
            v-hasPermi="['order:manager:sign']"
          >
            合同签署
          </el-button>
          <el-button
            v-if="canRefund(row)"
            type="warning"
            link
            @click="handleRefund(row)"
            v-hasPermi="['order:manager:refund-apply']"
          >
            申请退款
          </el-button>
          <el-button
            v-if="canFinancialRefund(row)"
            type="danger"
            link
            size="small"
            @click="handleFinancialRefund(row)"
            v-hasPermi="['refund:records:financial-refund']"
          >
            财务退款
          </el-button>
          <el-button
            v-if="canWriteOff(row)"
            type="info"
            link
            size="small"
            @click="handleWriteOff(row)"
            v-hasPermi="['order:writeoff:apply']"
          >
            核销
          </el-button>
          <el-button
            v-if="row.orderStatus === '未付款'"
            type="danger"
            size="small"
            link
            @click="handleCancel(row)"
            v-hasPermi="['order:manager:cancel']"
          >
            取消订单
          </el-button>
          <!-- <el-button
              v-if="row.writerOffStatus === '待核销'"
              type="primary"
              link
              size="small"
              @click="handleOrderWriteOff(row)"
              v-hasPermi="['order:manager:writerOff']"
          >
            订单核销
          </el-button> -->
<!--          <el-button-->
<!--            v-if="canWriteOff(row)"-->
<!--            type="primary"-->
<!--            link-->
<!--            @click="handleWriteOff(row)"-->
<!--          >-->
<!--            核销-->
<!--          </el-button>-->
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="detailOpen"
      :order-detail="orderDetail"
      @refresh="fetchData"
      @edit="handleEditOrder"
      @pay-transaction="handlePayTransaction"
    />

    <!-- 支付对话框 -->
    <PaymentDialog
      v-model="payOpen"
      :transaction="currentTransaction"
      @success="handlePaymentSuccess"
      @generate-qr="handleGenerateQR"
      @copy-link="handleCopyLink"
      @send-wechat="handleSendWechat"
    />

    <!-- 合同签署对话框 -->
    <ContractSignDialog
      v-model="contractSignVisible"
      :order-info="currentOrder"
      @success="handleContractSignSuccess"
    />

    <!-- 订单退款对话框 -->
    <OrderRefundDialog
      v-model="refundVisible"
      :order-info="currentOrder"
      @success="handleRefundSuccess"
    />


    <!-- 财务退款对话框 -->
    <FinancialRefundDialog
        v-model="financialRefundVisible"
        :order-info="currentOrder"
        @success="handleFinancialRefundSuccess"
    />
    
    <!-- 学生选择对话框 -->
    <el-dialog
      v-model="studentSelectVisible"
      title="选择学生进行订单核销"
      width="1000px"
      @close="handleStudentSelectCancel"
    >
      <div class="student-select-content">
        <el-card shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span class="course-info-title">产品信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="产品名称">
              {{ currentOrderForWriteOff?.productName }}
            </el-descriptions-item>
            <el-descriptions-item label="学科信息">
              {{ currentOrderForWriteOff?.subject }}
              {{ currentOrderForWriteOff?.courseType }}
            </el-descriptions-item>
            <el-descriptions-item label="适用年级">
              <el-tag size="small" style="margin-right: 10px" v-for="grade in currentOrderForWriteOff?.applicableGrades">{{ grade }}</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 搜索框 -->
        <el-form :model="studentSearchForm" inline style="margin-bottom: 20px;">
          <el-form-item label="学生姓名">
            <el-input
              v-model="studentSearchForm.name"
              placeholder="请输入学生姓名"
              clearable
              style="width: 200px"
              @keyup.enter="searchStudents"
            />
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input
              v-model="studentSearchForm.phone"
              placeholder="请输入手机号码"
              clearable
              style="width: 200px"
              @keyup.enter="searchStudents"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchStudents">搜索</el-button>
            <el-button @click="resetStudentSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 学生列表 -->
        <el-table
          v-loading="studentLoading"
          :data="studentList"
          highlight-current-row
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column prop="name" label="学生姓名" width="120" />
          <el-table-column prop="phone" label="手机号码" width="130" />
          <el-table-column prop="grade" label="年级" width="100">
            <template #default="{ row }">
              <el-tag type="success" size="small">{{ getGradeText(row.grade) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="school" label="家长信息" show-overflow-tooltip >
            <template #default="{ row }">
              {{ row.parentName }} - {{ row.parentPhone }}
            </template>
          </el-table-column>
          <el-table-column prop="salesName" label="销售人员" width="120">
            <template #default="{ row }">
              <span v-if="row.salesName">{{ row.salesName }}</span>
              <el-tag v-else type="warning" size="small">未分配</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-tooltip v-if="currentOrderForWriteOff?.applicableGrades && !currentOrderForWriteOff?.applicableGrades?.includes(getGradeText(row.grade))"
                content="该订单不适用于该学生年级"
                placement="top"
              >
                <el-button type="primary" size="small" @click="handleStudentSelect(row)" disabled>
                  选择
                </el-button>
              </el-tooltip>
              <el-tooltip v-else-if="!row.parentName || !row.parentPhone"
                          content="该学生家长信息未完善"
                          placement="top"
              >
                <el-button type="primary" size="small" @click="handleStudentSelect(row)" disabled>
                  选择
                </el-button>
              </el-tooltip>
              <el-button type="primary" size="small" @click="handleStudentSelect(row)" v-else>
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container" style="margin-top: 20px;">
          <el-pagination
            v-model:current-page="studentPagination.pageNum"
            v-model:page-size="studentPagination.pageSize"
            :total="studentPagination.total"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleStudentSizeChange"
            @current-change="handleStudentCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 订单核销申请对话框 -->
    <OrderWriteOffDialog
      v-model="writeoffDialogVisible"
      :order-info="currentOrderForWriteOff"
      @success="handleWriteoffSuccess"
    />
  </div>
</template>

<script setup lang="ts" name="OrderManagement">
import {onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Plus, Refresh, Search} from '@element-plus/icons-vue'
import {useRouter} from 'vue-router'
import {
  batchCancelOrdersApi,
  cancelOrderApi,
  getOrderManagerDetailApi,
  getOrderManagerListApi,
  getOrderStatisticsApi,
  getOrderTransactionsApi,
  managerExportOrdersApi
} from '@/api/management/order-manager'
import ContractSignDialog from './components/ContractSignDialog.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import PaymentDialog from './components/PaymentDialog.vue'
import OrderRefundDialog from './components/OrderRefundDialog.vue'
import FinancialRefundDialog from './components/FinancialRefundDialog.vue'
import OrderWriteOffDialog from "@/components/OrderWriteOff/OrderWriteOffDialog.vue"
import {getSalesStudentListApi} from '@/api/management/salesStudent'

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const detailOpen = ref(false)
const payOpen = ref(false)
const dateRange = ref([])
const selectedOrders = ref([]) // 新增：选中的订单
const statistics = ref(null) // 新增：统计信息
const router = useRouter();
const exportLoading = ref(false)

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  studentName: '',
  salerName: '',
  orderStatus: '',
  trxMethod: '',
  writerOffStatus: ''
})

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const orderDetail = ref(null)
const currentTransaction = ref(null)
const contractSignVisible = ref(false)
const currentOrder = ref(null)
const refundVisible = ref(false)
const trxExportVisible = ref(false)
// 学生选择相关
const studentSelectVisible = ref(false)
const studentLoading = ref(false)
const studentList = ref([])
const selectedStudent = ref(null)
const currentOrderForWriteOff = ref(null)

// 学生搜索表单
const studentSearchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  phone: ''
})

// 学生分页
const studentPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 订单核销相关
const writeoffDialogVisible = ref(false)

// 方法
const fetchData = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      orderNo: searchForm.orderNo,
      studentName: searchForm.studentName,
      salerName: searchForm.salerName,
      orderStatus: searchForm.orderStatus,
      trxMethod: searchForm.trxMethod
    }
    
    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.beginTime = dateRange.value[0]
      params.endTime = dateRange.value[1]
    }
    
    const response = await getOrderManagerListApi(params)
    if (response.code === 200) {
      orderList.value = response.rows || []
      pagination.total = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageNum = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    studentName: '',
    salerName: '',
    orderStatus: '',
    trxMethod: ''
  })
  dateRange.value = []
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

// 新增：批量取消订单
const handleBatchCancel = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要取消的订单')
    return
  }

  await ElMessageBox.confirm('确认要批量取消选中的订单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })

  const orderIds = selectedOrders.value.map(order => order.id)
  const response = await batchCancelOrdersApi(orderIds)

  if (response.code === 200) {
    ElMessage.success(response.data || '批量取消成功')
    await fetchData()
    selectedOrders.value = []
  } else {
    ElMessage.error(response.msg || '批量取消失败')
  }
}

// 新增：获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await getOrderStatisticsApi()
    if (response.code === 200) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

const handleAdd = () => {
  // 跳转到订单创建页面
  router.push('/product/order')
}

const handleView = async (row) => {
  try {
    const response = await getOrderManagerDetailApi(row.id)
    if (response.code === 200) {
      orderDetail.value = response.data.order
      orderDetail.value.transactions = response.data.transactions
      orderDetail.value.saler = response.data.saler
      orderDetail.value.student = response.data.student
      orderDetail.value.product = response.data.product
      detailOpen.value = true
    } else {
      ElMessage.error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

const handlePay = async (row) => {
  // 获取交易流水
  const transactionsResponse = await getOrderTransactionsApi(row.id)
  const transaction = transactionsResponse.data
  if (transaction) {
    currentTransaction.value = transaction
    payOpen.value = true
  } else {
    ElMessage.error('未找到交易流水')
  }
}

const handleCancel = async (row) => {
  try {
    await ElMessageBox.confirm('确认要取消该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await cancelOrderApi(row.id)
    ElMessage.success('取消成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消失败')
    }
  }
}

const handlePayTransaction = (transaction) => {
  currentTransaction.value = transaction
  payOpen.value = true
}



const getStatusType = (status) => {
  const statusMap = {
    '未付款': 'warning',
    '已付款': 'success',
    '已取消': 'danger',
    '已退款': 'info',
    '退款待审核': 'primary'
  }
  return statusMap[status] || 'info'
}

function getWriterOffStatusTagType(status) {
  const typeMap = {
    '待核销': 'primary',
    '核销成功': 'success',
    '核销失败': 'danger'
  };
  return typeMap[status] || 'info';
}

const getSignStatusType = (signStatus) => {
  const statusMap = {
    '未签署': 'warning',
    '已签署': 'success',
    '签署中': 'primary',
    '签署失败': 'danger'
  }
  return statusMap[signStatus] || 'warning'
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchData()
}

const canSign = (row) => {
  // 判断是否可以签署合同：已付款且未签署
  return row.orderStatus === '已全额支付' && row.signStatus === '未签署'
}

const canRefund = (row) => {
  // 判断是否可以退款：已付款状态
  return ['已全额支付', '已全额付款', '已部分支付', '已部分付款', '已付款'].includes(row.orderStatus)
}

// 新增：判断是否可以核销
const canWriteOff = (row) => {
  // 只有未付款的订单才能核销
  return row.orderStatus === '未付款'
}

const handleSign = (row) => {
  currentOrder.value = row
  contractSignVisible.value = true
}

const handleContractSignSuccess = () => {
  fetchData() // 刷新列表
}

// 处理退款
const handleRefund = (row) => {
  currentOrder.value = row
  refundVisible.value = true
}

// 处理退款成功
const handleRefundSuccess = () => {
  fetchData()
}

// 年级选项
const GRADE_OPTIONS = [
  { label: '一年级', value: 1 },
  { label: '二年级', value: 2 },
  { label: '三年级', value: 3 },
  { label: '四年级', value: 4 },
  { label: '五年级', value: 5 },
  { label: '六年级', value: 6 },
  { label: '初一', value: 7 },
  { label: '初二', value: 8 },
  { label: '初三', value: 9 },
  { label: '高一', value: 10 },
  { label: '高二', value: 11 },
  { label: '高三', value: 12 }
]

// 获取年级文本
const getGradeText = (grade) => {
  const option = GRADE_OPTIONS.find(item => item.value === parseInt(grade))
  return option ? option.label : grade
}

// 处理订单核销
const handleOrderWriteOff = (row) => {
  currentOrderForWriteOff.value = row
  studentSelectVisible.value = true
  getStudentList()
}

// 获取学生列表
const getStudentList = async () => {
  studentLoading.value = true
  try {
    const params = {
      pageNum: studentPagination.pageNum,
      pageSize: studentPagination.pageSize,
      name: studentSearchForm.name,
      phone: studentSearchForm.phone
    }
    
    const response = await getSalesStudentListApi(params)
    studentList.value = response.data?.records || []
    studentPagination.total = response.data?.total || 0
  } catch (error) {
    ElMessage.error('获取学生列表失败')
  } finally {
    studentLoading.value = false
  }
}

// 搜索学生
const searchStudents = () => {
  studentPagination.pageNum = 1
  getStudentList()
}

// 重置学生搜索
const resetStudentSearch = () => {
  Object.assign(studentSearchForm, {
    pageNum: 1,
    pageSize: 10,
    name: '',
    phone: ''
  })
  searchStudents()
}

// 学生分页大小变化
const handleStudentSizeChange = (size) => {
  studentPagination.pageSize = size
  studentPagination.pageNum = 1
  getStudentList()
}

// 学生分页页码变化
const handleStudentCurrentChange = (page) => {
  studentPagination.pageNum = page
  getStudentList()
}

// 选择学生
const handleStudentSelect = (student) => {
  selectedStudent.value = student
  studentSelectVisible.value = false
  writeoffDialogVisible.value = true
  console.log('选择的学生信息:', student) // 添加日志，确认学生信息是否正确
}

// 取消学生选择
const handleStudentSelectCancel = () => {
  studentSelectVisible.value = false
  selectedStudent.value = null
  currentOrderForWriteOff.value = null
}

// 订单核销成功回调
const handleWriteoffSuccess = () => {
  ElMessage.success('订单核销成功')
  fetchData() // 刷新订单列表
}

// 处理导出订单
const handleExportOrders = () => {
  exportLoading.value = true
  const params = {
    orderNo: searchForm.orderNo,
    studentName: searchForm.studentName,
    salerName: searchForm.salerName,
    orderStatus: searchForm.orderStatus,
    trxMethod: searchForm.trxMethod
  }

  // 添加时间范围参数
  if (dateRange.value && dateRange.value.length === 2) {
    params.beginTime = dateRange.value[0]
    params.endTime = dateRange.value[1]
  }
  managerExportOrdersApi(params).then(response => {
    // 获取文件名（如果后端通过 header 返回）
    // const content = response.data;
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    // 尝试从 header 获取文件名
    // const contentDisposition = response.headers['content-disposition'];
    const now = new Date();
    const timestamp = now.getFullYear() +
        String(now.getMonth() + 1).padStart(2, '0') +
        String(now.getDate()).padStart(2, '0') + '_' +
        String(now.getHours()).padStart(2, '0') +
        String(now.getMinutes()).padStart(2, '0') +
        String(now.getSeconds()).padStart(2, '0');
    let fileName = `订单数据_${timestamp}.xlsx`;
    // if (contentDisposition) {
    //   const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
    //   if (fileNameMatch && fileNameMatch[1]) {
    //     fileName = fileNameMatch[1].replace(/['"]/g, '');
    //   }
    // }
    // fileName = fileName.replace("utf-8", "")

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.href = url;
    link.download = decodeURIComponent(fileName); // 解码文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url); // 释放内存
  }).finally(() => {
    exportLoading.value = false
  })
}

// 处理导出交易流水
const handleExportTrx = () => {
  trxExportVisible.value = true
}

// 新增：处理编辑订单
const handleEditOrder = (orderDetail) => {
  // 跳转到订单编辑页面或打开编辑对话框
  router.push(`/management/order-edit/${orderDetail.id}`)
}

// 新增：处理支付成功
const handlePaymentSuccess = () => {
  fetchData() // 刷新订单列表
  ElMessage.success('支付成功')
}

// 新增：处理生成二维码
const handleGenerateQR = async (data) => {
  // try {
  //   const response = await generateQRCodeApi(data.transaction.id)
  //   ElMessage.success('二维码生成成功')
  // } catch (error) {
  //   ElMessage.error('生成二维码失败')
  // }
}

// 新增：处理复制链接
const handleCopyLink = (link) => {
  ElMessage.success('支付链接已复制')
}

// 新增：处理发送微信
const handleSendWechat = (data) => {
  ElMessage.success('微信消息发送成功')
}

// 生命周期
onMounted(() => {
  fetchData()
  fetchStatistics()
})

const financialRefundVisible = ref(false)

// 判断是否可以财务退款
const canFinancialRefund = (row) => {
  return ['已付款', '已全额支付', '已部分支付'].includes(row.orderStatus)
}

// 处理财务退款
const handleFinancialRefund = (row) => {
  currentOrder.value = row
  financialRefundVisible.value = true
}

// 财务退款成功回调
const handleFinancialRefundSuccess = () => {
  fetchData()
}

// 处理订单核销申请
const handleWriteOff = (row) => {
  currentOrderForWriteOff.value = row
  writeoffDialogVisible.value = true
}
</script>

<style scoped>
.order-detail {
  margin: 20px 0;
}

.transactions-section {
  margin-top: 30px;
}

.transactions-section h3 {
  margin-bottom: 15px;
  color: #333;
}



/* 新增统一样式 */
.order-management-container {
  padding: 20px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  
  .stat-content {
    padding: 20px 0;
  }
  
  .stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #666;
  }
}

/* 订单详情对话框样式 */
.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.order-title {
  font-weight: 500;
  color: #409EFF;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: #409EFF;
  color: white;
  font-size: 12px;
}

.amount-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.amount-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.amount-value {
  font-size: 20px;
  font-weight: 600;
}

.amount-value.total {
  color: #409EFF;
}

.amount-value.paid {
  color: #67C23A;
}

.amount-value.unpaid {
  color: #F56C6C;
}

.progress-container {
  margin-top: 8px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  display: block;
}

.amount-text {
  font-weight: 500;
  color: #409EFF;
}

.timeline-content {
  padding: 8px 0;
}

.status-change {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.from-status {
  color: #909399;
  font-size: 14px;
}

.to-status {
  color: #409EFF;
  font-weight: 500;
  font-size: 14px;
}

.arrow-icon {
  color: #C0C4CC;
}

.status-remark {
  color: #606266;
  font-size: 13px;
  margin-bottom: 4px;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.operator-info {
  color: #909399;
  font-size: 12px;
}

.remark-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
  color: #606266;
  line-height: 1.6;
}

.search-card {
  margin-bottom: 20px;

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}

.table-card {
  .batch-operations {
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
