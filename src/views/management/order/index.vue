<template>
  <div class="order-create-container">
    <el-card class="order-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">产品下单</span>
        </div>
      </template>

      <!-- 步骤条 -->
      <div class="steps-container">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="选择产品"></el-step>
          <el-step title="选择学生"></el-step>
          <el-step title="确认订单"></el-step>
          <el-step title="支付"></el-step>
        </el-steps>
      </div>

      <!-- 步骤1: 选择产品 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="step-header">
          <h3 class="step-title">选择产品</h3>
        </div>

        <div v-if="presetProductId && selectedProduct" class="preset-info">
          <el-alert
            title="已预选产品"
            :description="`产品：${selectedProduct.name} - ¥${(selectedProduct.sellingPrice / 100).toFixed(2)}`"
            type="info"
            show-icon
            :closable="false"
          />
          <div class="preset-actions">
            <el-button type="primary" link @click="changeProduct"><el-icon><Switch /></el-icon>更换产品</el-button>
          </div>
        </div>

        <div v-else class="product-selection">
          <!-- 产品搜索表单 -->
          <div class="product-search">
            <el-form :model="productSearchForm" :inline="true" class="search-form">
              <el-form-item label="产品名称">
                <el-input
                  v-model="productSearchForm.name"
                  placeholder="请输入产品名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="学科">
                <el-select
                  v-model="productSearchForm.subject"
                  placeholder="请选择学科"
                  clearable
                  @change="handleSubjectChange"
                  style="width: 150px"
                >
                  <el-option label="英语" value="英语" />
                  <el-option label="语文" value="语文" />
                  <el-option label="数学" value="数学" />
                  <el-option label="物理" value="物理" />
                  <el-option label="化学" value="化学" />
                </el-select>
              </el-form-item>
              <el-form-item label="课型">
                <el-select
                  v-model="productSearchForm.courseType"
                  placeholder="请选择课型"
                  clearable
                  style="width: 150px"
                >
                  <el-option
                      v-for="option in availableSpecifications"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleProductSearch">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="resetProductSearch">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 产品显示选项 -->
          <div class="product-options" v-if="selectedProduct || selectedStudent">
            <el-form :inline="true" class="options-form">
              <el-form-item label="显示不可用产品">
                <el-switch
                  v-model="showInvalidProducts"
                  @change="handleShowInvalidChange"
                  active-text="显示"
                  inactive-text="隐藏"
                />
              </el-form-item>
            </el-form>
          </div>
          <el-row :gutter="20" v-if="filteredProducts.length > 0" class="product-grid">
            <el-col :xs="24" :sm="12" :md="8" :lg="6" style="margin-bottom: 10px;" v-for="product in filteredProducts" :key="product.id">
              <el-card
                :class="[
                  'product-card',
                  { 'selected': selectedProduct && selectedProduct.id === product.id },
                  { 'disabled': isProductDisabled(product) },
                  { 'invalid': product.validStatus === '无效' }
                ]"
                @click="!isProductDisabled(product) ? selectProduct(product) : null"
                shadow="hover"
              >
                <!-- 客户类型标识 - 右上角标签 -->
                <div class="customer-type-corner">
                  <el-tag
                    :type="product.customerType !== '通用' ? (product.customerType === '仅老客户可用' ? 'success' : 'warning') : 'primary'"
                    size="small"
                  >
                    {{ product.customerType }}
                  </el-tag>
                </div>

                <div class="product-info">
                  <h4 class="product-name">{{ product.name }}</h4>
                  <p class="product-desc">{{ product.description }}</p>
                  <div class="product-meta">
                    <el-tag size="small" type="success">{{ product.subject }}</el-tag>
                    <el-tag size="small" type="info" style="margin-left: 10px">{{ product.courseType }}</el-tag>
                  </div>
                  <div class="product-meta" v-if="product.applicableGrades" style=" line-height: 25px;">
                    <el-tag
                      size="small"
                      :type="selectedStudent && !isGradeApplicable(grade, selectedStudent.grade) ? 'danger' : 'info'"
                      v-for="grade in product.applicableGrades"
                      :key="grade"
                      style="margin-right: 10px;"
                    >
                      {{ getGradeText(grade) }}
                    </el-tag>
                  </div>
                  <div class="product-meta">
                    <span v-if="product.quantity" style="margin-right: 20px; font-weight: bold;" class="hours">正课：{{ product.quantity }}课时</span>
                    <span v-if="product.hasBonusHours" class="hours">赠送：{{ product.bonusHoursQuantity }}课时</span>
                  </div>
                  <div class="product-price">
                    <span class="price">¥{{ (product.sellingPrice / 100).toFixed(2) }}</span>
                    <span style="color:#999; font-size: 12px; text-decoration:line-through; margin-left: 10px">原价：¥{{ (product.originalPrice / 100).toFixed(2) }}</span>
                  </div>

                  <!-- 综合不适用提示 - 只在显示所有产品时显示 -->
                  <div v-if="showInvalidProducts && selectedStudent && getProductIncompatibleReasons(product, selectedStudent).length > 0" class="incompatible-notice">
                    <el-icon><WarningFilled /></el-icon>
                    <span>{{ getProductIncompatibleReasons(product, selectedStudent)?.join('，') }}</span>
                  </div>

                  <!-- 仅无效产品提示（当没有选择学生时） -->
                  <div v-else-if="!selectedStudent && product.validStatus === '无效'" class="incompatible-notice">
                    <el-icon><WarningFilled /></el-icon>
                    <span>产品已无效</span>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 空状态提示 -->
          <div v-else class="empty-state">
            <el-empty
              description="暂无可用产品"
              :image-size="100"
            >
              <template #description>
                <span v-if="!showInvalidProducts">
                  暂无可用产品，您可以尝试
                  <el-button type="text" @click="showInvalidProducts = true">显示不可用产品</el-button>
                </span>
                <span v-else>暂无产品数据</span>
              </template>
            </el-empty>
          </div>
        </div>

        <div class="step-actions">
          <el-button type="primary" :disabled="!selectedProduct" @click="nextStep">
            下一步
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 选择学生 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="step-header">
          <h3 class="step-title">选择学生</h3>
        </div>

        <div v-if="presetStudentId && selectedStudent" class="preset-info">
          <el-alert
            title="已预选学生" type="success"
            :description="`学生：${selectedStudent.name} (${selectedStudent.phone || ''}) - ${selectedStudent.grade ? getGradeText(selectedStudent.grade) : '未设置年级'}
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                家长：${selectedStudent.parentName || '家长姓名未设置'} (${selectedStudent.parentPhone || '家长手机号未设置'})`"
            show-icon
            :closable="false"
          />
          <div class="preset-actions">
            <el-button type="primary" link @click="changeStudent"><el-icon><Switch /></el-icon>更换学生</el-button>
          </div>
        </div>

        <div v-else class="student-selection">
          <el-form :model="orderForm" label-width="100px">
            <el-form-item label="选择学生">
              <el-select
                v-model="orderForm.studentId"
                placeholder="请选择学生"
                filterable
                remote
                :remote-method="searchStudents"
                :loading="studentLoading"
                style="width: 100%"
                @change="onStudentChange"
                clearable
              >
                <el-option
                  v-for="student in students"
                  :key="student.id"
                  :label="`${student.name} (${student.phone}) - ${student.grade ? getGradeText(student.grade) : '未设置年级'} - 家长：${student.parentName || '家长姓名未设置'} (${student.parentPhone || '家长手机号未设置'})`"
                  :value="student.id"
                  :disabled="(selectedProduct && !isProductApplicableForStudent(selectedProduct, student)) || !isParentInfoComplete(student) || !isCustomerTypeApplicable(selectedProduct, student)"
                >
                  <div class="student-option" :class="{ 'disabled': (selectedProduct && !isProductApplicableForStudent(selectedProduct, student)) || !isParentInfoComplete(student) || !isCustomerTypeApplicable(selectedProduct, student) }">
                    <div class="student-info">
                      <div class="student-main-info">
                        <span class="student-name">{{ student.name }}</span>
                        <span class="student-phone">({{ student.phone }})</span>
                        <span class="parent-name" style="margin-left: 10px">家长：{{ student.parentName || '家长姓名未设置' }}</span>
                        <span class="parent-phone">({{ student.parentPhone || '家长手机号未设置' }})</span>
                        <el-tag
                          size="small"
                          :type="((selectedProduct && !isProductApplicableForStudent(selectedProduct, student)) || !isParentInfoComplete(student) || !isCustomerTypeApplicable(selectedProduct, student)) ? 'danger' : 'success'"
                        >
                          {{ student.grade ? getGradeText(student.grade) : '未设置年级' }}
                        </el-tag>
                      </div>
                    </div>
                    <!-- 不适用提示 -->
                    <div v-if="selectedProduct && !isProductApplicableForStudent(selectedProduct, student)" class="incompatible-notice-inline">
                      <el-icon><WarningFilled /></el-icon>
                      <span>年级不适用</span>
                    </div>
                    <!-- 家长信息不完整提示 -->
                    <div v-else-if="!isParentInfoComplete(student)" class="incompatible-notice-inline">
                      <el-icon><WarningFilled /></el-icon>
                      <span>家长信息不完整</span>
                    </div>
                    <!-- 通用，仅新客/老客不适用提示 -->
                    <div v-else-if="!isCustomerTypeApplicable(selectedProduct, student)" class="incompatible-notice-inline">
                      <el-icon><WarningFilled /></el-icon>
                      <span>{{ getProductIncompatibleReasons(selectedProduct, student)?.join('，') }}</span>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" :disabled="!orderForm.studentId" @click="nextStep">
            下一步
          </el-button>
        </div>
      </div>

      <!-- 步骤3: 确认订单 -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>确认订单信息</h3>
        <el-form :model="orderForm" label-width="120px">
          <el-form-item label="产品信息">
            <div class="order-product-info">
              <div class="product-header">
                <h4>{{ selectedProduct.name }}</h4>
                <el-tag size="small" type="success">{{ selectedProduct.subject }}</el-tag>
                <el-tag size="small" type="info" style="margin-left: 8px">{{ selectedProduct.courseType }}</el-tag>
              </div>

              <div class="product-description">
                <p>{{ selectedProduct.description }}</p>
              </div>

              <div class="product-details">
                <div class="detail-row">
                  <span class="detail-label">单价：</span>
                  <span class="detail-value price">¥{{ (selectedProduct.unitPrice / 100).toFixed(2) }}/课时</span>
                </div>

                <div class="detail-row">
                  <span class="detail-label">课时数量：</span>
                  <span class="detail-value">{{ selectedProduct.quantity }}课时</span>
                  <span v-if="selectedProduct.hasBonusHours" class="bonus-info">
                    （赠送{{ selectedProduct.bonusHoursQuantity }}课时）
                  </span>
                </div>

                <div v-if="selectedProduct.hasMaterialFee" class="detail-row">
                  <span class="detail-label">教材费：</span>
                  <span class="detail-value">¥{{ (selectedProduct.materialFee / 100).toFixed(2) }}</span>
                </div>

                <div class="detail-row">
                  <span class="detail-label">原价：</span>
                  <span class="detail-value original-price">¥{{ (selectedProduct.originalPrice / 100).toFixed(2) }}</span>
                </div>

                <div v-if="selectedProduct.applicableGrades && selectedProduct.applicableGrades.length > 0" class="detail-row">
                  <span class="detail-label">适用年级：</span>
                  <div class="grade-tags">
                    <el-tag size="small" v-for="grade in selectedProduct.applicableGrades" :key="grade" style="margin-right: 4px">
                      {{ grade }}
                    </el-tag>
                  </div>
                </div>

                <div class="detail-row total-row">
                  <span class="detail-label">售价：</span>
                  <span class="detail-value selling-price">¥{{ (selectedProduct.sellingPrice / 100).toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="学生信息">
            <div class="order-student-info">
              <div class="detail-row">
                <span class="detail-label">学生姓名：</span>
                <span class="detail-value">{{ selectedStudent.name }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">联系电话：</span>
                <span class="detail-value">{{ selectedStudent.phone }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">年级：</span>
                <span class="detail-value">
                  <el-tag size="small" type="success">
                    {{ selectedStudent.grade ? getGradeText(selectedStudent.grade) : '未设置年级' }}
                  </el-tag>
                </span>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="支付方式">
            <el-radio-group v-model="orderForm.paymentType">
              <el-radio label="once">一次性付款</el-radio>
              <el-radio label="installment">分期付款</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="orderForm.paymentType === 'installment'" label="分期设置">
            <div class="installment-settings">
              <el-button @click="addInstallment" :disabled="orderForm.installments.length === 2" type="primary" size="small">添加分期</el-button>
              <div v-for="(installment, index) in orderForm.installments" :key="index" class="installment-item">
                <el-input-number
                  v-model="installment.amount"
                  :min="0.01"
                  :step="0.01"
                  :precision="2"
                  :max="selectedProduct.sellingPrice"
                  placeholder="金额"
                  size="small"
                ></el-input-number>
                <span>第{{ index + 1 }}期</span>
                <el-button @click="removeInstallment(index)" type="danger" size="small">删除</el-button>
              </div>
              <p class="installment-total">
                总计: ¥{{ installmentTotal }} / ¥{{ (selectedProduct.sellingPrice / 100).toFixed(2) }}
                <span :class="{ 'error': installmentTotal !== selectedProduct.sellingPrice }">
                  {{ installmentTotal * 100 === selectedProduct.sellingPrice ? '✓' : '✗' }}
                </span>
              </p>
            </div>
          </el-form-item>
        </el-form>
        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" :disabled="!isOrderValid" @click="nextStep" v-hasPermi="['order:pay']">确认订单</el-button>
        </div>
      </div>

      <!-- 步骤4: 支付 -->
      <div v-if="currentStep === 3" class="step-content">
<!--        <h3>支付</h3>-->
        <div v-if="orderCreated" class="payment-info">
          <p>订单创建成功！订单号: {{ orderNo }}</p>
          <div class="payment-methods">
            <el-button v-hasPermi="['order:pay']" type="primary" @click="generateQRCode"><el-icon><Postcard /></el-icon>&nbsp;生成支付二维码</el-button>
            <el-button v-hasPermi="['order:pay']" @click="copyPaymentLink"><el-icon><CopyDocument /></el-icon>&nbsp;复制支付链接</el-button>
            <el-button v-hasPermi="['order:pay']" @click="sendWechatMessage" v-loading="sendWechatMessageLoading"><el-icon><Link /></el-icon>&nbsp;发送微信模板消息</el-button>
          </div>
          <div v-if="qrCodeVisible && qrCodeData" class="qr-code-container">
            <h4>扫码支付</h4>
            <img :src="`data:image/png;base64,${qrCodeData.qrCodeBase64}`" alt="支付二维码" class="qr-code-image">
            <p class="qr-code-tip">请使用微信或支付宝扫描二维码完成支付</p>
          </div>
        </div>
        <div class="step-actions">
          <el-button @click="resetOrder" v-hasPermi="['order:pay']"><el-icon><DocumentAdd /></el-icon>&nbsp;重新下单</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {getAvailableProductsApi} from '@/api/management/product'
import {
  createOrderApi,
  generateQRCodeApi,
  getAlreadyPaidOrdersApi,
  getOrderTransactionsApi,
  getPaymentLinkApi,
  getSalesStudentDetailApi,
  getSalesStudentListApi,
  sendWechatPaymentOrderApi
} from '@/api/management/order'
import {
  CopyDocument,
  DocumentAdd,
  Link,
  Postcard,
  Refresh,
  Search,
  Switch,
  WarningFilled
} from "@element-plus/icons-vue"
import {getGradeText, GRADE_OPTIONS} from '@/utils/gradeUtils'

export default {
  name: 'OrderManagement',
  components: {Refresh, Switch, DocumentAdd, Postcard, CopyDocument, Link, WarningFilled},
  data() {
    return {
      currentStep: 0,
      products: [],
      allProducts: [], // 存储所有产品，用于搜索过滤
      students: [],
      selectedProduct: null,
      selectedStudent: null,
      studentLoading: false,
      orderForm: {
        studentId: '',
        productId: '',
        paymentType: 'once',
        installments: []
      },
      orderCreated: false,
      createdOrderId: '',
      orderNo: '',
      qrCodeVisible: false,
      qrCodeData: null,
      // 预设参数（从其他页面传入）
      presetStudentId: null,
      presetProductId: null,
      sendWechatMessageLoading: false,
      // 产品搜索表单
      productSearchForm: {
        name: '',
        subject: '',
        courseType: ''
      },
      // 是否显示无效产品
      showInvalidProducts: false,
      // 学生已购买的课型列表 (格式: ["学科_课型", ...])
      subjectCourseTypes: [],
      // 分页相关
      pagination: {
        pageNum: 1,
        pageSize: 12,
        total: 0
      },
      allSpecifications: [
        { label: "单词课", value: "单词课" },
        { label: "音标拼读课", value: "音标拼读课" },
        { label: "语法课", value: "语法课" },
        { label: "题型课", value: "题型课" },
        { label: "听说课", value: "听说课" },
        { label: "通用课（非英语）", value: "通用课（非英语）" }
      ],
      englishSpecifications: [
        { label: "单词课", value: "单词课" },
        { label: "音标拼读课", value: "音标拼读课" },
        { label: "语法课", value: "语法课" },
        { label: "题型课", value: "题型课" },
        { label: "听说课", value: "听说课" }
      ],
      otherSubjectSpecifications: [
        { label: "通用课（非英语）", value: "通用课（非英语）" }
      ],
      availableSpecifications: this.allSpecifications
    }
  },
  computed: {
    // 过滤后的产品列表
    filteredProducts() {
      if (!this.products || this.products.length === 0) {
        return []
      }

      if (this.showInvalidProducts) {
        // 显示所有产品（包括无效产品、不适用年级、家长信息不完善的产品）
        return this.products
      } else {
        // 只显示有效且适用的产品
        return this.products.filter(product => {
          // 过滤掉无效产品
          if (product.validStatus === '无效') {
            return false
          }

          // 如果选择了学生，还要检查产品是否适用于该学生
          if (this.selectedStudent) {
            // 检查年级是否适用
            if (!this.isProductApplicableForStudent(product, this.selectedStudent)) {
              return false
            }

            // 检查家长信息是否完整
            if (!this.isParentInfoComplete(this.selectedStudent)) {
              return false
            }

            // 检查客户类型是否适用
            if (!this.isCustomerTypeApplicable(product, this.selectedStudent)) {
              return false
            }
          }

          return true
        })
      }
    },

    // 检查产品是否不适用（用于disabled状态判断）
    isProductDisabled() {
      return (product) => {
        if (!this.showInvalidProducts) {
          return false // 隐藏模式下，不适用的产品已经被过滤掉了
        }

        // 显示模式下，检查产品是否不适用
        const reasons = this.getProductIncompatibleReasons(product, this.selectedStudent)
        return reasons.length > 0
      }
    },

    installmentTotal() {
      return this.orderForm.installments.reduce((total, item) => total + (item.amount || 0), 0)
    },
    isOrderValid() {
      if (this.orderForm.paymentType === 'once') {
        return true
      } else {
        return this.installmentTotal * 100 === this.selectedProduct.sellingPrice && this.orderForm.installments.length > 0
      }
    }
  },
  async mounted() {
    this.initializeFromRoute()

    // 如果有预设学生ID，先加载学生信息
    if (this.presetStudentId) {
      await this.loadPresetStudent()
    }

    // 然后加载产品列表
    await this.loadProducts()
  },
  methods: {
    /** 初始化路由参数 */
    initializeFromRoute() {
      // 从路由参数获取预设的学生ID和产品ID
      const { studentId, productId } = this.$route.query

      if (studentId) {
        this.presetStudentId = studentId
        this.orderForm.studentId = studentId
        console.log('预设学生ID:', studentId)
      }

      if (productId) {
        this.presetProductId = productId
        this.orderForm.productId = productId
        console.log('预设产品ID:', productId)
      }

      // 根据预设参数调整初始步骤
      this.determineInitialStep()
    },

    /** 确定初始步骤 */
    determineInitialStep() {
      if (this.presetProductId && this.presetStudentId) {
        // 如果产品和学生都预设了，直接跳到确认订单步骤
        this.currentStep = 2
      } else if (this.presetProductId) {
        // 如果只预设了产品，跳到选择学生步骤
        this.currentStep = 1
      } else if (this.presetStudentId) {
        // 如果只预设了学生，从选择产品开始
        this.currentStep = 0
      } else {
        // 都没有预设，从第一步开始
        this.currentStep = 0
      }
    },

    async loadProducts() {
      const params = {
        showInvalid: true, // 始终获取所有产品，包括无效产品
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }
      if (this.selectedStudent) {
        params.studentId = this.selectedStudent.id
      }

      const response = await getAvailableProductsApi()
      this.products = response.data

      // 如果有预设的产品ID，自动选择该产品
      if (this.presetProductId) {
        this.selectedProduct = this.products.find(p => p.id === this.presetProductId)
        if (this.selectedProduct) {
          console.log('自动选择产品:', this.selectedProduct.name)
        } else {
          this.$message.warning('指定的产品不存在或已下架')
          // 重置到第一步
          this.currentStep = 0
          this.presetProductId = null
          this.orderForm.productId = ''
        }
      }
    },

    // 产品搜索处理
    async handleProductSearch() {
      try {
        // 搜索时重置到第一页
        this.pagination.pageNum = 1

        const params = {
          showInvalid: true, // 始终获取所有产品，包括无效产品
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        }
        if (this.productSearchForm.name) {
          params.name = this.productSearchForm.name
        }
        if (this.productSearchForm.subject) {
          params.subject = this.productSearchForm.subject
        }
        if (this.productSearchForm.courseType) {
          params.courseType = this.productSearchForm.courseType
        }
        if (this.selectedStudent) {
          params.studentId = this.selectedStudent.id
        }

        const response = await getAvailableProductsApi(params)

        // 如果返回的是分页数据
        if (response.data && typeof response.data === 'object' && response.data.records) {
          this.products = response.data.records
          this.pagination.total = response.data.total
        } else {
          // 兼容原有的非分页数据格式
          this.products = response.data || []
          this.pagination.total = this.products.length
        }
      } catch (error) {
        this.$message.error('搜索产品失败')
      }
    },

    // 重置产品搜索
    resetProductSearch() {
      this.productSearchForm = {
        name: '',
        subject: '',
        courseType: ''
      }
      this.loadProducts()
    },

    // 处理显示无效产品开关变化
    handleShowInvalidChange() {
      // 可以添加一些提示信息
      if (this.showInvalidProducts) {
        this.$message.info('已显示所有产品（包括不可用产品）')
      } else {
        this.$message.info('已隐藏不可用产品')
      }
    },
    async searchStudents(query) {
      if (query !== '') {
        this.studentLoading = true
        try {
          const response = await getSalesStudentListApi({ name: query, pageSize: 20 })
          this.students = response.data.records

          // 如果有预设的学生ID，自动选择该学生
          if (this.presetStudentId && this.students.length > 0) {
            this.selectedStudent = this.students.find(s => s.id === this.presetStudentId)
            if (this.selectedStudent) {
              console.log('自动选择学生:', this.selectedStudent.name)
            }
          }
        } finally {
          this.studentLoading = false
        }
      } else if (this.presetStudentId) {
        // 如果有预设学生ID但没有搜索词，直接加载该学生
        await this.loadPresetStudent()
      }
    },

    /** 加载预设学生信息 */
    async loadPresetStudent() {
      if (!this.presetStudentId) return

      this.studentLoading = true
      try {
        const response = await getSalesStudentDetailApi(this.presetStudentId)
        this.selectedStudent = response.data

        if (this.selectedStudent) {
          this.students = [this.selectedStudent]
          console.log('加载预设学生:', this.selectedStudent.name)
          // 加载学生已购买的课型
          await this.loadStudentPaidCourseTypes(this.selectedStudent.id)
        } else {
          this.$message.warning('指定的学生不存在')
          this.presetStudentId = null
          this.orderForm.studentId = ''
        }
      } finally {
        this.studentLoading = false
      }
    },
    selectProduct(product) {
      // 如果选择了学生，检查所有不适用原因
      if (this.selectedStudent) {
        const reasons = this.getProductIncompatibleReasons(product, this.selectedStudent)
        if (reasons.length > 0) {
          this.$message.warning(`无法选择该产品：${reasons.join('，')}`)
          return
        }
      }

      this.selectedProduct = product
      this.orderForm.productId = product.id
    },

    /** 更换产品 */
    changeProduct() {
      this.presetProductId = null
      this.selectedProduct = null
      this.orderForm.productId = ''
    },

    /** 更换学生 */
    changeStudent() {
      this.presetStudentId = null
      this.selectedStudent = null
      this.orderForm.studentId = ''
      this.students = []
    },
    nextStep() {
      if (this.currentStep === 1) {
        // 获取选中的学生信息（如果还没有选中的话）
        if (!this.selectedStudent) {
          this.selectedStudent = this.students.find(s => s.id === this.orderForm.studentId)
        }
        // 进入产品选择步骤时，重新加载产品列表以进行动态有效性判断
        this.loadProducts()
      } else if (this.currentStep === 2) {
        // 创建订单
        this.createOrder()
        return
      }
      this.currentStep++
    },
    prevStep() {
      this.currentStep--
    },
    async createOrder() {
      const orderData = {
        studentId: this.orderForm.studentId,
        productId: this.orderForm.productId
      }

      if (this.orderForm.paymentType === 'installment') {
        orderData.multiTrxAmts = this.orderForm.installments.map((item, index) => ({
          idx: index + 1,
          amt: item.amount * 100
        }))
      }

      const response = await createOrderApi(orderData)
      this.createdOrderId = response.data.orderId
      this.orderNo = response.data.orderNo
      this.orderCreated = true
      this.currentStep++
      this.$message.success('订单创建成功')
    },
    addInstallment() {
      this.orderForm.installments.push({ amount: 0 })
    },
    removeInstallment(index) {
      this.orderForm.installments.splice(index, 1)
    },
    async generateQRCode() {
      try {
        // 先获取订单的交易流水
        const transactionsResponse = await getOrderTransactionsApi(this.createdOrderId)
        const transaction = transactionsResponse.data
        if (transaction) {
          const qrCodeResponse = await generateQRCodeApi(transaction.id)
          this.qrCodeData = qrCodeResponse.data
          this.qrCodeVisible = true
          this.$message.success('支付二维码生成成功')
        } else {
          this.$message.error('未找到交易流水')
        }
      } catch (error) {
        this.$message.error('生成支付二维码失败: ' + error.message)
      }
    },
    async copyPaymentLink() {
      // 先获取订单的交易流水
      const transactionsResponse = await getOrderTransactionsApi(this.createdOrderId)
      const transaction = transactionsResponse.data
      if (transaction) {
        const linkResponse = await getPaymentLinkApi(transaction.id)
        const paymentLink = linkResponse.data

        // 复制到剪贴板
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(paymentLink)
          this.$message.success('支付链接已复制到剪贴板')
        } else {
          // 兼容旧浏览器
          const textArea = document.createElement('textarea')
          textArea.value = paymentLink
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          this.$message.success('支付链接已复制到剪贴板')
        }
      } else {
        this.$message.error('未找到交易流水')
      }
    },
    async sendWechatMessage() {
      // 发送微信模板消息的逻辑
      this.sendWechatMessageLoading = true
      try {
        await sendWechatPaymentOrderApi(this.createdOrderId)
        this.$message.success('微信模板消息发送成功')
      } finally {
        this.sendWechatMessageLoading = false
      }
    },
    resetOrder() {
      this.currentStep = 0
      this.selectedProduct = null
      this.selectedStudent = null
      this.orderForm = {
        studentId: '',
        productId: '',
        paymentType: 'once',
        installments: []
      }
      this.orderCreated = false
      this.createdOrderId = ''
      this.qrCodeVisible = false
      this.qrCodeData = null
    },

    /** 学生选择变化事件 */
    async onStudentChange(studentId) {
      if (studentId) {
        this.selectedStudent = this.students.find(s => s.id === studentId)
        // 加载学生已购买的课型
        await this.loadStudentPaidCourseTypes(studentId)
      } else {
        this.selectedStudent = null
        this.subjectCourseTypes = []
      }
      // 学生变化时重新加载产品列表，以便进行动态有效性判断
      await this.loadProducts()
    },

    /** 检查产品是否适用于学生 */
    isProductApplicableForStudent(product, student) {
      if (!product || !student || !product.applicableGrades || !student.grade) {
        return false
      }

      return product.applicableGrades.includes(getGradeText(student.grade))
    },

    /** 检查家长信息是否完整 */
    isParentInfoComplete(student) {
      if (!student) {
        return false
      }
      // 检查家长姓名和家长手机号是否都不为空
      return !!(student.parentName && student.parentName.trim() && student.parentPhone && student.parentPhone.trim())
    },
    //
    // /** 检查产品是否适用于学生 */
    // isCustomerTypeApplicable2(product, student) {
    //   if (!product || !student || !product.subject || !product.courseType || !student.subjectCourseTypes) {
    //     return false
    //   }
    //   this.subjectCourseTypes = student.subjectCourseTypes
    //   if(product.customerType === '通用') {
    //     return true;
    //   }
    //   if(product.customerType === '仅新客可用') {
    //     return !student.subjectCourseTypes.includes(product.subject + "_" + product.courseType);
    //   }
    //   if(product.customerType === '仅老客可用') {
    //     return student.subjectCourseTypes.includes(product.subject + "_" + product.courseType);
    //   } else {
    //     return true;
    //   }
    // },

    /** 获取学生已购买的课型 */
    async loadStudentPaidCourseTypes(studentId) {
      try {
        const response = await getAlreadyPaidOrdersApi(studentId)
        this.subjectCourseTypes = response.data || []
        console.log('学生已购买课型:', this.subjectCourseTypes)
      } catch (error) {
        console.error('获取学生已购买课型失败:', error)
        this.subjectCourseTypes = []
      }
    },

    /** 检查产品客户类型是否适用于学生 */
    isCustomerTypeApplicable(product, student) {
      if (!product || !product.customerType) {
        return true // 如果没有客户类型限制，默认适用
      }

      // 通用产品，所有人都可以购买
      if (product.customerType === '通用') {
        return true
      }

      if (!student || !this.subjectCourseTypes) {
        return false
      }

      const currentCourseType = `${product.subject}_${product.courseType}`
      const hasPurchased = this.subjectCourseTypes.includes(currentCourseType)

      // 仅新客可用：学生没有购买过该学科和课型
      if (product.customerType === '仅新客可用') {
        return !hasPurchased
      }

      // 仅老客可用：学生之前购买过该学科和课型
      if (product.customerType === '仅老客可用') {
        return hasPurchased
      }

      return true
    },

    /** 获取客户类型不适用的原因 */
    getCustomerTypeIncompatibleReason(product, student) {
      if (!product || !product.customerType || !student) {
        return ''
      }

      const currentCourseType = `${product.subject}_${product.courseType}`
      const hasPurchased = student.subjectCourseTypes.includes(currentCourseType)

      if (product.customerType === '仅新客可用' && hasPurchased) {
        return '仅新客可用，您已购买过该课型'
      }

      if (product.customerType === '仅老客可用' && !hasPurchased) {
        return '仅老客可用，您未购买过该课型'
      }

      return ''
    },

    /** 获取产品的所有不适用原因 */
    getProductIncompatibleReasons(product, student) {
      if (!product || !student) {
        return []
      }

      const reasons = []
      // 检查年级适用性
      if (!this.isProductApplicableForStudent(product, student)) {
        reasons.push(`不适用于${this.getGradeText(student.grade)}`)
      }

      // 检查家长信息完整性
      if (!this.isParentInfoComplete(student)) {
        reasons.push('家长信息不完整')
      }

      // 检查客户类型适用性
      if (!this.isCustomerTypeApplicable(product, student)) {
        const customerTypeReason = this.getCustomerTypeIncompatibleReason(product, student)
        if (customerTypeReason) {
          reasons.push(customerTypeReason)
        }
      }

      return reasons
    },

    /** 检查年级是否适用（用于产品年级标签显示） */
    isGradeApplicable(gradeToCheck, studentGrade) {
      if (!gradeToCheck || !studentGrade) return false

      return String(gradeToCheck) === String(studentGrade)
    },

    /** 获取年级显示文本 */
    getGradeText(grade) {
      if (!grade) return ''

      const gradeNum = parseInt(grade)
      const option = GRADE_OPTIONS.find(item => item.value === gradeNum)
      return option ? option.label : grade
    },
    handleSubjectChange(subject) {
      this.productSearchForm.courseType = ''
      if (!subject) {
        // 没有选择学科，显示所有课型
        this.availableSpecifications = this.allSpecifications
      } else if (subject === '英语') {
        // 英语学科，显示英语相关课型
        this.availableSpecifications = this.englishSpecifications
      } else {
        // 其他学科，只显示通用课
        this.availableSpecifications = this.otherSubjectSpecifications
      }
    }
  }
}
</script>

<style scoped>
.step-content {
  margin: 30px 0;
  min-height: 400px;
}

/* 产品网格布局 */
.product-grid {
  min-height: 200px; /* 确保有最小高度，避免布局跳动 */
}

.product-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  height: 100%; /* 确保卡片高度一致 */
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-card.selected {
  border-color: #409EFF;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.product-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.product-info {
  padding: 10px;
}

.product-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.product-desc {
  color: #666;
  font-size: 12px;
  margin: 0 0 8px 0;
}

.product-meta {
  margin-bottom: 8px;
}

.product-type, .product-subject {
  display: inline-block;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  margin-right: 5px;
}

.product-price {
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
}

.price {
  color: #e74c3c;
  font-weight: bold;
  font-size: 16px;
}

.hours {
  color: #666;
  font-size: 12px;
}

.step-actions {
  text-align: center;
  margin-top: 30px;
}

.order-product-info, .order-student-info {
  width: 100%;
  background: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
}

.installment-settings {
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 5px;
}

.installment-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.installment-total {
  margin-top: 10px;
  font-weight: bold;
}

.installment-total .error {
  color: #e74c3c;
}

.payment-info {
  text-align: center;
}

.payment-methods {
  margin: 20px 0;
}

.qr-code-container {
  margin-top: 20px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.qr-code-tip {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.preset-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
}

/* 新增样式优化 */
.order-create-container {
  padding: 20px;
}

.order-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.steps-container {
  margin: 30px 0;
}

.step-header {
  margin-bottom: 30px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
  display: inline-block;
}

.step-actions {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.step-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}

.preset-actions {
  margin-top: 15px;
}

.product-selection {
  margin-bottom: 30px;
}

.student-selection {
  margin-bottom: 30px;
}

.student-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.student-name {
  font-weight: 500;
  color: #303133;
}

.student-phone {
  color: #8492a6;
  font-size: 13px;
}

.product-name {
  border-left: 3px solid #409EFF;
  padding-left: 10px;
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}
.order-product-info {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.product-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.product-header h4 {
  margin: 0;
  margin-right: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.product-description {
  margin-bottom: 16px;
}

.product-description p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.detail-value.price {
  color: #409eff;
  font-weight: 600;
}

.detail-value.original-price {
  color: #909399;
  text-decoration: line-through;
}

.detail-value.selling-price {
  color: #e6a23c;
  font-weight: 600;
  font-size: 16px;
}

.total-row {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e4e7ed;
}

.bonus-info {
  color: #67c23a;
  font-size: 12px;
  margin-left: 8px;
}

.grade-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 产品卡片禁用状态 */
.product-card.disabled {
  cursor: not-allowed;
  background-color: #f5f5f5;
  pointer-events: none; /* 完全禁用点击事件 */
  position: relative;
}

.product-card.disabled:hover {
  box-shadow: none;
  transform: none; /* 禁用hover动画 */
}

.product-card.disabled .product-info {
  color: #999;
}

/* 为disabled产品添加遮罩效果 */
.product-card.disabled::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 5;
  border-radius: 4px;
}

.product-card.invalid {
  border: 1px solid #f56c6c;
  background: #fef0f0;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 产品显示选项样式 */
.product-options {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.product-options .options-form {
  margin: 0;
}

.product-options .el-form-item {
  margin-bottom: 0;
}

/* 产品有效性状态样式 */
.product-validity {
  margin-top: 8px;
}

.invalid-reason {
  margin-top: 4px;
  color: #f56c6c;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 客户类型标识样式 - 右上角标签 */
.customer-type-corner {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 15; /* 确保在disabled遮罩之上 */
}

.customer-type-corner .el-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 0 4px 0 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 不适用提示样式 */
.incompatible-notice {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.incompatible-notice-inline {
  color: #f56c6c;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: 8px;
}

/* 学生选项禁用状态 */
.student-option.disabled {
  opacity: 0.6;
  color: #999;
}

.student-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.student-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

.student-main-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.student-parent-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.parent-name {
  color: #606266;
  font-size: 12px;
}

.parent-phone {
  color: #8492a6;
  font-size: 12px;
}

.student-grade-info {
  margin-top: 2px;
}

.student-main-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.student-grade-info {
  display: flex;
  align-items: center;
}

.student-grade {
  color: #8492a6;
  font-size: 12px;
  margin-top: 2px;
}
</style>
