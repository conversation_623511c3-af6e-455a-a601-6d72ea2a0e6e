<template>
  <div class="teacher-match-container" :class="{ 'dialog-mode': isDialogMode }">
    <!-- 页面标题 -->
    <div v-if="!isDialogMode" class="page-header">
      <h2>学生信息匹配老师</h2>
      <p>根据学生的可上课时间段，智能匹配合适的老师</p>
    </div>

    <div class="match-layout">
      <!-- 左侧：搜索条件 -->
      <div class="search-panel">
        <el-card class="search-card">
          <template #header>
            <div class="search-header">
              <span>匹配条件</span>
              <!-- 弹窗模式下显示操作模式选择 - 销售角色不显示切换选项 -->
              <el-radio-group
                v-if="shouldShowModeSelector"
                v-model="operationMode"
                size="small"
                class="operation-mode-selector"
              >
                <el-radio-button label="direct">直接分配</el-radio-button>
                <el-radio-button label="apply">申请预约</el-radio-button>
              </el-radio-group>
            </div>
          </template>

          <el-form
            ref="searchFormRef"
            :model="searchForm"
            :rules="searchRules"
            label-width="80px"
            class="search-form"
          >
            <!-- 弹窗模式下显示锁定的学生信息 -->
            <el-form-item v-if="isDialogMode" label="学生信息">
              <div class="locked-student-info">
                <div class="student-name">{{ studentInfo.name }}</div>
                <div class="student-details">
                  <span>{{ studentInfo.phone }}</span>
                  <span v-if="studentInfo.grade">{{ getGradeText(studentInfo.grade) }}</span>
                  <span v-if="studentInfo.school">{{ studentInfo.school }}</span>
                </div>
              </div>
            </el-form-item>

            <!-- 非弹窗模式下显示学生选择器 -->
            <el-form-item v-else label="选择学生" prop="studentId">
              <el-select
                v-model="searchForm.studentId"
                placeholder="请选择学生"
                filterable
                remote
                :remote-method="searchStudents"
                :loading="studentLoading"
                @change="handleStudentChange"
                style="width: 100%"
                size="small"
              >
                <el-option
                  v-for="student in studentOptions"
                  :key="student.id"
                  :label="`${student.name} (${student.phone}) ${student.grade ? getGradeText(student.grade) : ''}`"
                  :value="student.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="searchForm.startDate"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                size="small"
              />
            </el-form-item>

            <el-form-item label="教学组">
              <el-select
                v-model="searchForm.groupIds"
                placeholder="全部教学组"
                multiple
                clearable
                style="width: 100%"
                size="small"
              >
                <el-option
                  v-for="group in teachingGroups"
                  :key="group.id"
                  :label="group.name"
                  :value="group.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="关键词">
              <el-input
                v-model="searchForm.keyword"
                placeholder="教师姓名或手机号"
                clearable
                size="small"
              />
            </el-form-item>

            <!-- 基础信息筛选 - 一行两个 -->
            <div class="form-row">
              <el-form-item label="性别" class="form-item-half">
                <el-select
                  v-model="searchForm.gender"
                  placeholder="全部性别"
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option label="男" value="0" />
                  <el-option label="女" value="1" />
                  <el-option label="未知" value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="工作性质" class="form-item-half">
                <el-select
                  v-model="searchForm.employmentType"
                  placeholder="全部类型"
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option label="全职" value="全职" />
                  <el-option label="意向全职" value="意向全职" />
                  <el-option label="兼职" value="兼职" />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="年龄范围" class="form-item-full">
                <div style="display: flex; gap: 32px; align-items: center;">
                  <el-input-number
                    v-model="searchForm.minAge"
                    placeholder="最小年龄"
                    :min="18"
                    :max="70"
                    size="small"
                    style="width: 120px"
                  />
                  <span>-</span>
                  <el-input-number
                    v-model="searchForm.maxAge"
                    placeholder="最大年龄"
                    :min="18"
                    :max="70"
                    size="small"
                    style="width: 120px"
                  />
                </div>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="目前状态" class="form-item-half">
                <el-select
                  v-model="searchForm.currentStatus"
                  placeholder="全部状态"
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option label="在职" value="在职" />
                  <el-option label="离职" value="离职" />
                  <el-option label="休假" value="休假" />
                </el-select>
              </el-form-item>

              <el-form-item label="师范类" class="form-item-half">
                <el-select
                  v-model="searchForm.isNormalUniversity"
                  placeholder="全部"
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
            </div>

            <!-- 教育背景筛选 - 一行两个 -->
            <div class="form-row">
              <el-form-item label="学历" class="form-item-half">
                <el-select
                  v-model="searchForm.education"
                  placeholder="全部学历"
                  multiple
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option label="高中" value="高中" />
                  <el-option label="大专" value="大专" />
                  <el-option label="本科" value="本科" />
                  <el-option label="硕士" value="硕士" />
                  <el-option label="博士" value="博士" />
                </el-select>
              </el-form-item>

              <el-form-item label="大学属性" class="form-item-half">
                <el-select
                  v-model="searchForm.universityType"
                  placeholder="全部类型"
                  multiple
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option label="双一流" value="双一流" />
                  <el-option label="985" value="985" />
                  <el-option label="211" value="211" />
                  <el-option label="一本" value="一本" />
                  <el-option label="普通" value="普通" />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="留学经历" class="form-item-half">
                <el-select
                  v-model="searchForm.studyAbroad"
                  placeholder="全部"
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option label="有" :value="true" />
                  <el-option label="无" :value="false" />
                </el-select>
              </el-form-item>

              <el-form-item label="暑期时间" class="form-item-half">
                <el-select
                  v-model="searchForm.summerScheduleType"
                  placeholder="全部档期"
                  clearable
                  style="width: 100%"
                  size="small"
                >
                  <el-option label="全满档" value="full" />
                  <el-option label="黄金档" value="golden" />
                  <el-option label="其他档" value="other" />
                </el-select>
              </el-form-item>
            </div>

            <div class="form-row">
                <el-form-item label="课点更新" class="form-item-half">
                <el-input-number
                  v-model="searchForm.timeSlotUpdateDays"
                  placeholder="默认3天"
                  :min="1"
                  :max="365"
                  style="width: 100%"
                  size="small"
                  controls-position="right"
                />
                <div class="form-item-tip">老师可排课时间最后更新距今天数</div>
              </el-form-item>
            </div>

            <!-- 高级筛选条件 -->
            <el-collapse v-model="activeCollapse" style="margin-bottom: 16px;">
              <el-collapse-item title="教学资质筛选" name="qualification">
                <el-form-item label="教资级别" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.teachingCertificateLevel"
                    placeholder="全部级别"
                    multiple
                    clearable
                    style="width: 100%"
                    size="small"
                  >
                    <el-option label="幼儿园" value="幼儿园" />
                    <el-option label="小学" value="小学" />
                    <el-option label="初中" value="初中" />
                    <el-option label="高中" value="高中" />
                  </el-select>
                </el-form-item>

                <el-form-item label="教授学科" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.subjects"
                    placeholder="全部学科"
                    multiple
                    clearable
                    style="width: 100%"
                    size="small"
                  >
                    <el-option label="英语" value="英语" />
                    <el-option label="语文" value="语文" />
                    <el-option label="数学" value="数学" />
                    <el-option label="物理" value="物理" />
                    <el-option label="化学" value="化学" />
                  </el-select>
                </el-form-item>

                <el-form-item label="已通过培训科目" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.trainingSubjects"
                    placeholder="全部培训科目"
                    multiple
                    clearable
                    style="width: 100%"
                    size="small"
                  >
                    <el-option label="音标" value="音标" />
                    <el-option label="语法" value="语法" />
                    <el-option label="阅读" value="阅读" />
                    <el-option label="听说" value="听说" />
                    <el-option label="写作" value="写作" />
                    <el-option label="完型" value="完型" />
                  </el-select>
                </el-form-item>

                <el-form-item label="英语资质" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.englishQualification"
                    placeholder="全部资质"
                    multiple
                    clearable
                    style="width: 100%"
                    size="small"
                  >
                    <el-option label="四级" value="四级" />
                    <el-option label="六级" value="六级" />
                    <el-option label="专四" value="专四" />
                    <el-option label="专八" value="专八" />
                    <el-option label="雅思" value="雅思" />
                    <el-option label="托福" value="托福" />
                  </el-select>
                </el-form-item>

                <el-form-item label="普通话资质" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.mandarinQualification"
                    placeholder="全部等级"
                    multiple
                    clearable
                    style="width: 100%"
                    size="small"
                  >
                    <el-option label="一级甲等" value="一级甲等" />
                    <el-option label="一级乙等" value="一级乙等" />
                    <el-option label="二级甲等" value="二级甲等" />
                    <el-option label="二级乙等" value="二级乙等" />
                  </el-select>
                </el-form-item>

                <el-form-item label="教龄范围" style="margin-bottom: 16px;">
                  <div style="display: flex; gap: 8px; align-items: center;">
                    <el-input-number
                      v-model="searchForm.minTeachingYears"
                      placeholder="最少"
                      :min="0"
                      :max="50"
                      size="small"
                      style="width: 80px"
                    />
                    <span>-</span>
                    <el-input-number
                      v-model="searchForm.maxTeachingYears"
                      placeholder="最多"
                      :min="0"
                      :max="50"
                      size="small"
                      style="width: 80px"
                    />
                    <span>年</span>
                  </div>
                </el-form-item>
              </el-collapse-item>

              <el-collapse-item title="教学风格筛选" name="style">
                <el-form-item label="上课风格" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.teachingStyle"
                    placeholder="全部风格"
                    multiple
                    clearable
                    style="width: 100%"
                    size="small"
                  >
                    <el-option label="严格" value="严格" />
                    <el-option label="温和" value="温和" />
                    <el-option label="幽默" value="幽默" />
                    <el-option label="活泼" value="活泼" />
                    <el-option label="耐心" value="耐心" />
                  </el-select>
                </el-form-item>

                <el-form-item label="适合年级" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.suitableGrades"
                    placeholder="全部年级"
                    multiple
                    clearable
                    style="width: 100%"
                    size="small"
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="grade in gradeOptions"
                      :key="grade.value"
                      :label="grade.label"
                      :value="grade.value"
                    />
                  </el-select>
                  <!-- 快速选择按钮 -->
                  <div class="grade-quick-select" style="margin-top: 4px;">
                    <el-button size="small" @click="selectPrimaryGrades">小学</el-button>
                    <el-button size="small" @click="selectMiddleGrades">初中</el-button>
                    <el-button size="small" @click="selectHighGrades">高中</el-button>
                    <el-button size="small" @click="clearGradeSelection">清空</el-button>
                  </div>
                </el-form-item>

                <el-form-item label="适合程度" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.suitableLevels"
                    placeholder="全部程度"
                    multiple
                    clearable
                    style="width: 100%"
                    size="small"
                  >
                    <el-option label="基础" value="基础" />
                    <el-option label="中等" value="中等" />
                    <el-option label="优秀" value="优秀" />
                  </el-select>
                </el-form-item>

                <el-form-item label="适合性格" style="margin-bottom: 16px;">
                  <el-select
                    v-model="searchForm.suitablePersonality"
                    placeholder="全部性格"
                    clearable
                    style="width: 100%"
                    size="small"
                  >
                    <el-option label="内向" value="内向" />
                    <el-option label="外向" value="外向" />
                    <el-option label="活泼" value="活泼" />
                    <el-option label="安静" value="安静" />
                  </el-select>
                </el-form-item>
              </el-collapse-item>
            </el-collapse>

            <el-form-item  prop="timeSlots" label-width="0">
              <div class="time-slots-container">
                <div class="time-slots-header">
                  <span>上课时间段</span>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Plus"
                    @click="addTimeSlot"
                    :disabled="!searchForm.studentId"
                  >
                    添加
                  </el-button>
                </div>

            <div v-if="searchForm.timeSlots.length > 0" class="time-slots-list">
              <div
                v-for="(slot, index) in searchForm.timeSlots"
                :key="index"
                class="time-slot-item"
              >
                <div class="schedule-row">
                  <div class="time-controls">
                    <el-select
                      v-model="slot.weekday"
                      placeholder="星期"
                      size="small"
                      @change="onDayOfWeekChange(slot)"
                      class="weekday-select"
                    >
                      <el-option
                        v-for="day in weekDays"
                        :key="day.value"
                        :label="day.label"
                        :value="day.value"
                      />
                    </el-select>

                    <el-select
                      v-model="slot.duration"
                      placeholder="时长"
                      @change="onDurationChange(slot)"
                      size="small"
                      class="duration-select"
                    >
                      <el-option
                        v-for="duration in durationOptions"
                        :key="duration.value"
                        :label="duration.label"
                        :value="duration.value"
                      />
                    </el-select>

                    <div class="time-range-input">
                      <el-select
                        v-model="slot.startTime"
                        placeholder="开始时间"
                        filterable
                        size="small"
                        @change="updateEndTime(slot)"
                        class="start-time-select"
                      >
                        <el-option
                          v-for="time in getAvailableStartTimeOptions(slot.duration)"
                          :key="time.value"
                          :label="time.label"
                          :value="time.value"
                        />
                      </el-select>
                      <span class="time-separator">～</span>
                      <span class="end-time">{{ slot.endTime || '--:--' }}</span>
                    </div>
                  </div>

                  <div class="slot-actions">
                    <el-button
                      type="danger"
                      size="small"
                      :icon="Delete"
                      @click="removeTimeSlot(index)"
                      circle
                      v-if="searchForm.timeSlots.length > 1"
                      class="delete-btn"
                    />
                  </div>
                </div>

                <div class="time-summary">
                  <el-tag size="small" class="time-tag">
                    {{ getTimeSlotDisplay(slot) }}
                  </el-tag>
                </div>
              </div>
            </div>

            <div v-else class="empty-time-slots">
              <el-empty description="暂无时间段设置" :image-size="60">
                <el-button
                  type="primary"
                  @click="addTimeSlot"
                  :disabled="!searchForm.studentId"
                >
                  <el-icon><Plus /></el-icon>
                  添加第一个时间段
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="handleSearch"
                :loading="searching"
                :disabled="!canSearch"
                size="small"
                style="width: 100%; margin-bottom: 8px;"
              >
                <el-icon><Search /></el-icon>
                开始匹配
              </el-button>
              <el-button
                @click="handleReset"
                size="small"
                style="width: 100%;"
              >
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>

          </el-form>
        </el-card>

        <!-- 申请表单卡片 - 仅在申请模式下显示 -->
        <el-card v-if="isDialogMode && operationMode === 'apply'" class="application-form-card">
          <template #header>
            <span>申请信息</span>
          </template>

          <el-form
            ref="applicationFormRef"
            :model="applicationForm"
            :rules="applicationRules"
            label-width="80px"
            class="application-form"
          >
            <el-form-item label="申请说明" prop="applyReason">
              <el-input
                v-model="applicationForm.applyReason"
                type="textarea"
                :rows="3"
                placeholder="请简要说明申请原因"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="已选老师">
              <div class="selected-teachers">
                <el-tag
                  v-for="teacher in selectedTeachers"
                  :key="teacher.teacherId"
                  closable
                  @close="removeSelectedTeacher(teacher)"
                  class="teacher-tag"
                >
                  {{ teacher.teacherName }}
                </el-tag>
                <span v-if="selectedTeachers.length === 0" class="no-selection">
                  请在右侧老师列表中选择候选老师（最少1个，最多3个）
                </span>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="submitApplication"
                :disabled="!canSubmitApplication"
                :loading="submittingApplication"
                style="width: 100%;"
              >
                <el-icon><Check /></el-icon>
                提交申请
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 右侧：匹配结果 -->
      <div class="result-panel">
        <el-card v-if="matchResult" class="result-card">
          <template #header>
            <div class="result-header">
              <span>匹配结果</span>
              <el-tag type="info">找到 {{ matchResult.totalCount }} 位老师</el-tag>
            </div>
          </template>

          <!-- 学生信息 -->
          <div class="student-info">
            <h4>学生信息</h4>
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="姓名">{{ matchResult.studentInfo.studentName }}</el-descriptions-item>
              <el-descriptions-item label="手机号">{{ matchResult.studentInfo.studentPhone }}</el-descriptions-item>
              <el-descriptions-item label="年级">{{ getGradeText(matchResult.studentInfo.grade) }}</el-descriptions-item>
              <el-descriptions-item label="学校">{{ matchResult.studentInfo.school }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 教师列表 -->
          <div class="teachers-list">
            <h4>匹配的教师</h4>
            <div class="teachers-grid">
              <div
                v-for="teacher in matchResult.teachers"
                :key="teacher.teacherId"
                class="teacher-card"
              >
                <div class="teacher-header">
                  <div class="teacher-basic">
                    <h5>{{ teacher.teacherName }}</h5>
                    <span class="teacher-phone">{{ teacher.teacherPhone }}</span>
                  </div>
                </div>

                <div class="teacher-info">
                  <div class="info-row">
                    <div class="info-item">
                      <span class="label">教学组:</span>
                      <span>{{ teacher.groupName || '未分组' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">经验:</span>
                      <span>{{ teacher.teachingYears }}年</span>
                    </div>
                  </div>

                  <div class="info-row">
                    <div class="info-item">
                      <span class="label">学生:</span>
                      <span>{{ teacher.currentStudents }}人</span>
                    </div>

                  <div class="info-row" v-if="teacher.employmentType || teacher.age">
                    <div class="info-item" v-if="teacher.employmentType">
                      <span class="label">性质:</span>
                      <el-tag size="small" :type="getEmploymentTypeTagType(teacher.employmentType)">
                        {{ getEmploymentTypeText(teacher.employmentType) }}
                      </el-tag>
                    </div>
                    <div class="info-item" v-if="teacher.age">
                      <span class="label">年龄:</span>
                      <span>{{ teacher.age }}岁</span>
                    </div>
                  </div>

                  <div class="info-item">
                    <span class="label">学科:</span>
                    <el-tag
                      v-for="subject in teacher.subjects"
                      :key="subject"
                      size="small"
                      class="subject-tag"
                    >
                      {{ subject }}
                    </el-tag>
                  </div>

                  <!-- 已通过培训科目 -->
                  <div class="info-item" v-if="teacher.trainingSubjects && teacher.trainingSubjects.length > 0">
                    <span class="label">培训:</span>
                    <el-tag
                      v-for="subject in teacher.trainingSubjects"
                      :key="subject"
                      size="small"
                      type="success"
                      class="training-tag"
                    >
                      {{ subject }}
                    </el-tag>
                  </div>

                  <!-- 教过课程 -->
                  <div class="info-item" v-if="teacher.taughtCourses && teacher.taughtCourses.length > 0">
                    <span class="label">课程:</span>
                    <el-tag
                      v-for="course in teacher.taughtCourses.slice(0, 3)"
                      :key="course"
                      size="small"
                      type="info"
                      class="course-tag"
                    >
                      {{ course }}
                    </el-tag>
                    <span v-if="teacher.taughtCourses.length > 3" class="more-tags">
                      +{{ teacher.taughtCourses.length - 3 }}
                    </span>
                  </div>
                </div>

                <div class="teacher-actions">
                  <el-button
                    size="small"
                    @click="viewTeacherDetail(teacher)"
                  >
                    <el-icon><User /></el-icon>
                    查看详情
                  </el-button>
                  <el-button
                    size="small"
                    @click="viewTeacherSchedule(teacher)"
                  >
                    <el-icon><Calendar /></el-icon>
                    查看时间表
                  </el-button>

                  <!-- 直接分配模式 -->
                  <el-button
                    v-if="!isDialogMode || operationMode === 'direct'"
                    type="primary"
                    size="small"
                    @click="selectTeacher(teacher)"
                  >
                    <el-icon><Check /></el-icon>
                    选择此老师
                  </el-button>

                  <!-- 申请模式 -->
                  <el-checkbox
                    v-if="isDialogMode && operationMode === 'apply'"
                    v-model="teacher.selected"
                    :disabled="!canSelectTeacher(teacher)"
                    @change="handleTeacherSelection(teacher, $event)"
                    class="teacher-checkbox"
                  >
                    选择候选
                  </el-checkbox>
                </div>
              </div>
            </div>
            </div>

            <el-empty v-if="matchResult.teachers.length === 0" description="没有找到匹配的教师" />
          </div>
        </el-card>

        <!-- 空状态 -->
        <div v-else class="empty-result">
          <el-empty description="请设置匹配条件并开始匹配" />
        </div>
      </div>
    </div>

    <!-- 教师时间表查看对话框 -->
    <TeacherScheduleDialog
      v-model="showScheduleDialog"
      :teacher="selectedTeacher"
      :start-date="searchForm.startDate"
    />

    <!-- 教师详情查看对话框 -->
    <TeacherDetailDialog
      v-model="showDetailDialog"
      :teacher="selectedTeacherForDetail"
    />

    <!-- 排课对话框 -->
    <ScheduleDialog
      v-if="showCourseScheduleDialog && selectedTeacherForSchedule"
      v-model="showCourseScheduleDialog"
      :teacher-id="selectedTeacherForSchedule.teacherId"
      :teacher-name="selectedTeacherForSchedule.teacherName"
      :student-id="searchForm.studentId"
      :student-name="selectedStudentName"
      :start-date="searchForm.startDate"
      :time-slots="convertedTimeSlots"
      @success="handleScheduleSuccess"
    />
  </div>
</template>

<script setup name="StudentMatchTeacher">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Search, Refresh, Calendar, User, Check } from '@element-plus/icons-vue'
import { matchTeachersApi } from '@/api/management/teacherMatch'
import { getStudentsApi, assignTeacherToStudentApi } from '@/api/management/student'
import { getTeachingGroupsApi } from '@/api/management/teachingGroup'
import { submitCourseBookingApplicationApi } from '@/api/management/courseBookingApplication'
import { GRADE_OPTIONS, getGradeText, getGradesByStage, getDefaultSuitableGrades } from '@/utils/gradeUtils'
import TeacherScheduleDialog from './components/TeacherScheduleDialog.vue'
import TeacherDetailDialog from '@/views/management/teacher/components/TeacherDetailDialog.vue'
import ScheduleDialog from '@/views/curriculum/components/ScheduleDialog.vue'
import { useRoute } from 'vue-router'
import useUserStore from '@/store/modules/user'

// Props
const props = defineProps({
  studentInfo: {
    type: Object,
    default: null
  },
  isDialogMode: {
    type: Boolean,
    default: false
  },
  // 新增：操作模式，用于区分进入路径
  // 'direct' - 直接分配模式（教学管理进入）
  // 'apply' - 申请预约模式（销售管理进入）
  // 'auto' - 自动模式（根据用户角色决定，兼容旧版本）
  mode: {
    type: String,
    default: 'auto',
    validator: (value) => ['direct', 'apply', 'auto'].includes(value)
  }
})

// Emits
const emit = defineEmits(['success'])

// 路由实例
const route = useRoute()

// 用户store
const userStore = useUserStore()

// 响应式数据
const searchFormRef = ref()
const applicationFormRef = ref()
const searching = ref(false)
const studentLoading = ref(false)
const showScheduleDialog = ref(false)
const showDetailDialog = ref(false)
const showCourseScheduleDialog = ref(false)
const activeCollapse = ref([])

// 申请模式相关
const operationMode = ref('direct') // 'direct' | 'apply'
const selectedTeachers = ref([]) // 申请模式下选中的候选老师
const submittingApplication = ref(false)

// 搜索表单
const searchForm = reactive({
  studentId: '',
  startDate: '',
  timeSlots: [],
  keyword: '',
  groupIds: [],

  // ========== 基础信息筛选 ==========
  gender: '',
  minAge: null,
  maxAge: null,
  employmentType: '',
  currentStatus: '',

  // ========== 教育背景筛选 ==========
  education: [],
  universityType: [],
  isNormalUniversity: null,
  studyAbroad: null,

  // ========== 教学资质筛选 ==========
  teachingCertificateLevel: [],
  subjects: [],
  trainingSubjects: [],
  englishQualification: [],
  mandarinQualification: [],
  communicationAbility: [],
  englishPronunciation: [],
  minTeachingYears: null,
  maxTeachingYears: null,

  // ========== 教学经历和风格筛选 ==========
  taughtCourses: [],
  teachingStyle: [],
  suitableGrades: [],
  suitableLevels: [],
  suitablePersonality: '',

  // ========== 暑期课上课时间筛选 ==========
  summerScheduleType: '',

  // ========== 课点更新天数筛选 ==========
  timeSlotUpdateDays: 3
})

// 表单验证规则
const searchRules = computed(() => {
  const rules = {
    startDate: [
      { required: true, message: '请选择开始日期', trigger: 'change' }
    ],
    timeSlots: [
      {
        validator: (_rule, value, callback) => {
          if (!value || value.length === 0) {
            callback(new Error('请设置学生可上课时间段'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  }

  // 非弹窗模式下需要验证学生选择
  if (!props.isDialogMode) {
    rules.studentId = [
      { required: true, message: '请选择学生', trigger: 'change' }
    ]
  }

  return rules
})

// 申请表单
const applicationForm = reactive({
  applyReason: ''
})

// 申请表单验证规则
const applicationRules = computed(() => ({
  applyReason: [
    { required: true, message: '请填写申请说明', trigger: 'blur' },
    { min: 5, max: 200, message: '申请说明长度在5到200个字符', trigger: 'blur' }
  ]
}))

// 选项数据
const studentOptions = ref([])
const teachingGroups = ref([])
const matchResult = ref(null)
const selectedTeacher = ref(null)
const selectedTeacherForDetail = ref(null)
const selectedTeacherForSchedule = ref(null)

// 星期选项
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 时长选项
const durationOptions = [
  { label: '60分钟', value: 60 },
  { label: '90分钟', value: 90 },
  { label: '120分钟', value: 120 },
]

// 年级选项
const gradeOptions = GRADE_OPTIONS

// 生成时间选项（从06:00到24:00，每5分钟一个档）
const generateTimeOptions = () => {
  const options = [];
  for (let hour = 6; hour <= 24; hour++) {
    const maxMinute = hour === 24 ? 0 : 60; // 24点只显示00分
    for (let minute = 0; minute < maxMinute; minute += 5) {
      const timeStr = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;
      options.push({
        label: timeStr,
        value: timeStr,
      });
    }
  }
  return options;
};

const timeOptions = generateTimeOptions();

// 计算属性
const canSearch = computed(() => {
  if (props.isDialogMode) {
    // 弹窗模式下不需要验证学生ID，因为已经锁定
    return searchForm.startDate && searchForm.timeSlots.length > 0
  } else {
    // 非弹窗模式下需要验证学生ID
    return searchForm.studentId && searchForm.startDate && searchForm.timeSlots.length > 0
  }
})

// 申请模式相关计算属性
const canSubmitApplication = computed(() => {
  return selectedTeachers.value.length > 0 &&
         selectedTeachers.value.length <= 3 &&
         applicationForm.applyReason.trim().length >= 5
})

// 判断用户是否为销售角色（保留用于auto模式）
const isSalesRole = computed(() => {
  const roles = userStore.roles || []
  return roles.some(role =>
    role === '销售' ||
    role === '销售组长' ||
    role === '销售总监'
  )
})

// 根据mode参数决定是否显示模式切换选项
const shouldShowModeSelector = computed(() => {
  // 如果不是弹窗模式，不显示模式切换
  if (!props.isDialogMode) {
    return false
  }

  // 如果明确指定了模式，不显示切换选项
  if (props.mode === 'direct' || props.mode === 'apply') {
    return false
  }

  // auto模式下，根据用户角色决定（兼容旧版本）
  if (props.mode === 'auto') {
    return !isSalesRole.value
  }

  return false
})

// 移除教学组验证限制，现在允许选择不同教学组的老师

// 数据重置函数
const resetAllData = () => {
  // 重置搜索表单
  searchForm.studentId = ''
  searchForm.startDate = new Date().toISOString().split('T')[0]
  searchForm.timeSlots = []
  searchForm.keyword = ''
  searchForm.groupIds = []

  // 重置基础信息筛选
  searchForm.gender = ''
  searchForm.minAge = null
  searchForm.maxAge = null
  searchForm.employmentType = ''
  searchForm.currentStatus = ''

  // 重置教育背景筛选
  searchForm.education = []
  searchForm.universityType = []
  searchForm.isNormalUniversity = null
  searchForm.studyAbroad = null

  // 重置教学资质筛选
  searchForm.teachingCertificateLevel = []
  searchForm.subjects = []
  searchForm.trainingSubjects = []
  searchForm.englishQualification = []
  searchForm.mandarinQualification = []
  searchForm.communicationAbility = []
  searchForm.englishPronunciation = []
  searchForm.minTeachingYears = null
  searchForm.maxTeachingYears = null

  // 重置教学经历和风格筛选
  searchForm.taughtCourses = []
  searchForm.teachingStyle = []
  searchForm.suitableGrades = []
  searchForm.suitableLevels = []
  searchForm.suitablePersonality = ''

  // 重置暑期课上课时间筛选
  searchForm.summerScheduleType = ''

  // 重置课点更新天数筛选
  searchForm.timeSlotUpdateDays = 3

  // 重置其他状态
  studentOptions.value = []
  matchResult.value = null
  selectedTeacher.value = null
  selectedTeacherForDetail.value = null
  selectedTeacherForSchedule.value = null
  activeCollapse.value = []
  showScheduleDialog.value = false
  showDetailDialog.value = false
  showCourseScheduleDialog.value = false
  currentSelectedStudent.value = null
}

// 监听器
watch(() => props.studentInfo, (newStudentInfo, oldStudentInfo) => {
  // 弹窗模式下，当学生信息变化时重置数据
  if (props.isDialogMode && newStudentInfo) {
    // 检查是否是不同的学生
    if (!oldStudentInfo || newStudentInfo.id !== oldStudentInfo.id) {
      resetAllData()
      initializeFormData()
    } else {
      // 同一个学生但信息可能更新了，更新年级设置
      if (newStudentInfo.grade) {
        searchForm.suitableGrades = getDefaultSuitableGrades(newStudentInfo.grade)
      }
    }
  }
}, { deep: true })

// 监听操作模式变化
watch(() => operationMode.value, (newMode) => {
  if (newMode === 'apply') {
    // 切换到申请模式时重置选择
    selectedTeachers.value = []
    applicationForm.applyReason = ''

    // 重置所有老师的选择状态
    if (matchResult.value && matchResult.value.teachers) {
      matchResult.value.teachers.forEach(teacher => {
        teacher.selected = false
      })
    }
  }
})

watch(() => route.query, (newQuery, oldQuery) => {
  // 页面模式下，当URL参数变化时重置数据
  if (!props.isDialogMode) {
    const newStudentId = newQuery.studentId
    const oldStudentId = oldQuery?.studentId

    // 检查学生ID是否变化
    if (newStudentId !== oldStudentId) {
      resetAllData()
      initializeFormData()
    }
  }
}, { deep: true })

// 初始化表单数据
const initializeFormData = () => {
  loadTeachingGroups()
  // 设置默认开始日期为今天
  searchForm.startDate = new Date().toISOString().split('T')[0]

  // 根据mode参数设置默认操作模式
  if (props.isDialogMode) {
    if (props.mode === 'direct') {
      // 明确指定直接分配模式
      operationMode.value = 'direct'
    } else if (props.mode === 'apply') {
      // 明确指定申请预约模式
      operationMode.value = 'apply'
    } else if (props.mode === 'auto') {
      // auto模式下根据用户角色决定（兼容旧版本）
      if (isSalesRole.value) {
        operationMode.value = 'apply'
      } else {
        operationMode.value = 'direct'
      }
    }
  }

  if (props.isDialogMode && props.studentInfo) {
    // 弹窗模式下使用传入的学生信息
    searchForm.studentId = props.studentInfo.id
    currentSelectedStudent.value = props.studentInfo
    studentOptions.value = [props.studentInfo]

    // 根据学生年级设置默认的适合年级筛选条件
    if (props.studentInfo.grade) {
      searchForm.suitableGrades = getDefaultSuitableGrades(props.studentInfo.grade)
    }
  } else {
    // 非弹窗模式下从URL参数中获取学生信息
    const urlParams = new URLSearchParams(window.location.search)
    const studentId = urlParams.get('studentId')
    const studentName = urlParams.get('studentName')

    if (studentId && studentName) {
      // 设置学生选项和选中值
      const student = {
        id: studentId,
        name: studentName,
        phone: '' // 这里可以后续通过API获取完整信息
      };
      studentOptions.value = [student]
      searchForm.studentId = studentId
      currentSelectedStudent.value = student
    }
  }
}

// 年级快速选择方法
const selectPrimaryGrades = () => {
  searchForm.suitableGrades = getGradesByStage('小学')
}

const selectMiddleGrades = () => {
  searchForm.suitableGrades = getGradesByStage('初中')
}

const selectHighGrades = () => {
  searchForm.suitableGrades = getGradesByStage('高中')
}

const clearGradeSelection = () => {
  searchForm.suitableGrades = []
}

// 生命周期
onMounted(() => {
  initializeFormData()
})

// 方法
async function loadTeachingGroups() {
  try {
    const { data } = await getTeachingGroupsApi({
      pageSize: 100 // 获取所有教学组
    })
    teachingGroups.value = data?.records || []
  } catch (error) {
    console.error('加载教学组失败:', error)
  }
}

async function searchStudents(query) {
  if (!query) {
    studentOptions.value = []
    return
  }

  studentLoading.value = true
  try {
    const { data } = await getStudentsApi({
      keyword: query,
      pageSize: 20
    })
    studentOptions.value = data?.records || []

    // 如果当前有选中的学生ID，尝试从新的列表中找到并更新 currentSelectedStudent
    if (searchForm.studentId && studentOptions.value.length > 0) {
      const student = studentOptions.value.find(s => String(s.id) === String(searchForm.studentId));
      if (student) {
        currentSelectedStudent.value = student;
      }
    }
  } catch (error) {
    console.error('搜索学生失败:', error)
    ElMessage.error('搜索学生失败')
  } finally {
    studentLoading.value = false
  }
}

function handleStudentChange() {
  // 更新当前选中的学生信息
  if (searchForm.studentId && studentOptions.value.length > 0) {
    const student = studentOptions.value.find(s => String(s.id) === String(searchForm.studentId));
    if (student) {
      currentSelectedStudent.value = student;

      // 根据学生年级设置默认的适合年级筛选条件
      if (student.grade) {
        searchForm.suitableGrades = getDefaultSuitableGrades(student.grade);
      }
    }
  }

  // 学生变更时重置时间段为默认值
  const defaultSlot = {
    weekday: 1, // 周一
    startTime: '09:00',
    endTime: '10:00',
    duration: 60, // 默认60分钟
  }
  updateEndTime(defaultSlot)
  searchForm.timeSlots = [defaultSlot]
  matchResult.value = null
}

function getTimeSlotDisplay(slot) {
  if (!slot.weekday || !slot.startTime || !slot.endTime) return ''

  const weekDayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
  return `${weekDayNames[slot.weekday]} ${slot.startTime}-${slot.endTime}`
}



function addTimeSlot() {
  if (!searchForm.studentId) {
    ElMessage.warning('请先选择学生')
    return
  }

  const newSlot = {
    weekday: 1, // 默认周一
    startTime: '09:00',
    endTime: '10:00',
    duration: 60, // 默认60分钟
  }
  updateEndTime(newSlot)
  searchForm.timeSlots.push(newSlot)
}

function removeTimeSlot(index) {
  searchForm.timeSlots.splice(index, 1)
}

// 处理星期几变更
function onDayOfWeekChange(slot) {
  // 星期几变更时，保持当前的开始时间和时长，重新计算结束时间
  updateEndTime(slot)
}

// 处理时长变更
function onDurationChange(slot) {
  // 时长变更时，重新计算结束时间
  updateEndTime(slot)
}

// 获取指定时长的可用开始时间选项
function getAvailableStartTimeOptions(duration) {
  if (!duration) {
    return timeOptions
  }

  // 过滤时间选项，确保开始时间+时长后的结束时间不会跨越午夜
  const availableOptions = []

  timeOptions.forEach(timeOption => {
    const startTime = timeOption.value

    // 计算结束时间
    const [hours, minutes] = startTime.split(':').map(Number)
    const startDate = new Date()
    startDate.setHours(hours, minutes, 0, 0)
    const endDate = new Date(startDate.getTime() + duration * 60 * 1000)

    // 检查是否跨越了午夜
    if (endDate.getDate() === startDate.getDate()) {
      availableOptions.push(timeOption)
    }
  })

  return availableOptions
}

// 自动计算结束时间
function updateEndTime(slot) {
  if (!slot.startTime || !slot.duration) {
    slot.endTime = ""
    return
  }

  const [hours, minutes] = slot.startTime.split(":").map(Number)
  const startDate = new Date()
  startDate.setHours(hours, minutes, 0, 0)

  const endDate = new Date(startDate.getTime() + slot.duration * 60 * 1000)

  // 检查是否跨越了午夜
  if (endDate.getDate() !== startDate.getDate()) {
    // 如果跨越了午夜，清空结束时间并提示
    slot.endTime = ""
    ElMessage.warning('课程时长过长，结束时间不能跨越午夜，请调整时长或开始时间')
    return
  }

  const endHours = endDate.getHours().toString().padStart(2, "0")
  const endMinutes = endDate.getMinutes().toString().padStart(2, "0")

  slot.endTime = `${endHours}:${endMinutes}`
}

async function handleSearch() {
  try {
    await searchFormRef.value.validate()

    searching.value = true

    // 如果是申请预约模式，清空已选择的老师
    if (operationMode.value === 'apply') {
      selectedTeachers.value = []
      applicationForm.applyReason = ''
    }

    // 构建请求参数，只传递有值的筛选条件
    const requestParams = {
      studentId: searchForm.studentId,
      startDate: searchForm.startDate,
      timeSlots: searchForm.timeSlots,
      keyword: searchForm.keyword,
      groupIds: searchForm.groupIds
    }

    // 添加基础信息筛选
    if (searchForm.gender) requestParams.gender = searchForm.gender
    if (searchForm.minAge !== null) requestParams.minAge = searchForm.minAge
    if (searchForm.maxAge !== null) requestParams.maxAge = searchForm.maxAge
    if (searchForm.employmentType) requestParams.employmentType = searchForm.employmentType
    if (searchForm.currentStatus) requestParams.currentStatus = searchForm.currentStatus

    // 添加教育背景筛选
    if (searchForm.education.length > 0) requestParams.education = searchForm.education
    if (searchForm.universityType.length > 0) requestParams.universityType = searchForm.universityType
    if (searchForm.isNormalUniversity !== null) requestParams.isNormalUniversity = searchForm.isNormalUniversity
    if (searchForm.studyAbroad !== null) requestParams.studyAbroad = searchForm.studyAbroad

    // 添加教学资质筛选
    if (searchForm.teachingCertificateLevel.length > 0) requestParams.teachingCertificateLevel = searchForm.teachingCertificateLevel
    if (searchForm.subjects.length > 0) requestParams.subjects = searchForm.subjects
    if (searchForm.trainingSubjects.length > 0) requestParams.trainingSubjects = searchForm.trainingSubjects
    if (searchForm.englishQualification.length > 0) requestParams.englishQualification = searchForm.englishQualification
    if (searchForm.mandarinQualification.length > 0) requestParams.mandarinQualification = searchForm.mandarinQualification
    if (searchForm.communicationAbility.length > 0) requestParams.communicationAbility = searchForm.communicationAbility
    if (searchForm.englishPronunciation.length > 0) requestParams.englishPronunciation = searchForm.englishPronunciation
    if (searchForm.minTeachingYears !== null) requestParams.minTeachingYears = searchForm.minTeachingYears
    if (searchForm.maxTeachingYears !== null) requestParams.maxTeachingYears = searchForm.maxTeachingYears

    // 添加教学经历和风格筛选
    if (searchForm.taughtCourses.length > 0) requestParams.taughtCourses = searchForm.taughtCourses
    if (searchForm.teachingStyle.length > 0) requestParams.teachingStyle = searchForm.teachingStyle
    if (searchForm.suitableGrades.length > 0) requestParams.suitableGrades = searchForm.suitableGrades
    if (searchForm.suitableLevels.length > 0) requestParams.suitableLevels = searchForm.suitableLevels
    if (searchForm.suitablePersonality) requestParams.suitablePersonality = searchForm.suitablePersonality

    // 添加暑期课上课时间筛选
    if (searchForm.summerScheduleType) requestParams.summerScheduleType = searchForm.summerScheduleType

    // 添加课点更新天数筛选
    if (searchForm.timeSlotUpdateDays !== null && searchForm.timeSlotUpdateDays !== undefined) {
      requestParams.timeSlotUpdateDays = searchForm.timeSlotUpdateDays
    }

    const { data } = await matchTeachersApi(requestParams)

    // 为每个老师添加选择状态
    if (data && data.teachers) {
      data.teachers.forEach(teacher => {
        teacher.selected = false
      })
    }

    matchResult.value = data

    if (data.totalCount === 0) {
      ElMessage.info('没有找到匹配的教师，请调整搜索条件')
    } else {
      ElMessage.success(`找到 ${data.totalCount} 位匹配的教师`)
    }
  } catch (error) {
    console.error('匹配教师失败:', error)
    ElMessage.error('匹配教师失败')
  } finally {
    searching.value = false
  }
}

function handleReset() {
  // 使用统一的重置函数
  resetAllData()
  // 重新初始化表单数据
  initializeFormData()

  ElMessage.success('筛选条件已重置')
}





function viewTeacherDetail(teacher) {
  selectedTeacherForDetail.value = {
    id: teacher.teacherId,
    name: teacher.teacherName,
    phone: teacher.teacherPhone,
    ...teacher
  };
  showDetailDialog.value = true;
}

function viewTeacherSchedule(teacher) {
  selectedTeacher.value = teacher
  showScheduleDialog.value = true
}

async function selectTeacher(teacher) {
  try {
    await ElMessageBox.confirm(
      `确定选择教师 ${teacher.teacherName} 吗？`,
      '确认选择',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    // 保存选中的教师信息
    selectedTeacherForSchedule.value = teacher

    // 先建立师生关系
    // ElMessage.info('正在建立师生关系...');
    try {
      await assignTeacherToStudentApi(searchForm.studentId, teacher.teacherId);
    //   ElMessage.success('师生关系建立成功');

      // 师生关系建立成功后才打开排课对话框
      showCourseScheduleDialog.value = true
    //   ElMessage.success(`已选择教师：${teacher.teacherName}，正在打开排课界面`)
    } catch (error) {
      console.error('建立师生关系失败:', error);
      ElMessage.error('建立师生关系失败，无法进行排课');
      // 师生关系建立失败时不打开排课对话框
      selectedTeacherForSchedule.value = null
      return;
    }
  } catch (error) {
    // 用户取消或其他错误
  }
}

// ========== 申请模式相关方法 ==========

// 检查是否可以选择老师
function canSelectTeacher(teacher) {
  // 如果当前老师已经被选中，允许取消选择
  const isCurrentlySelected = selectedTeachers.value.some(t => t.teacherId === teacher.teacherId)
  if (isCurrentlySelected) {
    return true
  }

  // 如果已经选择了3个老师且当前老师未被选中，不能再选择
  if (selectedTeachers.value.length >= 3) {
    return false
  }

  // 移除教学组限制，现在可以选择不同教学组的老师
  return true
}

// 处理老师选择
function handleTeacherSelection(teacher, selected) {
  if (selected) {
    // 选择老师
    if (!canSelectTeacher(teacher)) {
      ElMessage.warning('最多只能选择3个老师')
      teacher.selected = false
      return
    }

    selectedTeachers.value.push({
      teacherId: teacher.teacherId,
      teacherName: teacher.teacherName,
      teachingGroupId: teacher.teachingGroupId,
      teachingGroupName: teacher.teachingGroupName
    })
  } else {
    // 取消选择老师
    const index = selectedTeachers.value.findIndex(t => t.teacherId === teacher.teacherId)
    if (index > -1) {
      selectedTeachers.value.splice(index, 1)
    }
  }
}

// 移除已选择的老师
function removeSelectedTeacher(teacher) {
  const index = selectedTeachers.value.findIndex(t => t.teacherId === teacher.teacherId)
  if (index > -1) {
    selectedTeachers.value.splice(index, 1)

    // 同时更新匹配结果中的选择状态
    if (matchResult.value && matchResult.value.teachers) {
      const matchedTeacher = matchResult.value.teachers.find(t => t.teacherId === teacher.teacherId)
      if (matchedTeacher) {
        matchedTeacher.selected = false
      }
    }
  }
}

// 提交申请
async function submitApplication() {
  try {
    // 验证表单
    await applicationFormRef.value.validate()

    if (selectedTeachers.value.length === 0) {
      ElMessage.warning('请至少选择一个候选老师')
      return
    }

    if (selectedTeachers.value.length > 3) {
      ElMessage.warning('最多只能选择3个候选老师')
      return
    }

    submittingApplication.value = true

    // 构建申请数据 - 修复参数匹配问题
    const applicationData = {
      studentId: props.isDialogMode ? props.studentInfo?.id : searchForm.studentId,
      subject: '英语',
      specification: '单词课',
      // 移除后端不需要的字段: nature, grade
      preferredTeachers: selectedTeachers.value.map(t => t.teacherId), // 修复字段名
      preferredTimeSlots: searchForm.timeSlots.map(slot => ({
        weekday: slot.weekday,           // 修复字段名
        startTime: slot.startTime,       // 修复格式为HH:mm字符串
        endTime: slot.endTime,           // 修复格式为HH:mm字符串
        priority: 1                      // 添加优先级字段
      })),
      applicationReason: applicationForm.applyReason  // 修复字段名
    }

    // 调用申请API
    await submitCourseBookingApplicationApi(applicationData)

    ElMessage.success('申请提交成功！')

    // 触发成功事件
    emit('success')

  } catch (error) {
    console.error('提交申请失败:', error)
    ElMessage.error('提交申请失败：' + (error.message || '未知错误'))
  } finally {
    submittingApplication.value = false
  }
}

// 处理排课成功
function handleScheduleSuccess() {
  showCourseScheduleDialog.value = false
  selectedTeacherForSchedule.value = null
  ElMessage.success('排课成功！')

  // 弹窗模式下触发success事件
  if (props.isDialogMode) {
    emit('success')
  }
}

// 转换时间段格式，从匹配页面格式转换为排课组件格式
const convertedTimeSlots = computed(() => {
  if (!searchForm.timeSlots || searchForm.timeSlots.length === 0) {
    return []
  }

  return searchForm.timeSlots.map(slot => ({
    dayOfWeek: slot.weekday, // 直接使用weekday，因为都是1-7格式
    startTime: slot.startTime,
    endTime: slot.endTime,
    duration: slot.duration || 60
  }))
})

// 存储当前选中的学生信息，避免被清空
const currentSelectedStudent = ref(null)

// 获取选中学生的姓名
const selectedStudentName = computed(() => {
  if (!searchForm.studentId) {
    return ''
  }

  // 首先尝试从 currentSelectedStudent 获取
  if (currentSelectedStudent.value && String(currentSelectedStudent.value.id) === String(searchForm.studentId)) {
    return currentSelectedStudent.value.name;
  }

  // 然后尝试从 studentOptions 获取
  if (studentOptions.value && studentOptions.value.length > 0) {
    const student = studentOptions.value.find(s => {
      return String(s.id) === String(searchForm.studentId);
    });

    if (student) {
      // 同时更新 currentSelectedStudent
      currentSelectedStudent.value = student;
      return student.name;
    }
  }

  return '';
})

// 获取工作性质标签类型
const getEmploymentTypeTagType = (type) => {
  const typeMap = {
    'full_time': 'success',
    'intended_full_time': 'primary',
    'part_time': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取工作性质文本
const getEmploymentTypeText = (type) => {
  const textMap = {
    'full_time': '全职',
    'intended_full_time': '意向全职',
    'part_time': '兼职'
  }
  return textMap[type] || type
}
</script>

<style lang="scss" scoped>
.teacher-match-container {
  padding: 20px;

  &.dialog-mode {
    padding: 0;
    height: 100%;
    overflow: hidden;

    .match-layout {
      height: 100%;
    }

    .search-panel {
      height: 100%;
      overflow-y: auto;
    }

    .result-panel {
      height: 100%;
      overflow-y: auto;
    }
  }

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .match-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;
  }

  .search-panel {
    width: 480px;
    flex-shrink: 0;

    // 表单行布局
    .form-row {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;

      .form-item-half {
        flex: 1;
        margin-bottom: 0;
      }

      .form-item-full {
        flex: 1;
        margin-bottom: 0;
      }
    }

    .search-card {

      .locked-student-info {
        padding: 12px;
        background-color: #f0f9ff;
        border: 1px solid #bfdbfe;
        border-radius: 6px;

        .student-name {
          font-size: 16px;
          font-weight: 600;
          color: #1e40af;
          margin-bottom: 4px;
        }

        .student-details {
          display: flex;
          gap: 12px;
          font-size: 14px;
          color: #64748b;

          span {
            &:not(:last-child)::after {
              content: '|';
              margin-left: 12px;
              color: #cbd5e1;
            }
          }
        }
      }

      .time-slots-container {
        .time-slots-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding: 8px 12px;
          background-color: #f5f7fa;
          border-radius: 4px;

          span {
            font-weight: 500;
            color: #303133;
            font-size: 14px;
          }
        }

        .time-slots-list {
          .time-slot-item {
            margin-bottom: 10px;
            padding: 12px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            background-color: #fafbfc;
            transition: all 0.2s ease;

            &:last-child {
              margin-bottom: 0;
            }

            &:hover {
              border-color: #d0e8ff;
              background-color: #f8fbff;
              box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
            }

            .schedule-row {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 8px;

              .time-controls {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;

                .weekday-select {
                  width: 68px;
                  flex-shrink: 0;
                }

                .duration-select {
                  width: 80px;
                  flex-shrink: 0;
                }

                .time-range-input {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  flex: 1;

                  .start-time-select {
                    flex: 1;
                    min-width: 80px;
                  }

                  .time-separator {
                    color: #909399;
                    font-size: 13px;
                    font-weight: 500;
                    padding: 0 2px;
                  }

                  .end-time {
                    color: #606266;
                    font-size: 13px;
                    font-weight: 500;
                    min-width: 40px;
                    text-align: center;
                    background-color: #f5f7fa;
                    padding: 4px 6px;
                    border-radius: 3px;
                    border: 1px solid #e4e7ed;
                  }
                }
              }

              .slot-actions {
                margin-left: 8px;
                flex-shrink: 0;

                .delete-btn {
                  width: 24px;
                  height: 24px;
                }
              }
            }

            .time-summary {
              margin-bottom: 2px;

              .time-tag {
                background-color: #ecf5ff;
                color: #409eff;
                border-color: #d0e8ff;
                font-size: 12px;
                height: 20px;
                line-height: 18px;
              }
            }
          }
        }

        .empty-time-slots {
          text-align: center;
          padding: 30px 15px;
        }
      }
    }

    // 申请表单卡片样式
    .application-form-card {
      margin-top: 16px;

      .search-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .operation-mode-selector {
          .el-radio-button {
            --el-radio-button-font-size: 12px;
          }
        }
      }

      .application-form {
        .selected-teachers {
          min-height: 32px;
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          align-items: center;

          .teacher-tag {
            background-color: #e1f3d8;
            border-color: #67c23a;
            color: #67c23a;
          }

          .no-selection {
            color: #909399;
            font-size: 14px;
            font-style: italic;
          }
        }
      }
    }
  }

  .result-panel {
    flex: 1;

    .empty-result {
      height: 400px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .result-card {
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .student-info {
        margin-bottom: 20px;

        h4 {
          margin: 0 0 10px 0;
          color: #303133;
          font-size: 16px;
        }
      }

      .teachers-list {
        h4 {
          margin: 0 0 12px 0;
          color: #303133;
          font-size: 16px;
        }

        .teachers-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 12px;

          .teacher-card {
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 12px;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
            }

            .teacher-header {
              margin-bottom: 10px;

              .teacher-basic {
                h5 {
                  margin: 0 0 4px 0;
                  color: #303133;
                  font-size: 15px;
                }

                .teacher-phone {
                  font-size: 12px;
                  color: #909399;
                }
              }
            }

            .teacher-info {
              margin-bottom: 12px;

              .info-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 6px;
              }

              .info-item {
                font-size: 12px;
                flex: 1;

                .label {
                  color: #909399;
                  margin-right: 4px;
                }

                .subject-tag,
                .training-tag,
                .course-tag {
                  margin-right: 4px;
                  margin-bottom: 4px;
                }
              }
            }

            .teacher-actions {
              display: flex;
              gap: 6px;
              align-items: center;

              .el-button {
                flex: 1;
              }

              .teacher-checkbox {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 6px 12px;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                background-color: #f5f7fa;
                transition: all 0.3s;

                &:hover {
                  border-color: #409eff;
                  background-color: #ecf5ff;
                }

                :deep(.el-checkbox__label) {
                  font-size: 12px;
                  color: #606266;
                }

                &.is-checked {
                  border-color: #67c23a;
                  background-color: #f0f9ff;

                  :deep(.el-checkbox__label) {
                    color: #67c23a;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    .match-layout {
      flex-direction: column;
      gap: 15px;
    }

    .search-panel {
      width: 100%;

      .search-card {
        .time-slots-container {
          .time-slots-list {
            .time-slot-item {
              .schedule-row {
                .time-controls {
                  flex-wrap: wrap;
                  gap: 6px;

                  .weekday-select {
                    width: 60px;
                  }

                  .duration-select {
                    width: 70px;
                  }

                  .time-range-input {
                    min-width: 140px;

                    .start-time-select {
                      min-width: 70px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .search-panel {
      .search-card {
        .time-slots-container {
          .time-slots-list {
            .time-slot-item {
              padding: 10px;

              .schedule-row {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;

                .time-controls {
                  justify-content: space-between;

                  .weekday-select,
                  .duration-select {
                    flex: 1;
                    min-width: 0;
                  }

                  .time-range-input {
                    flex: 2;
                    min-width: 0;
                  }
                }

                .slot-actions {
                  margin-left: 0;
                  align-self: center;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 年级快速选择按钮样式
.grade-quick-select {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;

  .el-button {
    font-size: 12px;
    padding: 2px 6px;
    height: auto;
    min-height: 20px;
  }
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.2;
}
</style>
