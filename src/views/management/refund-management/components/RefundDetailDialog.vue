<template>
  <el-dialog v-model="visible" :title="dialogTitle" width="900px" :close-on-click-modal="false">
    <div class="detail" v-loading="loading">
      <template v-if="detail">
        <!-- 顶部状态与操作 -->
        <div class="header-row">
          <div class="status-block">
            <el-tag :type="statusTagType(normalizedStatus)">{{ detail.refundStatusDesc || detail.refundStatus || '-' }}</el-tag>
            <span class="amount">¥{{ detail.refundAmountYuan || '0.00' }}</span>
          </div>
          <div class="ops">
            <el-button size="small" @click="onCopy(detail.refundNo, '退款单号')">复制退款单号</el-button>
            <el-button size="small" @click="onCopy(detail.orderNo, '订单号')">复制订单号</el-button>
            <el-button v-if="detail.refundTrxId" size="small" @click="onCopy(detail.refundTrxId!, '退款交易流水ID')">复制退款流水ID</el-button>
            <el-button type="primary" size="small" :loading="loading" @click="refresh">刷新</el-button>
          </div>
        </div>

        <!-- 进度步骤 -->
        <el-steps :active="stepActive" finish-status="success" align-center>
          <el-step title="提交申请" />
          <el-step :title="approvalTitle" />
          <el-step :title="refundTitle" />
        </el-steps>

        <!-- 基础信息 -->
        <el-card shadow="never" class="mt16">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="退款单号">
              <span>{{ detail.refundNo }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="订单号">
              <span>{{ detail.orderNo }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="消耗课时">
              <span>{{ detail.courseHoursRefundCalculationResult?.consumedHours }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="退款课时">
              <span>{{ detail.courseHoursRefundCalculationResult?.refundableHours }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="退款类型">{{ detail.refundTypeDesc || detail.refundType || '-' }}</el-descriptions-item>
            <el-descriptions-item label="退款方式">{{ detail.refundMethodDesc || detail.refundMethod || '-' }}</el-descriptions-item>
            <el-descriptions-item label="退款完成时间">{{ formatDateTime(detail.refundTime) }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(detail.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="退款原因" :span="2">{{ detail.refundReason || '-' }}</el-descriptions-item>
            <el-descriptions-item v-if="detail.remark" label="备注" :span="2">{{ detail.remark }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 退款交易流水 -->
        <el-card shadow="never" class="mt16">
          <el-table :data="detail.refundTrxs" border stripe>
            <el-table-column label="原交易流水号" prop="originalTrxId" width="240" />
            <el-table-column label="退款交易流水号" prop="refundTrxId" width="240" />
            <el-table-column label="退款金额" prop="trxAmt" width="120" align="center">
              <template #default="{ row }">
                <span class="amount-text">¥{{ (row.refundAmount / 100).toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="退款方式" prop="refundTrxId" width="120" show-overflow-tooltip >
              <template #default="{ row }">
                {{ row.payType }}
              </template>
            </el-table-column>
            <el-table-column label="消耗课时" prop="trxAmt" width="120" align="center">
              <template #default="{ row }">
                {{ row.calcCourseHours?.consumedFromThisPayment }}
              </template>
            </el-table-column>
            <el-table-column label="退款课时" prop="trxAmt" width="120" align="center">
              <template #default="{ row }">
                {{ row.calcCourseHours?.refundableHours }}
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="refundStatus" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="statusTagType(row.refundStatus)" size="small">{{ row.refundStatus }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="退款时间" prop="createTime" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.refundTime) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 平台响应 -->
<!--        <el-card shadow="never" class="mt16">-->
<!--          <template #header>-->
<!--            <div class="card-header">支付平台响应</div>-->
<!--          </template>-->
<!--          <div v-if="prettyPlatformResponse" class="code-block">-->
<!--            <pre>{{ prettyPlatformResponse }}</pre>-->
<!--          </div>-->
<!--          <div v-else class="empty-text">暂无平台响应</div>-->
<!--        </el-card>-->
      </template>
      <template v-else>
        <el-empty description="正在加载或暂无数据" />
      </template>
    </div>
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
      <el-button type="primary" :loading="loading" @click="refresh">刷新</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDateTime } from '@/utils/date.js'
import { getRefundRecordDetail } from '@/api/management/refund-records'

interface DetailResp {
  id: string
  orderId: string
  orderNo: string
  refundNo: string
  refundType?: string
  refundTypeDesc?: string
  refundAmountYuan?: string
  refundReason?: string
  refundStatus?: string
  refundStatusDesc?: string
  refundMethod?: string
  refundMethodDesc?: string
  originalTrxId?: string
  refundTrxId?: string
  platformRefundId?: string
  platformResponse?: string
  errorMessage?: string
  remark?: string
  refundTime?: string | number | Date
  createTime?: string | number | Date
  updateTime?: string | number | Date
  refundTrxs?: DetailTrxResp[]
  courseHoursRefundCalculationResult?: any
}

interface DetailTrxResp {
  originalTrxId?: string
  refundTrxId?: string
  refundType?: string
  refundAmount?: number
  refundStatus?: string
  refundTime?: Date
}

const props = defineProps<{ modelValue: boolean; refundId?: string }>()
const emit = defineEmits<{ (e: 'update:modelValue', v: boolean): void }>()

const visible = ref<boolean>(props.modelValue)
const detail = ref<DetailResp | null>(null)
const loading = ref(false)

watch(() => props.modelValue, (v) => (visible.value = v))
watch(visible, (v) => {
  emit('update:modelValue', v)
  if (v && props.refundId) fetchDetail(props.refundId)
})

watch(
  () => props.refundId,
  (id) => { if (visible.value && id) fetchDetail(id) }
)

const dialogTitle = computed(() => `退款详情${detail.value?.refundNo ? ' - ' + detail.value.refundNo : ''}`)

const fetchDetail = async (id: string) => {
  try {
    loading.value = true
    const res: any = await getRefundRecordDetail(id)
    if (res.code === 200) {
      detail.value = res.data
    } else {
      ElMessage.error(res.msg || '获取详情失败')
    }
  } catch (e) {
    console.error(e)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

const refresh = () => { if (props.refundId) fetchDetail(props.refundId) }

const onCopy = async (text?: string, label?: string) => {
  if (!text) return
  try {
    await navigator.clipboard?.writeText(text)
    ElMessage.success(`${label || '内容'}已复制`)
  } catch {
    ElMessage.success(`${label || '内容'}：${text}`)
  }
}

// 规范化状态（兼容中英文与历史状态）
const normalizedStatus = computed(() => {
  const s = (detail.value?.refundStatus || '').toLowerCase()
  switch (s) {
    case '待审批':
    case 'wait_approve':
    case 'waitapprove':
      return '待审批'
    case '审批不通过':
    case 'reject':
      return '审批不通过'
    case '待退款':
    case 'wait_refund':
      return '待退款'
    case '已退款':
    case 'refunded':
    case 'success':
      return '已退款'
    case 'processing':
      return '待退款'
    case 'failed':
      return '审批不通过'
    default:
      return detail.value?.refundStatus || ''
  }
})

const statusTagType = (status?: string) => {
  const map: Record<string, string> = {
    '待审批': 'warning',
    '审批不通过': 'danger',
    '待退款': 'primary',
    '已退款': 'success'
  }
  return (status && map[status]) || 'info'
}

const stepActive = computed(() => {
  const s = normalizedStatus.value
  if (s === '待审批') return 1
  if (s === '审批不通过') return 2
  if (s === '待退款') return 2
  if (s === '已退款') return 3
  return 1
})

const approvalTitle = computed(() => (normalizedStatus.value === '审批不通过' ? '审批拒绝' : '审批通过'))
const refundTitle = computed(() => (normalizedStatus.value === '已退款' ? '退款完成' : '退款处理中'))

const prettyPlatformResponse = computed(() => {
  const raw = detail.value?.platformResponse
  if (!raw) return ''
  try {
    const obj = typeof raw === 'string' ? JSON.parse(raw) : raw
    return JSON.stringify(obj, null, 2)
  } catch {
    return String(raw)
  }
})

onMounted(() => {
  if (props.refundId) {
    fetchDetail(props.refundId)
  }
})
</script>

<style scoped>
.detail { padding: 8px 0; }
.header-row { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
.status-block { display: flex; align-items: center; gap: 12px; }
.amount { color: #E6A23C; font-weight: 600; }
.mt16 { margin-top: 16px; }
.card-header { font-weight: 600; }
.code-block { background: #0b1020; color: #d6deeb; padding: 12px; border-radius: 6px; overflow: auto; max-height: 260px; }
.empty-text { color: #909399; }
</style>

