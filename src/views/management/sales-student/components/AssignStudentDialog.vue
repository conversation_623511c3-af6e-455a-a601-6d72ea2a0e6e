<template>
  <el-dialog
    v-model="visible"
    :title="isBatch ? '批量分配学生' : '分配学生'"
    width="500px"
    :before-close="handleClose"
  >
    <div v-if="isBatch" class="batch-info">
      <el-alert
        :title="`将为 ${studentList.length} 个学生分配销售人员`"
        type="info"
        :closable="false"
        show-icon
      />
      <div class="student-list">
        <el-tag
          v-for="student in studentList"
          :key="student.id"
          class="student-tag"
          size="small"
        >
          {{ student.name }}
        </el-tag>
      </div>
    </div>

    <div v-else class="single-info">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="学生姓名">
          {{ studentData?.name }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号码">
          {{ studentData?.phone }}
        </el-descriptions-item>
        <el-descriptions-item label="年级">
          {{ getGradeText(studentData?.grade) }}
        </el-descriptions-item>
        <el-descriptions-item label="学校">
          {{ studentData?.school }}
        </el-descriptions-item>
        <el-descriptions-item label="当前销售">
          <span v-if="studentData?.salesName">{{ studentData.salesName }}</span>
          <el-tag v-else type="warning" size="small">未分配</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      style="margin-top: 20px"
      v-loading="loading"
    >
      <!-- <el-form-item label="销售组" prop="salesGroupId">
        <el-select 
          v-model="form.salesGroupId" 
          placeholder="请选择销售组" 
          style="width: 100%"
          @change="handleSalesGroupChange"
          clearable
        >
          <el-option
            v-for="group in salesGroupOptions"
            :key="group.id"
            :label="group.name"
            :value="group.id"
          />
        </el-select>
      </el-form-item> -->

      <el-form-item label="销售人员" prop="salesId">
        <el-select 
          v-model="form.salesId" 
          placeholder="请选择销售人员" 
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="sales in salesOptions"
            :key="sales.id"
            :label="`${sales.name} (${sales.phone})`"
            :value="sales.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分配原因" prop="assignReason">
        <el-input
          v-model="form.assignReason"
          type="textarea"
          :rows="3"
          placeholder="请输入分配原因（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { assignStudentToSalesApi as assignSalesStudent, batchAssignStudentsToSalesApi as batchAssignSalesStudent } from '@/api/management/salesStudent'
import { getSalesStaffOptionsApi as getSalesOptions } from '@/api/management/salesStaff'
import { getSalesGroupOptionsApi as getSalesGroupOptions } from '@/api/management/salesGroup'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentData: {
    type: Object,
    default: null
  },
  studentList: {
    type: Array,
    default: () => []
  },
  isBatch: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const salesGroupOptions = ref([])
const salesOptions = ref([])

// 年级选项
const GRADE_OPTIONS = [
  { label: '一年级', value: 1 },
  { label: '二年级', value: 2 },
  { label: '三年级', value: 3 },
  { label: '四年级', value: 4 },
  { label: '五年级', value: 5 },
  { label: '六年级', value: 6 },
  { label: '初一', value: 7 },
  { label: '初二', value: 8 },
  { label: '初三', value: 9 },
  { label: '高一', value: 10 },
  { label: '高二', value: 11 },
  { label: '高三', value: 12 }
]

// 表单数据
const form = reactive({
  salesGroupId: '',
  salesId: '',
  assignReason: ''
})

// 表单验证规则
const rules = {
  salesId: [
    { required: true, message: '请选择销售人员', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    initForm()
    getSalesGroupOptionList()
    getSalesOptionList()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 初始化表单
const initForm = () => {
  // 重置表单
  Object.assign(form, {
    salesGroupId: '',
    salesId: '',
    assignReason: ''
  })
  
  // 如果是单个分配且已有销售信息，预填充
  if (!props.isBatch && props.studentData) {
    if (props.studentData.salesGroupId) {
      form.salesGroupId = props.studentData.salesGroupId
      getSalesOptionList(props.studentData.salesGroupId)
    }
    if (props.studentData.salesId) {
      form.salesId = props.studentData.salesId
    }
  }
}

// 获取销售组选项
const getSalesGroupOptionList = async () => {
  try {
    const response = await getSalesGroupOptions()
    salesGroupOptions.value = response.data
  } catch (error) {
    console.error('获取销售组选项失败:', error)
  }
}

// 获取销售人员选项
const getSalesOptionList = async (groupId = '') => {
  try {
    const params = {}
    if (groupId) {
      params.groupId = groupId
    }
    const response = await getSalesOptions(params)
    salesOptions.value = response.data || []
  } catch (error) {
    console.error('获取销售人员选项失败:', error)
    salesOptions.value = []
  }
}

// 销售组变化处理
const handleSalesGroupChange = (groupId) => {
  // 清空已选择的销售人员
  form.salesId = ''
  // 根据选择的销售组重新获取销售人员选项
  getSalesOptionList(groupId)
}

// 获取年级文本
const getGradeText = (grade) => {
  const option = GRADE_OPTIONS.find(item => item.value === parseInt(grade))
  return option ? option.label : grade
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (props.isBatch) {
      // 批量分配
      const studentIds = props.studentList.map(student => student.id)
      await batchAssignSalesStudent({
        studentIds,
        salesId: form.salesId,
        salesGroupId: form.salesGroupId,
        assignReason: form.assignReason
      })
      ElMessage.success(`成功分配 ${studentIds.length} 个学生`)
    } else {
      // 单个分配
      await assignSalesStudent({
        studentId: props.studentData.id,
        salesId: form.salesId,
        salesGroupId: form.salesGroupId,
        assignReason: form.assignReason
      })
      ElMessage.success('分配成功')
    }
    
    emit('refresh')
    handleClose()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('分配失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.batch-info {
  margin-bottom: 20px;
}

.student-list {
  margin-top: 10px;
  max-height: 120px;
  overflow-y: auto;
}

.student-tag {
  margin: 2px 4px 2px 0;
}

.single-info {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
