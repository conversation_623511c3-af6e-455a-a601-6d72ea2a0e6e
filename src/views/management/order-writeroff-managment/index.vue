<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="核销平台" prop="orderSource">
        <el-select
          v-model="queryParams.orderSource"
          placeholder="请选择核销平台"
          clearable
        >
          <el-option
            v-for="item in OrderWriteroffSource"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="核销订单号" prop="woffOrderNo">
        <el-input
          v-model="queryParams.woffOrderNo"
          placeholder="请输入核销订单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="系统订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入系统订单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="核销状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请输入核销状态"
          clearable
        >
          <el-option label="待核销" value="待核销" />
          <el-option label="核销成功" value="核销成功" />
        </el-select>
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="Plus"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['order:writeoff:add']"-->
<!--        >新增核销</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="Download"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['order:writeoff:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
<!--    </el-row>-->

    <el-table v-loading="loading" :data="writerOffList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="核销订单号" align="center" prop="woffOrderNo" min-width="180" />
      <el-table-column label="核销平台" align="center" prop="woffOrderSource" width="120">
        <template #default="scope">
          <el-tag :type="getSourceTagType(scope.row.woffOrderSource)">
            {{ getSourceLabel(scope.row.woffOrderSource) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="系统订单号" align="center" prop="orderNo" min-width="180" />
      <el-table-column label="产品信息" align="center" prop="productName" width="200" />
      <el-table-column label="核销状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="核销人员" align="center" prop="woffUserName" width="120" />
      <el-table-column label="学生姓名" align="center" prop="studentName" width="200" />
      <el-table-column label="核销时间" align="center" prop="woffTime" width="180">
        <template #default="scope">
          <span>{{ scope.row.woffTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单截图" align="center" prop="orderImg" width="120">
        <template #default="scope">
          <el-image
            v-if="scope.row.orderImg"
            :src="scope.row.orderImg"
            style="width: 60px; height: 40px;"
            fit="cover"
            :preview-src-list="[scope.row.orderImg]"
            preview-teleported
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
            v-hasPermi="['order:writeoff:query']"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 核销记录详情 -->
    <el-dialog v-model="detailVisible" title="核销记录详情" width="1200px" append-to-body>
      <OrderWriterOffDetail 
        v-if="detailVisible && currentDetailId" 
        :id="currentDetailId" 
        @close="detailVisible = false" 
      />
    </el-dialog>

  </div>
</template>

<script setup name="OrderWriterOff">
import {delWriterOff, getWriterOff, listWriterOff} from "@/api/management/order-writeroff.js";
import { OrderWriteroffSource } from "@/api/management/common/OrderConstants.ts";
import OrderWriterOffDetail from './detail.vue';

const { proxy } = getCurrentInstance();

const writerOffList = ref([]);
const detailVisible = ref(false);
const addVisible = ref(false);
const studentSelectVisible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const currentDetailId = ref(null);
const selectedStudent = ref(null);
const dateRange = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: null,
    orderSource: null,
    status: null,
    woffUserId: null,
    productId: null
  }
});

const { queryParams } = toRefs(data);

/** 查询核销记录列表 */
function getList() {
  loading.value = true;
  const params = { ...queryParams.value };
  if (dateRange.value && dateRange.value.length === 2) {
    params.beginTime = dateRange.value[0];
    params.endTime = dateRange.value[1];
  }
  listWriterOff(params).then(response => {
    writerOffList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  selectedStudent.value = null;
  studentSelectVisible.value = true;
}

/** 学生选择确认 */
function handleStudentSelect(student) {
  selectedStudent.value = student;
  studentSelectVisible.value = false;
  addVisible.value = true;
}

/** 新增成功回调 */
function handleAddSuccess() {
  getList();
}

/** 详情按钮操作 */
function handleDetail(row) {
  const id = row.id || ids.value[0];
  currentDetailId.value = id;
  detailVisible.value = true;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const writerOffIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除核销记录编号为"' + writerOffIds + '"的数据项？').then(function() {
    return delWriterOff(writerOffIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('order/writeroff/export', {
    ...queryParams.value
  }, `writeroff_${new Date().getTime()}.xlsx`);
}

/** 获取平台标签类型 */
function getSourceTagType(source) {
  const typeMap = {
    'DOUDIAN': 'primary',
    'TAOBAO': 'success',
    'WECHAT': 'warning',
    'OTHER': 'info'
  };
  return typeMap[source] || 'info';
}

/** 获取平台标签文本 */
function getSourceLabel(source) {
  const item = OrderWriteroffSource.find(item => item.value === source);
  return item ? item.label : source;
}

/** 获取状态标签类型 */
function getStatusTagType(status) {
  const typeMap = {
    '待核销': 'warning',
    '核销成功': 'success',
    '核销失败': 'danger'
  };
  return typeMap[status] || 'info';
}


getList();
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>