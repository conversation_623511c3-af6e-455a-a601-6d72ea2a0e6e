<template>
  <div class="writeoff-detail">
    <div v-if="detailData" class="detail-content">
      <!-- 核销信息 -->
      <el-card class="info-section" shadow="never">
        <template #header>
          <h3>核销信息</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="核销记录ID">{{ detailData.orderWriterOffs?.id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销订单号">{{ detailData.orderWriterOffs?.woffOrderNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销平台">
            <el-tag :type="getSourceTagType(detailData.orderWriterOffs?.woffOrderSource)">
              {{ detailData.orderWriterOffs?.woffOrderSource || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核销状态">
            <el-tag :type="getStatusTagType(detailData.orderWriterOffs?.status)">
              {{ detailData.orderWriterOffs?.status || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核销时间">{{ formatTime(detailData.orderWriterOffs?.woffTime) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ detailData.studentName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销人员">{{ detailData.writeroffUserName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="销售信息">
            {{ detailData.orders?.sales?.salleName || '-' }}
            <el-tag type="info" style="margin-left: 10px">{{ detailData.orders?.sales?.groupName || '-' }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 产品信息 -->
      <el-card class="info-section" shadow="never">
        <template #header>
          <h3>产品信息</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品名称">{{ detailData.orders?.products?.name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="产品编号">{{ detailData.orders?.products?.productNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="产品描述">{{ detailData.orders?.products?.description || '-' }}</el-descriptions-item>
          <el-descriptions-item label="学科">{{ detailData.orders?.products?.subject || '-' }}</el-descriptions-item>
          <el-descriptions-item label="课程类型">{{ detailData.orders?.products?.courseType || '-' }}</el-descriptions-item>
          <el-descriptions-item label="适用年级">
            <el-tag 
              v-for="grade in detailData.orders?.products?.applicableGrades || []" 
              :key="grade" 
              size="small" 
              style="margin-right: 5px;"
            >
              {{ grade }}
            </el-tag>
            <span v-if="!detailData.orders?.products?.applicableGrades?.length">-</span>
          </el-descriptions-item>
          <el-descriptions-item label="单价">{{ formatPrice(detailData.orders?.products?.unitPrice) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="课时">{{ detailData.orders?.products?.quantity || '-' }}&nbsp;&nbsp;赠送课时：{{ detailData.orders?.products?.bonusHoursQuantity || '-' }}</el-descriptions-item>
          <el-descriptions-item label="原价">{{ formatPrice(detailData.orders?.products?.originalPrice) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="售价">{{ formatPrice(detailData.orders?.products?.sellingPrice) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="教材费">{{ formatPrice(detailData.orders?.products?.materialFee) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="产品状态">
            <el-tag :type="detailData.orders?.products?.status === '上架' ? 'success' : 'danger'">
              {{ detailData.orders?.products?.status || '-' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 订单信息 -->
      <el-card class="info-section" shadow="never">
        <template #header>
          <h3>订单信息</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="系统订单号">{{ detailData.orders?.no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="订单来源">{{ detailData.orders?.source || '-' }}</el-descriptions-item>
          <el-descriptions-item label="订单内容">{{ detailData.orders?.body || '-' }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusTagType(detailData.orders?.orderStatus)">
              {{ detailData.orders?.orderStatus || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单总金额">{{ formatPrice(detailData.orders?.totalAmt) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="已支付金额">{{ formatPrice(detailData.orders?.amtPaid) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="未支付金额">{{ formatPrice(detailData.orders?.amtUnpaid) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ detailData.orders?.trxMethod || '-' }}</el-descriptions-item>
          <el-descriptions-item label="签署状态">
            <el-tag :type="detailData.orders?.signStatus === '已签署' ? 'success' : 'warning'">
              {{ detailData.orders?.signStatus || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="销售组">{{ detailData.orders?.sales?.groupName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销状态">
            <el-tag :type="getStatusTagType(detailData.orders?.writerOffStatus)">
              {{ detailData.orders?.writerOffStatus || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核销时间">{{ formatTime(detailData.orders?.writerOffTime) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{ formatTime(detailData.orders?.createTime) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="订单备注">{{ detailData.orders?.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <div v-else class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup name="OrderWriterOffDetail">
import { ref, onMounted } from 'vue'
import { getWriterOff } from "@/api/management/order-writeroff.js"
import { OrderWriteroffSource } from "@/api/management/common/OrderConstants.ts"

const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['close'])

const loading = ref(false)
const detailData = ref(null)

/** 获取详情数据 */
const getDetailData = async () => {
  if (!props.id) return
  
  loading.value = true
  try {
    const response = await getWriterOff(props.id)
    detailData.value = response.data
  } catch (error) {
    console.error('获取核销详情失败:', error)
  } finally {
    loading.value = false
  }
}

/** 获取平台标签类型 */
const getSourceTagType = (source) => {
  const typeMap = {
    '抖店': 'primary',
    '星橙CRM': 'success',
    '系统创建': 'warning'
  }
  return typeMap[source] || 'info'
}

/** 获取平台标签文本 */
const getSourceLabel = (source) => {
  const item = OrderWriteroffSource.find(item => item.value === source)
  return item ? item.label : source
}

/** 获取状态标签类型 */
const getStatusTagType = (status) => {
  const typeMap = {
    '待核销': 'warning',
    '核销成功': 'success',
    '核销失败': 'danger'
  }
  return typeMap[status] || 'info'
}

/** 获取订单状态标签类型 */
const getOrderStatusTagType = (status) => {
  const typeMap = {
    '已全额支付': 'success',
    '待支付': 'warning',
    '部分支付': 'primary',
    '已取消': 'danger'
  }
  return typeMap[status] || 'info'
}

/** 格式化时间 */
const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return timeStr
  }
}

/** 格式化价格 */
const formatPrice = (price) => {
  if (price === null || price === undefined) return '-'
  // 将分转换为元
  const yuan = (price / 100).toFixed(2)
  return `¥${yuan}`
}

onMounted(() => {
  getDetailData()
})
</script>

<style scoped>
.writeoff-detail {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-section {
  border: 1px solid #e4e7ed;
}

.info-section :deep(.el-card__header) {
  background-color: #f5f7fa;
  padding: 12px 20px;
}

.info-section h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style>