<template>
  <el-dialog v-model="visible" title="合同详情" width="880px" :close-on-click-modal="false">
    <div class="detail" v-loading="loading">
      <template v-if="detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合同编号">{{ detail.contractNo }}</el-descriptions-item>
          <el-descriptions-item label="所属订单编号">{{ detail.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="detail.status === '已签署'" type="success">已签署</el-tag>
            <el-tag v-else type="warning">未签署</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发起人">{{ detail.initiateUsername }}</el-descriptions-item>
          <el-descriptions-item label="签署人">{{ detail.signerUsername }}</el-descriptions-item>
          <el-descriptions-item label="签署时间">{{ formatDateTime(detail.signTime) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(detail.createTime) }}</el-descriptions-item>
        </el-descriptions>

<!--        <el-card shadow="never" class="mt16">-->
<!--          <template #header>-->
<!--            <div class="card-header">填充内容</div>-->
<!--          </template>-->
<!--          <div v-if="pretty(detail.fillContent)" class="code-block"><pre>{{ pretty(detail.fillContent) }}</pre></div>-->
<!--          <div v-else class="empty-text">暂无</div>-->
<!--        </el-card>-->

<!--        <el-card shadow="never" class="mt16">-->
<!--          <template #header>-->
<!--            <div class="card-header">签署内容</div>-->
<!--          </template>-->
<!--          <div v-if="pretty(detail.signerContent)" class="code-block"><pre>{{ pretty(detail.signerContent) }}</pre></div>-->
<!--          <div v-else class="empty-text">暂无</div>-->
<!--        </el-card>-->

        <el-card shadow="never" class="mt16">
          <template #header>
            <div class="card-header">签署链接信息</div>
          </template>
<!--          <div v-if="pretty(detail.signUrlInfo)" class="code-block"><pre>{{ pretty(detail.signUrlInfo) }}</pre></div>-->
          <div v-if="pretty(detail.signUrlInfo)"><pre>{{ pretty(detail.signUrlInfo?.data?.shortUrl) }}</pre></div>
          <div v-else class="empty-text">暂无</div>
        </el-card>
      </template>
      <template v-else>
        <el-empty description="正在加载或暂无数据" />
      </template>
    </div>
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
      <el-button type="primary" :loading="loading" @click="refresh">刷新</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, watch} from 'vue'
import {ElMessage} from 'element-plus'
import {formatDateTime} from '@/utils/date.js'
import {ContractsDetailResp, getContractDetail} from '@/api/management/contracts'


const props = defineProps<{ modelValue: boolean; id?: string }>()
const emit = defineEmits<{ (e: 'update:modelValue', v: boolean): void }>()

const visible = ref<boolean>(props.modelValue)
const loading = ref(false)
const detail = ref<ContractsDetailResp | null>(null)

watch(() => props.modelValue, (v) => (visible.value = v))
watch(visible, (v) => { emit('update:modelValue', v); if (v && props.id) fetchDetail(props.id) })
watch(() => props.id, (id) => { if (visible.value && id) fetchDetail(id) })

const fetchDetail = async (id: string) => {
  try {
    loading.value = true
    const res: ContractsDetailResp = await getContractDetail(id)
    if (res.code === 200) {
      detail.value = res.data
    } else {
      ElMessage.error(res.msg || '获取详情失败')
    }
  } catch (e) {
    console.error(e)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

const refresh = () => { if (props.id) fetchDetail(props.id) }

const pretty = (val: any) => {
  if (!val) return ''
  try { return JSON.stringify(typeof val === 'string' ? JSON.parse(val) : val, null, 2) } catch { return String(val) }
}
</script>

<style scoped>
.detail { padding: 8px 0; }
.mt16 { margin-top: 16px; }
.card-header { font-weight: 600; }
.code-block { background: #0b1020; color: #d6deeb; padding: 12px; border-radius: 6px; overflow: auto; max-height: 260px; }
.empty-text { color: #909399; }
</style>

