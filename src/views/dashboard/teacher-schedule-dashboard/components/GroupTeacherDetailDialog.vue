<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="`${group?.groupName || ''} - 教师详情`"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
    <div v-loading="loading" class="group-teacher-detail-container">
      <!-- 教学组基本信息 -->
      <el-card class="group-info-card" shadow="never">
        <div class="group-info">
          <div class="group-basic">
            <h3>{{ group?.groupName }}</h3>
            <p class="group-desc">{{ group?.description || '暂无描述' }}</p>
          </div>
          <div class="group-stats">
            <div class="stat-item">
              <span class="stat-label">总课次</span>
              <span class="stat-value">{{ group?.totalAvailableSlots || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">教师数</span>
              <span class="stat-value">{{ group?.teacherCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均课次</span>
              <span class="stat-value">{{ averageSlotsPerTeacher }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 教师列表 -->
      <el-card class="teacher-list-card">
        <template #header>
          <div class="card-header">
            <span>教师列表 ({{ teachers.length }}人)</span>
            <div class="header-actions">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索教师姓名"
                size="small"
                style="width: 200px"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </template>

        <el-table
          :data="filteredTeachers"
          stripe
          style="width: 100%"
          :default-sort="{ prop: 'availableSlots', order: 'descending' }"
        >
          <el-table-column prop="name" label="姓名" width="120">
            <template #default="{ row }">
              <div class="teacher-name">
                <span>{{ row.name }}</span>
                <el-tag v-if="row.nickname" size="small" type="info" style="margin-left: 8px">
                  {{ row.nickname }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="phone" label="手机号" width="130" />

          <el-table-column prop="availableSlots" label="可排课课次" width="120" sortable>
            <template #default="{ row }">
              <el-tag :type="getSlotsTagType(row.availableSlots)" size="small">
                {{ row.availableSlots }}课次
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="subjects" label="教授学科" width="150">
            <template #default="{ row }">
              <div v-if="row.subjects && row.subjects.length > 0" class="subjects-container">
                <el-tag
                  v-for="subject in row.subjects.slice(0, 2)"
                  :key="subject"
                  size="small"
                  style="margin-right: 4px; margin-bottom: 4px"
                >
                  {{ subject }}
                </el-tag>
                <el-tag v-if="row.subjects.length > 2" size="small" type="info">
                  +{{ row.subjects.length - 2 }}
                </el-tag>
              </div>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="suitableGrades" label="适合年级" width="120">
            <template #default="{ row }">
              <div v-if="row.suitableGrades && row.suitableGrades.length > 0">
                <el-tag
                  v-for="grade in row.suitableGrades.slice(0, 2)"
                  :key="grade"
                  size="small"
                  type="success"
                  style="margin-right: 4px"
                >
                  {{ grade }}
                </el-tag>
                <span v-if="row.suitableGrades.length > 2" class="text-muted">...</span>
              </div>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewTeacherDetail(row)">
                详情
              </el-button>
              <el-button type="text" size="small" @click="viewTeacherSchedule(row)">
                排课
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <el-empty
          v-if="!loading && filteredTeachers.length === 0"
          description="暂无教师数据"
          :image-size="80"
        />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="false" type="primary" @click="exportTeacherList">
          <el-icon><Download /></el-icon>
          导出列表
        </el-button>
      </div>
    </template>
  </el-dialog>

    <!-- 教师详情对话框 -->
    <TeacherDetailDialog
      v-model="teacherDetailVisible"
      :teacher="selectedTeacher"
      @edit="handleEditTeacher"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download } from '@element-plus/icons-vue'
import { teachingGroupApi } from '@/api/management/teachingGroup'
import TeacherDetailDialog from '@/views/management/teacher/components/TeacherDetailDialog.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  },
  dateRange: {
    type: Object,
    default: () => ({ startDate: '', endDate: '' })
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit-teacher'])

// 响应式数据
const loading = ref(false)
const teachers = ref([])
const searchKeyword = ref('')
const teacherDetailVisible = ref(false)
const selectedTeacher = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const filteredTeachers = computed(() => {
  if (!searchKeyword.value) return teachers.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return teachers.value.filter(teacher => 
    teacher.name?.toLowerCase().includes(keyword) ||
    teacher.nickname?.toLowerCase().includes(keyword) ||
    teacher.phone?.includes(keyword)
  )
})

const averageSlotsPerTeacher = computed(() => {
  if (!props.group || !props.group.teacherCount || props.group.teacherCount === 0) return '0'
  return (props.group.totalAvailableSlots / props.group.teacherCount).toFixed(1)
})

// 监听对话框打开
watch(dialogVisible, (newVal) => {
  if (newVal && props.group) {
    loadGroupTeachers()
  }
})

// 方法
const loadGroupTeachers = async () => {
  if (!props.group?.groupId) return

  loading.value = true
  try {
    const response = await teachingGroupApi.getGroupTeachersApi(props.group.groupId, {
      pageNum: 1,
      pageSize: 1000
    })

    teachers.value = response.data?.records || response.data || []
  } catch (error) {
    console.error('加载教学组教师失败:', error)
    ElMessage.error('加载教师列表失败')
    teachers.value = []
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleClose = () => {
  dialogVisible.value = false
  searchKeyword.value = ''
  teachers.value = []
}

const viewTeacherDetail = (teacher) => {
  selectedTeacher.value = teacher
  teacherDetailVisible.value = true
}

const viewTeacherSchedule = (teacher) => {
  ElMessage.info(`查看 ${teacher.name} 的排课安排`)
  // 这里可以打开教师排课对话框或跳转到排课页面
}

const handleEditTeacher = (teacher) => {
  emit('edit-teacher', teacher)
}

const exportTeacherList = () => {
  if (filteredTeachers.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }
  
  ElMessage.info('导出功能开发中...')
  // 这里可以实现Excel导出功能
}

// 工具方法
const getSlotsTagType = (slots) => {
  if (slots >= 20) return 'success'
  if (slots >= 10) return 'warning'
  if (slots >= 5) return 'info'
  return 'danger'
}

const getStatusTagType = (status) => {
  switch (status) {
    case 'active': return 'success'
    case 'inactive': return 'danger'
    case 'pending': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'active': return '正常'
    case 'inactive': return '停用'
    case 'pending': return '待审核'
    default: return '未知'
  }
}
</script>

<style scoped>
.group-teacher-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.group-info-card {
  margin-bottom: 16px;
}

.group-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.group-basic h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.group-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.group-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.teacher-list-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.teacher-name {
  display: flex;
  align-items: center;
}

.subjects-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.text-muted {
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
