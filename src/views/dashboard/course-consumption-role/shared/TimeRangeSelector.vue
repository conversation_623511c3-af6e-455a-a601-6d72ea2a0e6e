<template>
  <div class="time-range-selector">
    <el-form-item label="时间范围">
      <div class="time-range-controls">
        <el-select 
          v-model="timeRangeType" 
          placeholder="选择时间范围"
          style="width: 120px; margin-right: 10px;"
          @change="handleTimeRangeChange"
        >
          <el-option label="最近8周" value="lastEightWeeks" />
          <el-option label="本周" value="thisWeek" />
          <el-option label="上周" value="lastWeek" />
          <el-option label="本月" value="thisMonth" />
          <el-option label="上月" value="lastMonth" />
          <el-option label="自定义" value="custom" />
        </el-select>
        
        <el-date-picker
          v-if="timeRangeType === 'custom'"
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
          style="width: 240px;"
        />
        
        <span v-else class="time-range-display">
          {{ timeRangeDisplay }}
        </span>
      </div>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'TimeRangeSelector',
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        timeRangeType: 'lastEightWeeks',
        startDate: '',
        endDate: ''
      })
    }
  },
  emits: ['update:modelValue', 'change'],
  data() {
    return {
      timeRangeType: 'lastEightWeeks',
      dateRange: []
    }
  },
  computed: {
    timeRangeDisplay() {
      if (this.modelValue.startDate && this.modelValue.endDate) {
        return `${this.modelValue.startDate} 至 ${this.modelValue.endDate}`
      }
      return this.getTimeRangeDescription(this.timeRangeType)
    }
  },
  watch: {
    modelValue: {
      handler(newVal) {
        if (newVal) {
          this.timeRangeType = newVal.timeRangeType || 'lastEightWeeks'
          if (newVal.startDate && newVal.endDate) {
            this.dateRange = [newVal.startDate, newVal.endDate]
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initTimeRange()
  },
  methods: {
    initTimeRange() {
      this.handleTimeRangeChange()
    },
    
    handleTimeRangeChange() {
      const timeRange = this.calculateTimeRange(this.timeRangeType)
      const newValue = {
        timeRangeType: this.timeRangeType,
        startDate: timeRange.startDate,
        endDate: timeRange.endDate
      }
      
      this.$emit('update:modelValue', newValue)
      this.$emit('change', newValue)
    },
    
    handleDateRangeChange() {
      if (this.dateRange && this.dateRange.length === 2) {
        const newValue = {
          timeRangeType: 'custom',
          startDate: this.dateRange[0],
          endDate: this.dateRange[1]
        }
        
        this.$emit('update:modelValue', newValue)
        this.$emit('change', newValue)
      }
    },
    
    calculateTimeRange(type) {
      const now = new Date()
      let startDate, endDate
      
      switch (type) {
        case 'lastEightWeeks':
          // 最近8周：从8周前的周一到今天
          startDate = new Date(now.getTime() - 8 * 7 * 24 * 60 * 60 * 1000)
          startDate = this.getMonday(startDate)
          endDate = now
          break
        case 'thisWeek':
          startDate = this.getMonday(now)
          endDate = now
          break
        case 'lastWeek':
          const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          startDate = this.getMonday(lastWeek)
          endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000)
          break
        case 'thisMonth':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          endDate = now
          break
        case 'lastMonth':
          const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
          startDate = lastMonth
          endDate = new Date(now.getFullYear(), now.getMonth(), 0)
          break
        default:
          // 默认为最近8周
          startDate = new Date(now.getTime() - 8 * 7 * 24 * 60 * 60 * 1000)
          startDate = this.getMonday(startDate)
          endDate = now
      }
      
      return {
        startDate: this.formatDate(startDate),
        endDate: this.formatDate(endDate)
      }
    },
    
    getMonday(date) {
      const day = date.getDay()
      const diff = date.getDate() - day + (day === 0 ? -6 : 1)
      return new Date(date.setDate(diff))
    },
    
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    
    getTimeRangeDescription(type) {
      switch (type) {
        case 'lastEightWeeks':
          return '最近8周'
        case 'thisWeek':
          return '本周'
        case 'lastWeek':
          return '上周'
        case 'thisMonth':
          return '本月'
        case 'lastMonth':
          return '上月'
        default:
          return '自定义'
      }
    }
  }
}
</script>

<style scoped>
.time-range-selector {
  margin-bottom: 16px;
}

.time-range-controls {
  display: flex;
  align-items: center;
}

.time-range-display {
  color: #606266;
  font-size: 14px;
  margin-left: 10px;
}
</style>
