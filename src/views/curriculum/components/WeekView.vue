<template>
  <div class="week-view">
    <!-- 周视图头部 -->
    <div class="week-header">
      <!-- 时间导航 -->
      <div class="week-navigation">
        <el-button 
          :icon="ArrowLeft" 
          @click="goToPrevWeek"
          size="small"
          circle
        />
        <span class="week-range">{{ weekRangeText }}</span>
        <el-button 
          :icon="ArrowRight" 
          @click="goToNextWeek"
          size="small"
          circle
        />
        <el-button 
          type="primary" 
          size="small"
          @click="goToToday"
          :disabled="isCurrentWeek"
        >
          今天
        </el-button>
      </div>
    </div>

    <!-- 周视图网格 -->
    <div class="week-grid" ref="weekGridRef">
      <!-- 时间轴 -->
      <div class="time-column">
        <div class="time-header"></div>
        <div 
          v-for="hour in timeSlots" 
          :key="hour"
          class="time-slot"
          :style="{ height: `${slotHeight}px` }"
        >
          {{ formatHour(hour) }}
        </div>
      </div>

      <!-- 日期列 -->
      <div
        v-for="day in weekDays"
        :key="day.date"
        class="day-column"
        :class="{ 'is-today': day.isToday, 'is-weekend': day.isWeekend }"
      >
        <!-- 日期头部 -->
        <div class="day-header">
          <div class="day-name">{{ day.dayName }}</div>
          <div class="day-date" :class="{ 'is-today': day.isToday }">
            {{ day.dateNumber }}
          </div>
          <div class="day-course-count">
            {{ getDayCourses(day.date).length ?? 0 }}节课
          </div>
        </div>

        <!-- 时间槽 -->
        <div class="time-slots">
          <div 
            v-for="hour in timeSlots" 
            :key="`${day.date}-${hour}`"
            class="time-slot-cell"
            :style="{ height: `${slotHeight}px` }"
            @click="handleSlotClick(day, hour)"
          >
            <!-- 当前时间指示线 -->
            <div 
              v-if="day.isToday && isCurrentHour(hour)"
              class="current-time-line"
              :style="{ top: `${getCurrentTimePosition(hour)}px` }"
            >
              <div class="time-indicator">
                <span class="time-text">{{ currentTimeText }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 课程卡片 -->
        <div class="course-cards">
          <div
            v-for="course in getDayCourses(day.date)"
            :key="course.id"
            class="course-card-wrapper"
            :style="getCourseCardStyle(course)"
          >
            <CourseCard
              :course="course"
              :show-student-name="showStudentName"
              :show-teacher-name="showTeacherName"
              :show-actions="false"
              :is-week-view="true"
              @course-click="handleCourseClick"
              class="week-course-card"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 课程详情弹窗 -->
    <el-dialog
      v-model="courseDialogVisible"
      title="课程详情"
      width="500px"
      :before-close="handleCloseDialog"
    >
      <CourseCard
        v-if="selectedCourse"
        :course="selectedCourse"
        :show-student-name="showStudentName"
        :show-teacher-name="showTeacherName"
        :show-actions="showActions"
        @course-start="handleCourseAction"
        @course-end="handleCourseAction"
        @course-cancel="handleCourseAction"
        @course-reschedule="handleReschedule"
        @course-consume="handleCourseConsume"
      />
    </el-dialog>

    <!-- 调课弹窗 -->
    <RescheduleDialog
      v-if="rescheduleDialogVisible"
      v-model="rescheduleDialogVisible"
      :course="selectedCourse"
      @success="handleRescheduleSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { useCurriculumStore } from '@/stores/curriculum'
import CourseCard from './CourseCard.vue'
import RescheduleDialog from './RescheduleDialog.vue'

const props = defineProps({
  courses: {
    type: Array,
    default: () => []
  },
  showStudentName: {
    type: Boolean,
    default: true
  },
  showTeacherName: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  teacherId: {
    type: [String, Number],
    default: ''
  },
  studentId: {
    type: [String, Number],
    default: ''
  },
  startHour: {
    type: Number,
    default: 6
  },
  endHour: {
    type: Number,
    default: 24
  },
  slotHeight: {
    type: Number,
    default: 60
  }
})

const emit = defineEmits(['week-change', 'slot-click', 'course-action'])

const curriculumStore = useCurriculumStore()
const weekGridRef = ref(null)
const courseDialogVisible = ref(false)
const rescheduleDialogVisible = ref(false)
const selectedCourse = ref(null)
const currentTimeInterval = ref(null)
const currentTime = ref(new Date())

// 计算属性
const currentWeek = computed(() => curriculumStore.selectedDate)

const weekRangeText = computed(() => {
  const startOfWeek = getStartOfWeek(currentWeek.value)
  const endOfWeek = getEndOfWeek(currentWeek.value)
  
  const startMonth = startOfWeek.getMonth() + 1
  const startDay = startOfWeek.getDate()
  const endMonth = endOfWeek.getMonth() + 1
  const endDay = endOfWeek.getDate()
  
  if (startMonth === endMonth) {
    return `${startMonth}月${startDay}日 - ${endDay}日`
  } else {
    return `${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`
  }
})

const isCurrentWeek = computed(() => {
  const now = new Date()
  const currentWeekStart = getStartOfWeek(now)
  const selectedWeekStart = getStartOfWeek(currentWeek.value)
  
  return currentWeekStart.toDateString() === selectedWeekStart.toDateString()
})

const weekDays = computed(() => {
  const startOfWeek = getStartOfWeek(currentWeek.value)
  const days = []
  const today = new Date().toDateString()

  for (let i = 0; i < 7; i++) {
    const date = new Date(startOfWeek)
    date.setDate(startOfWeek.getDate() + i)

    const dayOfWeek = date.getDay()
    // 修正周几映射：0=周日，1=周一，2=周二...6=周六
    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

    days.push({
      date: date.toDateString(),
      dateNumber: date.getDate(),
      dayName: dayNames[dayOfWeek],
      isToday: date.toDateString() === today,
      isWeekend: dayOfWeek === 0 || dayOfWeek === 6
    })
  }

  return days
})

const timeSlots = computed(() => {
  const slots = []
  for (let hour = props.startHour; hour <= props.endHour; hour++) {
    slots.push(hour)
  }
  return slots
})

const currentTimeText = computed(() => {
  return currentTime.value.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
})

// 方法
const getStartOfWeek = (date) => {
  const start = new Date(date)
  const day = start.getDay()
  const diff = start.getDate() - day + (day === 0 ? -6 : 1) // 周一为起始
  start.setDate(diff)
  start.setHours(0, 0, 0, 0)
  return start
}

const getEndOfWeek = (date) => {
  const end = new Date(getStartOfWeek(date))
  end.setDate(end.getDate() + 6)
  end.setHours(23, 59, 59, 999)
  return end
}

const goToPrevWeek = () => {
  const newDate = new Date(currentWeek.value)
  newDate.setDate(newDate.getDate() - 7)
  curriculumStore.setSelectedDate(newDate)
  emit('week-change', newDate)
}

const goToNextWeek = () => {
  const newDate = new Date(currentWeek.value)
  newDate.setDate(newDate.getDate() + 7)
  curriculumStore.setSelectedDate(newDate)
  emit('week-change', newDate)
}

const goToToday = () => {
  const today = new Date()
  curriculumStore.setSelectedDate(today)
  emit('week-change', today)
}

const formatHour = (hour) => {
  return `${hour.toString().padStart(2, '0')}:00`
}

const getDayCourses = (dateString) => {
  return props.courses.filter(course => {
    // 使用更可靠的日期比较方法
    const courseStartTime = new Date(course.startTime)
    const targetDate = new Date(dateString)

    // 比较年月日，忽略时间部分
    const courseYear = courseStartTime.getFullYear()
    const courseMonth = courseStartTime.getMonth()
    const courseDay = courseStartTime.getDate()

    const targetYear = targetDate.getFullYear()
    const targetMonth = targetDate.getMonth()
    const targetDay = targetDate.getDate()

    return courseYear === targetYear &&
           courseMonth === targetMonth &&
           courseDay === targetDay
  })
}

const getCourseCardStyle = (course) => {
  const startTime = new Date(course.startTime)
  const endTime = new Date(course.endTime)
  
  const startHour = startTime.getHours()
  const startMinute = startTime.getMinutes()
  const duration = (endTime - startTime) / (1000 * 60) // 分钟
  
  const topOffset = (startHour - props.startHour) * props.slotHeight + 
                   (startMinute / 60) * props.slotHeight
  const height = (duration / 60) * props.slotHeight
  
  return {
    position: 'absolute',
    top: `${topOffset + 60}px`, // 加上头部高度
    left: '4px',
    right: '4px',
    height: `${Math.max(height - 4, 30)}px`, // 最小高度30px
    zIndex: 10
  }
}

const isCurrentHour = (hour) => {
  const now = currentTime.value
  return now.getHours() === hour
}

const getCurrentTimePosition = (hour) => {
  const now = currentTime.value
  if (now.getHours() !== hour) return 0
  
  const minutes = now.getMinutes()
  return (minutes / 60) * props.slotHeight
}

const handleSlotClick = (day, hour) => {
  const slotTime = new Date(day.date)
  slotTime.setHours(hour, 0, 0, 0)
  
  emit('slot-click', {
    date: day.date,
    hour: hour,
    time: slotTime
  })
}

const handleCourseClick = (course) => {
  selectedCourse.value = course
  courseDialogVisible.value = true
}

const handleCloseDialog = () => {
  courseDialogVisible.value = false
  selectedCourse.value = null
}

const handleCourseAction = (course) => {
  emit('course-action', course)
  courseDialogVisible.value = false
}

const handleReschedule = (course) => {
  courseDialogVisible.value = false
  rescheduleDialogVisible.value = true
}

const handleCourseConsume = (course) => {
  console.log('=== WeekView handleCourseConsume 被调用 ===');
  console.log('接收到课程数据:', course);
  courseDialogVisible.value = false
  console.log('发送 course-action 事件，类型: consume');
  emit('course-action', { type: 'consume', course })
}

const handleRescheduleSuccess = () => {
  rescheduleDialogVisible.value = false
  selectedCourse.value = null
  emit('course-action', null) // 触发数据刷新
}

const updateCurrentTime = () => {
  currentTime.value = new Date()
}

const startTimeUpdate = () => {
  currentTimeInterval.value = setInterval(updateCurrentTime, 60000) // 每分钟更新
}

const stopTimeUpdate = () => {
  if (currentTimeInterval.value) {
    clearInterval(currentTimeInterval.value)
    currentTimeInterval.value = null
  }
}

onMounted(() => {
  startTimeUpdate()
})

onUnmounted(() => {
  stopTimeUpdate()
})
</script>

<style lang="scss" scoped>
.week-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.week-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8f9fa;
}

.week-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  
  .week-range {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    min-width: 180px;
    text-align: center;
  }
}

.week-grid {
  flex: 1;
  display: flex;
  overflow: auto;
  position: relative;
  min-height: 0; /* 允许flex子元素收缩 */

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  &::-webkit-scrollbar-corner {
    background: #f1f1f1;
  }
}

.time-column {
  width: 80px;
  flex-shrink: 0;
  border-right: 1px solid #e5e7eb;
  background: #f9fafb;
  
  .time-header {
    height: 60px;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .time-slot {
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
  }
}

.day-column {
  flex: 1;
  min-width: 140px;
  border-right: 1px solid #e5e7eb;
  position: relative;
  display: flex;
  flex-direction: column;

  &.is-today {
    background: #f0f9ff;
  }

  &.is-weekend {
    background: #fefefe;
  }

  &:last-child {
    border-right: none;
  }
}

.day-header {
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #e5e7eb;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 20;
  padding: 4px;
  
  .day-name {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
  }
  
  .day-date {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 2px 0;
    
    &.is-today {
      color: #3b82f6;
      background: #dbeafe;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .day-course-count {
    font-size: 10px;
    color: #3b82f6;
    background: #eff6ff;
    padding: 1px 4px;
    border-radius: 8px;
    font-weight: 500;
  }
  .day-course-count-0 {
    font-size: 10px;
    color: #7d7f83;
    background: #eff6ff;
    padding: 1px 4px;
    border-radius: 8px;
    font-weight: 500;
  }
}

.time-slots {
  position: relative;
  flex: 1;
}

.time-slot-cell {
  border-bottom: 1px solid #f3f4f6;
  position: relative;
  cursor: pointer;
  
  &:hover {
    background: rgba(59, 130, 246, 0.05);
  }
  
  &:nth-child(even) {
    background: rgba(249, 250, 251, 0.5);
  }
}

.course-cards {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  
  .course-card-wrapper {
    pointer-events: auto;
  }
}

.week-course-card {
  height: 100% !important;
  min-height: 40px !important;
  font-size: 12px;
  padding: 6px 8px !important;
  
  // 隐藏时间和状态标签
  :deep(.course-header) {
    display: none;
  }
  
  :deep(.course-content) {
    margin-bottom: 0;
  }
  
  :deep(.course-actions) {
    display: none; // 在周视图中隐藏操作按钮
  }
  
  :deep(.cancel-reason) {
    display: none; // 隐藏停课原因
  }
}

.current-time-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: #ef4444;
  z-index: 30;
  
  .time-indicator {
    position: absolute;
    left: -2px;
    top: -8px;
    width: 12px;
    height: 12px;
    background: #ef4444;
    border-radius: 50%;
    
    .time-text {
      position: absolute;
      left: 16px;
      top: -6px;
      background: #ef4444;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      white-space: nowrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .week-navigation {
    .week-range {
      font-size: 16px;
      min-width: 160px;
    }
  }
  
  .time-column {
    width: 60px;
    
    .time-slot {
      font-size: 10px;
    }
  }
  
  .day-column {
    min-width: 100px;
  }
  
  .day-header {
    .day-name {
      font-size: 10px;
    }
    
    .day-date {
      font-size: 14px;
    }
  }
}
</style>