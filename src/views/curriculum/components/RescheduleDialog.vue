<template>
  <el-dialog
    v-model="visible"
    title="调课"
    width="90%"
    :before-close="handleClose"
    class="reschedule-dialog"
    :close-on-click-modal="false"
    style="max-width: 600px"
  >
    <div class="reschedule-content">
      <!-- 当前课程信息 -->
      <div class="current-course-info">
        <h4 class="section-title">当前课程信息</h4>
        <div class="course-info-grid">
          <div class="info-item">
            <span class="label">学生：</span>
            <span class="value">{{ course.studentName }}</span>
          </div>
          <div class="info-item">
            <span class="label">老师：</span>
            <span class="value">{{ course.teacherName }}</span>
          </div>
          <div class="info-item">
            <span class="label">科目：</span>
            <span class="value">{{ course.subject || '英语' }}</span>
          </div>
          <div class="info-item">
            <span class="label">规格：</span>
            <span class="value">{{ course.specification || '单词课' }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前时间：</span>
            <span class="value">{{ formatDateTime(course.startTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">时长：</span>
            <span class="value">{{ course.duration }}分钟</span>
          </div>
        </div>
      </div>

      <!-- 调课表单 -->
      <div class="reschedule-form">
        <h4 class="section-title">调课到</h4>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="80px"
          class="reschedule-form-inner"
        >
          <el-row :gutter="16">
            <el-col :xs="24" :sm="12">
              <el-form-item label="日期" prop="date">
                <el-date-picker
                  v-model="form.date"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="开始时间" prop="startTime">
                <el-select
                  v-model="form.startTime"
                  placeholder="选择开始时间"
                  filterable
                  style="width: 100%"
                  @change="updateEndTime"
                >
                  <el-option
                    v-for="time in timeOptions"
                    :key="time.value"
                    :label="time.label"
                    :value="time.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :xs="24" :sm="12">
              <el-form-item label="时长">
                <el-input
                  :value="course.duration + '分钟'"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="结束时间">
                <el-input
                  :value="form.endTime"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="调课类型" prop="rescheduleType">
            <el-radio-group v-model="form.rescheduleType">
              <el-radio label="teacher">老师原因</el-radio>
              <el-radio label="student">学生原因</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="调课原因" prop="reason">
            <el-input
              v-model="form.reason"
              type="textarea"
              :rows="3"
              placeholder="请说明调课原因..."
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          确认调课
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useCurriculumStore } from '@/stores/curriculum'

// 生成时间选项（从06:00到24:00，每5分钟一个档）
const generateTimeOptions = () => {
  const options = [];
  for (let hour = 6; hour <= 24; hour++) {
    const maxMinute = hour === 24 ? 0 : 60; // 24点只显示00分
    for (let minute = 0; minute < maxMinute; minute += 5) {
      const timeStr = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;
      options.push({
        label: timeStr,
        value: timeStr,
      });
    }
  }
  return options;
};

const timeOptions = generateTimeOptions();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  course: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const curriculumStore = useCurriculumStore()
const formRef = ref()
const submitting = ref(false)

// 表单数据
const form = ref({
  date: '',
  startTime: '',
  endTime: '',
  rescheduleType: 'teacher',
  reason: ''
})

// 表单验证规则
const rules = {
  date: [
    { required: true, message: '请选择日期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  rescheduleType: [
    { required: true, message: '请选择调课类型', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请填写调课原因', trigger: 'blur' },
    { min: 5, message: '调课原因至少5个字符', trigger: 'blur' }
  ]
}

// 控制对话框显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 格式化日期时间
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return ''
  const date = new Date(dateTimeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 更新结束时间
const updateEndTime = () => {
  if (!form.value.startTime || !props.course.duration) {
    form.value.endTime = ''
    return
  }

  const [hours, minutes] = form.value.startTime.split(':').map(Number)
  const startDate = new Date()
  startDate.setHours(hours, minutes, 0, 0)

  const endDate = new Date(startDate.getTime() + props.course.duration * 60 * 1000)
  
  // 检查是否跨越了午夜
  if (endDate.getDate() !== startDate.getDate()) {
    form.value.endTime = ''
    ElMessage.warning('课程时长过长，结束时间不能跨越午夜')
    return
  }

  const endHours = endDate.getHours().toString().padStart(2, '0')
  const endMinutes = endDate.getMinutes().toString().padStart(2, '0')
  form.value.endTime = `${endHours}:${endMinutes}`
}

// 监听开始时间变化
watch(() => form.value.startTime, updateEndTime)

// 将时间调整到最近的5分钟间隔
const roundToNearestFiveMinutes = (hours, minutes) => {
  // 将分钟数调整到最近的5分钟倍数
  const roundedMinutes = Math.round(minutes / 5) * 5

  // 如果分钟数达到60，需要进位到下一小时
  if (roundedMinutes >= 60) {
    return {
      hours: (hours + 1) % 24,
      minutes: 0
    }
  }

  return {
    hours: hours,
    minutes: roundedMinutes
  }
}

// 监听对话框显示状态，设置默认值
watch(() => props.modelValue, (newVisible) => {
  if (newVisible && props.course && props.course.startTime) {
    // 从原始的 datetime 格式中提取时间部分
    const originalDateTime = new Date(props.course.startTime)
    const originalHours = originalDateTime.getHours()
    const originalMinutes = originalDateTime.getMinutes()

    // 调整到最近的5分钟间隔
    const { hours, minutes } = roundToNearestFiveMinutes(originalHours, originalMinutes)

    // 转换为 HH:mm 格式
    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`

    // 确保时间在可选范围内（06:00-24:00）
    if (timeStr >= '06:00' && timeStr <= '24:00') {
      form.value.startTime = timeStr
    } else {
      // 如果超出范围，设置为默认时间
      form.value.startTime = '09:00'
    }

    // 设置默认值后更新结束时间
    updateEndTime()
  }
})

// 提交调课
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const rescheduleData = {
      courseId: props.course.id,
      newDate: form.value.date,
      newStartTime: form.value.startTime,
      newEndTime: form.value.endTime,
      rescheduleType: form.value.rescheduleType,
      reason: form.value.reason
    }

    const success = await curriculumStore.rescheduleCourse(rescheduleData)
    
    if (success) {
      ElMessage.success('调课成功')
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('调课失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  // 重置表单
  form.value = {
    date: '',
    startTime: '',
    endTime: '',
    rescheduleType: 'teacher',
    reason: ''
  }
  formRef.value?.clearValidate()
}
</script>

<style lang="scss" scoped>
.reschedule-dialog {
  :deep(.el-dialog) {
    overflow: hidden;
  }

  :deep(.el-dialog__body) {
    padding: 16px 20px;
    overflow-x: hidden;
  }

  .reschedule-content {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
  }

  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    border-bottom: 2px solid #409eff;
    padding-bottom: 8px;
  }

  .current-course-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #409eff;
  }

  .course-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }

  .info-item {
    display: flex;
    align-items: center;

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 60px;
    }

    .value {
      color: #303133;
      font-weight: 500;
    }
  }

  .reschedule-form {
    .section-title {
      border-bottom-color: #67c23a;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    width: 100%;
    box-sizing: border-box;
  }

  // 确保所有元素不会溢出
  * {
    box-sizing: border-box;
  }

  .el-row {
    width: 100%;
  }

  .el-col {
    width: 100%;
  }

  :deep(.el-form-item) {
    width: 100%;
    margin-bottom: 18px;
  }

  :deep(.el-input),
  :deep(.el-date-picker),
  :deep(.el-select),
  :deep(.el-textarea) {
    width: 100% !important;
    max-width: 100%;
  }

  :deep(.el-input__inner),
  :deep(.el-textarea__inner) {
    width: 100%;
    max-width: 100%;
  }

  // 调课类型单选按钮样式
  :deep(.el-radio-group) {
    display: flex;
    gap: 16px;

    .el-radio {
      margin-right: 0;

      .el-radio__label {
        font-size: 14px;
        color: #606266;
      }

      &.is-checked .el-radio__label {
        color: #409eff;
        font-weight: 500;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .reschedule-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    .reschedule-content {
      padding: 0 8px;
    }

    .course-info-grid {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .current-course-info {
      padding: 12px;
      margin-bottom: 16px;
    }

    .section-title {
      font-size: 14px;
      margin-bottom: 12px;
    }

    .info-item {
      .label {
        min-width: 50px;
        font-size: 14px;
      }

      .value {
        font-size: 14px;
      }
    }

    :deep(.el-form-item__label) {
      font-size: 14px;
    }

    :deep(.el-input__inner) {
      font-size: 14px;
    }

    .dialog-footer {
      padding: 0 8px;

      .el-button {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .reschedule-dialog {
    :deep(.el-dialog) {
      width: 98% !important;
      margin: 2vh auto !important;
    }

    :deep(.el-dialog__body) {
      padding: 12px 16px;
    }

    .reschedule-content {
      padding: 0;
    }

    .current-course-info {
      padding: 8px;
      margin-bottom: 12px;
    }

    .section-title {
      font-size: 13px;
      margin-bottom: 8px;
    }

    .info-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;

      .label {
        min-width: auto;
        font-size: 13px;
        font-weight: 600;
      }

      .value {
        font-size: 13px;
        padding-left: 8px;
      }
    }

    .reschedule-form-inner {
      :deep(.el-form-item) {
        margin-bottom: 16px;
      }

      :deep(.el-form-item__label) {
        font-size: 13px;
        line-height: 1.2;
        width: 70px !important;
      }
    }

    :deep(.el-input__inner) {
      font-size: 13px;
      padding: 8px 12px;
    }

    :deep(.el-textarea__inner) {
      font-size: 13px;
      padding: 8px 12px;
    }

    .dialog-footer {
      padding: 0;
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 100%;
        font-size: 14px;
      }
    }
  }
}

// 超小屏幕优化
@media (max-width: 360px) {
  .reschedule-dialog {
    :deep(.el-dialog__body) {
      padding: 8px 12px;
    }

    .reschedule-form-inner {
      :deep(.el-form-item__label) {
        width: 60px !important;
        font-size: 12px;
      }
    }

    .current-course-info {
      padding: 6px;
    }

    .section-title {
      font-size: 12px;
    }

    .info-item {
      .label, .value {
        font-size: 12px;
      }
    }
  }
}
</style>
