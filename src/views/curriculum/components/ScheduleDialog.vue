<template>
  <el-dialog
    v-model="visible"
    title="排课设置"
    width="800px"
    :before-close="handleClose"
    class="schedule-dialog"
    top="5vh"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="90px"
      class="schedule-form"
    >
      <!-- 教师和学生同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="选择教师" prop="teacherId" label-width="80px">
              <!-- 当有完整教师信息时，显示教师姓名且不可修改 -->
              <el-input
                v-if="hasCompleteTeacherInfo"
                :value="props.teacherName"
                readonly
                class="teacher-display"
                placeholder="教师姓名"
              >
                <template #suffix>
                  <el-icon class="lock-icon"><Lock /></el-icon>
                </template>
              </el-input>
              <!-- 否则显示教师选择下拉框 -->
              <el-select
                v-else
                v-model="form.teacherId"
                placeholder="请选择教师"
                filterable
                remote
                :remote-method="searchTeachers"
                :loading="teachersLoading"
                class="teacher-select"
                clearable
                :disabled="teacherIdFromProps && !canChangeTeacher"
                @change="onTeacherChange"
              >
                <el-option
                  v-for="teacher in teachers"
                  :key="teacher.id"
                  :label="`${teacher.name}`"
                  :value="teacher.id"
                >
                  <div class="teacher-option">
                    <span class="teacher-name">{{ teacher.name }}</span>
                    <!-- <span class="teacher-phone">({{ teacher.phone }})</span> -->
                  </div>
                </el-option>
              </el-select>
              <!-- <div v-if="teacherIdFromProps && !canChangeTeacher" class="teacher-locked-tip">
                <el-text type="info" size="small">教师已锁定，不可修改</el-text>
              </div> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择学生" prop="studentId" label-width="80px">
              <!-- 当有完整学生信息时，显示学生姓名且不可修改 -->
              <el-input
                v-if="hasCompleteStudentInfo"
                :value="props.studentName"
                readonly
                class="student-display"
                placeholder="学生姓名"
              >
                <template #suffix>
                  <el-icon class="lock-icon"><Lock /></el-icon>
                </template>
              </el-input>
              <!-- 否则显示学生选择下拉框 -->
              <template v-else>
                <el-select
                  v-model="form.studentId"
                  placeholder="请先选择教师"
                  filterable
                  remote
                  :remote-method="searchStudents"
                  :loading="studentsLoading"
                  class="student-select"
                  clearable
                  :disabled="!form.teacherId && !studentIdFromProps"
                >
                  <el-option
                    v-for="student in students"
                    :key="student.id"
                    :label="`${student.name}（${student.phone}）`"
                    :value="student.id"
                  >
                    <div class="student-option">
                      <span class="student-name">{{ student.name }}</span>
                      <span class="student-phone">({{ student.phone }})</span>
                    </div>
                  </el-option>
                </el-select>
                <div v-if="!form.teacherId" class="student-disabled-tip">
                  <el-text type="info" size="small">请先选择教师后再选择学生</el-text>
                </div>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 教师可排课时间段提示 -->
      <el-form-item v-if="false && teacherAvailableTimeSlots" label="可排课时间" class="compact-form-item">
        <div class="teacher-available-time compact">
          <div v-if="teacherAvailableTimeSlots.availableTimeSlots.length === 0" class="no-available-time">
            <el-text type="warning" size="small">该教师暂无可排课时间段</el-text>
          </div>
          <div v-else class="available-time-list compact">
            <span
              v-for="slot in teacherAvailableTimeSlots.availableTimeSlots"
              :key="slot.weekday"
              class="weekday-slot compact"
            >
              <span class="weekday-name">{{ getWeekdayName(slot.weekday) }}：</span>
              <span v-if="slot.timeSlots.length === 0" class="no-time">无</span>
              <span v-else class="time-slots">
                <span
                  v-for="(timeSlot, index) in slot.timeSlots"
                  :key="index"
                  class="time-slot"
                >
                  {{ timeSlot.startTime }}-{{ timeSlot.endTime }}
                </span>
              </span>
            </span>
          </div>
        </div>
      </el-form-item>

      <!-- 学科和规格同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="选择学科" prop="subject" label-width="80px">
              <el-select
                v-model="form.subject"
                placeholder="请选择学科"
                class="subject-select"
              >
                <el-option
                  v-for="subject in subjectOptions"
                  :key="subject.value"
                  :label="subject.label"
                  :value="subject.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择规格" prop="specification" label-width="80px">
              <el-select
                v-model="form.specification"
                placeholder="请选择规格"
                class="specification-select"
              >
                <el-option
                  v-for="spec in specificationOptions"
                  :key="spec.value"
                  :label="spec.label"
                  :value="spec.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 课程类型和课程性质同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="课程类型" prop="type" label-width="80px">
              <el-select
                v-model="form.type"
                placeholder="请选择课程类型"
                class="type-select"
              >
                <el-option label="学习课" value="学习课" />
                <el-option label="复习课" value="复习课" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程性质" prop="courseType" label-width="80px">
              <el-select
                v-model="form.courseType"
                placeholder="请选择课程性质"
                class="course-type-select"
              >
                <el-option label="正式课" value="正式课" />
                <el-option label="试听课" value="试听课" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 使用系统 -->
      <el-form-item v-if="false" label="使用系统" prop="useSystem">
        <el-radio-group v-model="form.useSystem">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 课时和开始日期同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="总课时数" prop="totalLessons" label-width="80px">
              <el-input-number
                v-model="form.totalLessons"
                :min="1"
                :max="999"
                placeholder="请输入课时"
                class="lessons-input"
                @change="calculateEndDate"
              />
              <span class="unit-text">节</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始日期" prop="startDate" label-width="80px">
              <el-date-picker
                v-model="form.startDate"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                class="start-date-picker"
                @change="handleStartDateChange"
                @input="handleStartDateChange"
                @update:modelValue="handleStartDateChange"
              />

            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 结束日期（只读显示） -->
      <el-form-item label="结束日期" v-if="form.endDate" class="compact-form-item">
        <div class="end-date-display">
          <el-tag type="info" size="small">{{ form.endDate }}</el-tag>
          <span class="date-info">(共{{ totalWeeks }}周)</span>
        </div>
      </el-form-item>

      <!-- 周时间安排 -->
      <el-form-item label="时间安排" prop="weeklySchedules">
        <div class="weekly-schedules">
          <div class="schedule-header compact">
            <span class="header-title">上课时间</span>
            <el-button
              type="primary"
              size="small"
              :icon="Plus"
              @click="addWeeklySchedule"
              :disabled="!canAddTimeSlot"
            >
              添加
            </el-button>
          </div>

          <div
            v-for="(schedule, index) in form.weeklySchedules"
            :key="index"
            class="schedule-item"
          >
            <el-row :gutter="8" align="middle" class="schedule-row">
              <el-col :span="4">
                <el-select
                  v-model="schedule.dayOfWeek"
                  placeholder="星期"
                  size="small"
                  @change="onDayOfWeekChange(schedule)"
                >
                  <el-option
                    v-for="day in getAvailableWeekDays()"
                    :key="day.value"
                    :label="day.label"
                    :value="day.value"
                  />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="schedule.duration"
                  placeholder="时长"
                  @change="onDurationChange(schedule)"
                  size="small"
                  class="duration-select"
                >
                  <el-option
                    v-for="duration in durationOptions"
                    :key="duration.value"
                    :label="duration.label"
                    :value="duration.value"
                  />
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="schedule.startTime"
                  placeholder="开始时间"
                  filterable
                  size="small"
                  @change="updateEndTime(schedule)"
                >
                  <el-option
                    v-for="time in getAvailableStartTimeOptions(schedule.dayOfWeek, schedule.duration)"
                    :key="time.value"
                    :label="time.label"
                    :value="time.value"
                  />
                </el-select>
              </el-col>
              <el-col :span="5" class="end-time-display">
                <span class="time-range">～ {{ schedule.endTime || '--:--' }}</span>
              </el-col>
              <el-col :span="2" class="time-actions">
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  @click="removeWeeklySchedule(index)"
                  circle
                  v-if="form.weeklySchedules.length > 1"
                  class="delete-btn"
                />
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form-item>

      <!-- 预览信息 -->
      <el-form-item label="排课预览" v-if="previewData.length > 0" class="compact-form-item">
        <div class="preview-container compact">
          <div class="preview-summary">
            <el-tag type="success" size="small">
              共 {{ previewData.length }} 节课程
            </el-tag>
            <el-button
              type="text"
              size="small"
              @click="showPreviewDetail = !showPreviewDetail"
              class="toggle-detail-btn"
            >
              {{ showPreviewDetail ? "收起" : "查看" }}日历
            </el-button>
          </div>

          <div v-if="showPreviewDetail" class="preview-detail compact">
            <!-- 紧凑日历月视图 -->
            <div class="calendar-container compact">
              <!-- 月份导航 -->
              <div class="calendar-nav compact">
                <el-button
                  type="text"
                  size="small"
                  @click="prevMonth"
                  :disabled="!canGoPrevMonth"
                >
                  ← 上月
                </el-button>
                <span class="current-month">{{ formatCurrentMonth }}</span>
                <el-button
                  type="text"
                  size="small"
                  @click="nextMonth"
                  :disabled="!canGoNextMonth"
                >
                  下月 →
                </el-button>
              </div>

              <!-- 星期标题 -->
              <div class="calendar-header compact">
                <div class="weekday-header" v-for="day in weekDayHeaders" :key="day">
                  {{ day }}
                </div>
              </div>

              <!-- 日历网格 -->
              <div class="calendar-grid compact">
                <div
                  v-for="(day, index) in calendarDays"
                  :key="index"
                  :class="getDayClass(day)"
                  class="calendar-day compact"
                >
                  <div class="day-number">{{ day.day }}</div>
                  <div v-if="day.courses.length > 0" class="day-courses">
                    <div
                      v-for="(course, courseIndex) in day.courses"
                      :key="courseIndex"
                      class="course-item compact"
                      :title="`${course.startTime}-${course.endTime}`"
                    >
                      {{ course.startTime.substring(0, 5) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="previewData.length === 0"
        >
          确定排课（{{ previewData.length }}节）
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Plus, Delete, Lock } from "@element-plus/icons-vue";
import { useCurriculumStore } from "@/stores/curriculum";
import useUserStore from "@/store/modules/user";

// 生成时间选项（从06:00到24:00，每5分钟一个档）
const generateTimeOptions = () => {
  const options = [];
  for (let hour = 6; hour <= 24; hour++) {
    const maxMinute = hour === 24 ? 0 : 60; // 24点只显示00分
    for (let minute = 0; minute < maxMinute; minute += 5) {
      const timeStr = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;
      options.push({
        label: timeStr,
        value: timeStr,
      });
    }
  }
  return options;
};

const timeOptions = generateTimeOptions();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  teacherId: {
    type: String,
    default: null,
  },
  teacherName: {
    type: String,
    default: null,
  },
  studentId: {
    type: String,
    default: null,
  },
  studentName: {
    type: String,
    default: null,
  },
  startDate: {
    type: String,
    default: null,
  },
  timeSlots: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["update:modelValue", "success"]);

const curriculumStore = useCurriculumStore();
const userStore = useUserStore();

const formRef = ref(null);
const submitting = ref(false);
const studentsLoading = ref(false);
const teachersLoading = ref(false);
const showPreviewDetail = ref(false);
const currentMonth = ref(new Date());

// 星期标题
const weekDayHeaders = ["日", "一", "二", "三", "四", "五", "六"];

// 表单数据
const form = ref({
  teacherId: "",
  studentId: "",
  subject: "英语",
  type: "学习课",
  courseType: "正式课", // 课程性质，默认为正式课
  useSystem: true, // 使用系统，默认为是
  specification: "单词课", // 更新为新的默认规格
  totalLessons: 10, // 总课时数
  startDate: "", // 开始日期
  endDate: "", // 结束日期（自动计算）
  dateRange: [], // 保留兼容性
  weeklySchedules: [
    {
      dayOfWeek: 1, // 周一
      startTime: "09:00",
      endTime: "10:00",
      duration: 60, // 默认60分钟
    },
  ],
  duration: 60,
});

// 教师和学生列表
const teachers = ref([]);
const students = ref([]);
const teacherAvailableTimeSlots = ref(null);

// 是否从props传入教师ID（外部指定）
const teacherIdFromProps = computed(() => props.teacherId);
const canChangeTeacher = computed(() => !teacherIdFromProps.value);

// 从props获取预填充数据
const studentIdFromProps = computed(() => props.studentId);
const startDateFromProps = computed(() => props.startDate);
const timeSlotsFromProps = computed(() => props.timeSlots);

// 判断是否有完整的教师和学生信息（ID + 姓名）
const hasCompleteTeacherInfo = computed(() => {
  return props.teacherId && props.teacherName;
});
const hasCompleteStudentInfo = computed(() => {
  return props.studentId && props.studentName;
});

// 学科选项
const subjectOptions = [
  { label: "英语", value: "英语" },
  { label: "语文", value: "语文" },
  { label: "数学", value: "数学" },
  { label: "物理", value: "物理" },
  { label: "化学", value: "化学" },
];

// 所有课型选项
const allSpecificationOptions = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
];

// 英语学科的课型选项
const englishSpecificationOptions = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
];

// 其他学科的课型选项
const otherSubjectSpecificationOptions = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
];

// 当前可用的课型选项
const specificationOptions = ref(allSpecificationOptions);

// 时长选项
const durationOptions = [
  { label: "60分钟", value: 60 },
  { label: "90分钟", value: 90 },
  { label: "120分钟", value: 120 },
  { label: "25分钟", value: 25 },
  { label: "20分钟", value: 20 },
  { label: "15分钟", value: 15 },
];

// 星期选项
const weekDays = [
  { label: "周日", value: 7 },
  { label: "周一", value: 1 },
  { label: "周二", value: 2 },
  { label: "周三", value: 3 },
  { label: "周四", value: 4 },
  { label: "周五", value: 5 },
  { label: "周六", value: 6 },
];

// 表单验证规则
const rules = {
  teacherId: [{ required: true, message: "请选择教师", trigger: "change" }],
  studentId: [{ required: true, message: "请选择学生", trigger: "change" }],
  subject: [{ required: true, message: "请选择课程科目", trigger: "change" }],
  specification: [{ required: true, message: "请选择课程规格", trigger: "change" }],
  type: [{ required: true, message: "请选择课程类型", trigger: "change" }],
  courseType: [{ required: true, message: "请选择课程性质", trigger: "change" }],
  useSystem: [{ required: true, message: "请选择是否使用系统", trigger: "change" }],
  totalLessons: [
    { required: true, message: "请输入总课时数", trigger: "blur" },
    {
      type: "number",
      min: 1,
      max: 999,
      message: "课时数必须在1-999之间",
      trigger: "blur",
    },
  ],
  startDate: [{ required: true, message: "请选择开始日期", trigger: "change" }],
  weeklySchedules: [{ required: true, message: "请设置上课时间", trigger: "change" }],
  duration: [{ required: true, message: "请设置课程时长", trigger: "change" }],
};

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 计算总周数
const totalWeeks = computed(() => {
  if (!form.value.startDate || !form.value.endDate) return 0;

  const start = new Date(form.value.startDate);
  const end = new Date(form.value.endDate);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.ceil(diffDays / 7);
});

// 处理开始日期变化
const handleStartDateChange = (newDate) => {
  // 更新日历月份显示
  if (newDate) {
    const startDate = new Date(newDate);
    currentMonth.value = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
  }

  // 计算结束日期
  calculateEndDate();
};

// 计算结束日期
const calculateEndDate = () => {
  if (
    !form.value.startDate ||
    !form.value.totalLessons ||
    form.value.weeklySchedules.length === 0
  ) {
    form.value.endDate = "";
    form.value.dateRange = [];
    return;
  }

  // 计算每周课程数
  const validSchedules = form.value.weeklySchedules.filter(
    (s) => s.dayOfWeek !== undefined && s.startTime && s.endTime
  );
  if (validSchedules.length === 0) {
    form.value.endDate = "";
    form.value.dateRange = [];
    return;
  }

  // 模拟生成课程来找到真实的结束日期
  const startDate = new Date(form.value.startDate);
  const maxLessons = form.value.totalLessons;
  let generatedLessons = 0;
  let lastCourseDate = startDate;

  // 设置一个合理的搜索范围（最多搜索一年）
  const maxSearchDays = 365;

  for (let dayOffset = 0; dayOffset < maxSearchDays && generatedLessons < maxLessons; dayOffset++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + dayOffset);
    const dayOfWeek = currentDate.getDay();

    // 转换JavaScript的星期几格式(0-6，周日=0)到系统格式(1-7，周日=7)
    const systemDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;

    // 找到当天对应的时间安排
    const todaySchedules = validSchedules.filter(
      schedule => schedule.dayOfWeek === systemDayOfWeek
    );

    for (const schedule of todaySchedules) {
      if (generatedLessons >= maxLessons) {
        break;
      }
      generatedLessons++;
      lastCourseDate = new Date(currentDate.getTime()); // 创建独立的日期副本
    }
  }

  form.value.endDate = lastCourseDate.toISOString().split("T")[0];
  form.value.dateRange = [form.value.startDate, form.value.endDate];
};

// 生成排课预览数据
const previewData = computed(() => {
  if (
    !form.value.dateRange ||
    form.value.dateRange.length !== 2 ||
    form.value.weeklySchedules.length === 0 ||
    !form.value.totalLessons
  ) {
    return [];
  }

  const [startDate, endDate] = form.value.dateRange;
  const start = new Date(startDate);
  const end = new Date(endDate);
  const schedules = [];
  const maxLessons = form.value.totalLessons; // 总课时数限制

  // 先过滤出有效的时间安排
  const validSchedules = form.value.weeklySchedules.filter(
    (schedule) => {
      return schedule.dayOfWeek !== undefined &&
             schedule.dayOfWeek !== null &&
             schedule.startTime &&
             schedule.endTime;
    }
  );

  // 遍历日期范围内的每一天
  for (
    let current = new Date(start);
    current <= end && schedules.length < maxLessons;
    current.setDate(current.getDate() + 1)
  ) {
    const dayOfWeek = current.getDay();

    // 转换JavaScript的星期几格式(0-6，周日=0)到系统格式(1-7，周日=7)
    const systemDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;

    // 找到当天对应的时间安排
    const todaySchedules = validSchedules.filter(
      (schedule) => schedule.dayOfWeek === systemDayOfWeek
    );

    for (const schedule of todaySchedules) {
      if (schedules.length >= maxLessons) {
        break; // 达到课时数限制，停止添加
      }

      const courseItem = {
        date: new Date(current.getTime()), // 创建独立的日期副本
        dayOfWeek: systemDayOfWeek, // 使用系统格式的星期几
        startTime: schedule.startTime,
        endTime: schedule.endTime,
      };
      
      schedules.push(courseItem);
    }
  }

  return schedules.sort((a, b) => a.date - b.date);
});

// 当前月份格式化显示
const formatCurrentMonth = computed(() => {
  return currentMonth.value.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
  });
});

// 获取指定日期的课程
const getCoursesForDate = (date) => {
  return previewData.value.filter((course) => {
    const courseDate = new Date(course.date);
    return courseDate.toDateString() === date.toDateString();
  });
};

// 生成日历网格数据
const calendarDays = computed(() => {
  const year = currentMonth.value.getFullYear();
  const month = currentMonth.value.getMonth();

  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  // 获取第一天是星期几（0-6，0是周日）
  const startWeekDay = firstDay.getDay();

  const days = [];

  // 填充上个月的日期
  if (startWeekDay > 0) {
    // 获取上个月的年份和月份
    const prevYear = month === 0 ? year - 1 : year;
    const prevMonthIndex = month === 0 ? 11 : month - 1;
    const prevMonthLastDay = new Date(prevYear, prevMonthIndex + 1, 0);
    const prevMonthLastDate = prevMonthLastDay.getDate();
    
    for (let i = startWeekDay - 1; i >= 0; i--) {
      const day = prevMonthLastDate - i;
      const date = new Date(prevYear, prevMonthIndex, day);
      days.push({
        day,
        date: new Date(date.getTime()), // 创建独立副本
        isCurrentMonth: false,
        isPrevMonth: true,
        isNextMonth: false,
        courses: getCoursesForDate(date),
      });
    }
  }

  // 填充当月的日期
  const currentMonthDays = lastDay.getDate();
  for (let day = 1; day <= currentMonthDays; day++) {
    const date = new Date(year, month, day);
    days.push({
      day,
      date: new Date(date.getTime()), // 创建独立副本
      isCurrentMonth: true,
      isPrevMonth: false,
      isNextMonth: false,
      courses: getCoursesForDate(date),
    });
  }

  // 计算需要多少下个月的日期来填满当前行
  const currentLength = days.length;
  const remainder = currentLength % 7;
  const needNextMonthDays = remainder === 0 ? 0 : 7 - remainder;

  // 填充下个月的日期（只填满当前行）
  if (needNextMonthDays > 0) {
    const nextYear = month === 11 ? year + 1 : year;
    const nextMonthIndex = month === 11 ? 0 : month + 1;
    
    for (let day = 1; day <= needNextMonthDays; day++) {
      const date = new Date(nextYear, nextMonthIndex, day);
      days.push({
        day,
        date: new Date(date.getTime()), // 创建独立副本
        isCurrentMonth: false,
        isPrevMonth: false,
        isNextMonth: true,
        courses: getCoursesForDate(date),
      });
    }
  }

  // 现在检查最后一行是否全部是下个月的日期
  const totalDays = days.length;
  const lastRowStart = Math.floor((totalDays - 1) / 7) * 7;
  const lastRowDays = days.slice(lastRowStart);
  const lastRowAllNextMonth = lastRowDays.every(d => d.isNextMonth);

  // 如果最后一行全部是下个月的日期，就移除这一行
  if (lastRowAllNextMonth && lastRowDays.length === 7) {
    days.splice(lastRowStart, 7);
  }

  return days;
});

// 检查是否可以切换到上个月
const canGoPrevMonth = computed(() => {
  if (!form.value.dateRange || form.value.dateRange.length !== 2) return true;

  const startDate = new Date(form.value.dateRange[0]);
  const prevMonth = new Date(
    currentMonth.value.getFullYear(),
    currentMonth.value.getMonth() - 1,
    1
  );

  return prevMonth >= new Date(startDate.getFullYear(), startDate.getMonth(), 1);
});

// 检查是否可以切换到下个月
const canGoNextMonth = computed(() => {
  if (!form.value.dateRange || form.value.dateRange.length !== 2) return true;

  const endDate = new Date(form.value.dateRange[1]);
  const nextMonth = new Date(
    currentMonth.value.getFullYear(),
    currentMonth.value.getMonth() + 1,
    1
  );

  return nextMonth <= new Date(endDate.getFullYear(), endDate.getMonth(), 1);
});

// 是否可以添加时间段
const canAddTimeSlot = computed(() => {
  // 如果没有选择教师或没有可排课时间段限制，允许添加
  if (!teacherAvailableTimeSlots.value) {
    return true;
  }

  // 检查是否有任何可排课时间段
  const hasAvailableTime = teacherAvailableTimeSlots.value.availableTimeSlots.some(slot =>
    slot.timeSlots && slot.timeSlots.length > 0
  );

  return hasAvailableTime;
});

// 方法
const searchTeachers = async (query) => {
  teachersLoading.value = true;
  try {
    await curriculumStore.fetchTeachers({ keyword: query || '' });
    teachers.value = curriculumStore.teachers;
    
    // 如果没有传入teacherId且教师列表只有一个教师，自动选中这个教师
    if (!teacherIdFromProps.value && teachers.value && teachers.value.length === 1) {
      const singleTeacher = teachers.value[0];
      form.value.teacherId = singleTeacher.id;
      await onTeacherChange(singleTeacher.id);
    }
  } finally {
    teachersLoading.value = false;
  }
};

// 加载教师列表并验证传入的teacherId是否存在
const loadAndValidateTeacher = async (teacherId) => {
  teachersLoading.value = true;

  try {
    // 先加载所有教师列表
    await curriculumStore.fetchTeachers({ keyword: '' });
    teachers.value = curriculumStore.teachers;

    // 验证传入的teacherId是否在列表中存在（处理类型转换）
    const targetTeacher = teachers.value?.find(teacher => {
      const teacherIdStr = String(teacher.id);
      const targetIdStr = String(teacherId);
      return teacherIdStr === targetIdStr;
    });

    if (targetTeacher) {
      // 设置教师ID（确保类型与选项value一致）并获取可排课时间段
      form.value.teacherId = targetTeacher.id; // 使用找到的教师的ID，确保类型一致
      await onTeacherChange(targetTeacher.id);
    } else {
      // 显示错误提示
      console.error('教师验证失败:', {
        teacherId,
        teachersList: teachers.value,
        teacherIds: teachers.value?.map(t => ({ id: t.id, type: typeof t.id }))
      });
      ElMessage.error({
        message: `无法操作ID为${teacherId}的老师`,
        duration: 3000,
        onClose: () => {
          handleClose();
        }
      });
    }
  } catch (error) {
    console.error("加载教师列表失败:", error);
    ElMessage.error({
      message: "加载教师列表失败",
      duration: 3000,
      onClose: () => {
        handleClose();
      }
    });
  } finally {
    teachersLoading.value = false;
  }
};

const searchStudents = async (query) => {
  // 如果没有选择教师，不请求学生列表
  if (!form.value.teacherId) {
    // 只有在用户主动搜索时才显示警告，自动调用时不显示
    if (query) {
      ElMessage.warning('请先选择教师');
    }
    return;
  }

  // 当有教师ID时，无论是否有query都可以请求学生列表
  studentsLoading.value = true;
  try {
    // 传递教师ID和关键词获取学生列表
    await curriculumStore.fetchStudents({
      keyword: query || '',
      teacherId: form.value.teacherId
    });
    students.value = curriculumStore.students;
  } finally {
    studentsLoading.value = false;
  }
};

// 教师变更时的处理
const onTeacherChange = async (teacherId) => {
  if (!teacherId) {
    teacherAvailableTimeSlots.value = null;
    // 清空学生选择和学生列表
    form.value.studentId = '';
    students.value = [];
    return;
  }

  try {
    // 获取教师可排课时间段
    // const timeSlots = await curriculumStore.fetchTeacherAvailableTimeSlots(teacherId);
    const timeSlots = {availableTimeSlots: []};
    for(var i=1; i<=7;i++){
        timeSlots.availableTimeSlots.push({
            weekday: i,
            timeSlots: [{startTime: '06:00', endTime: '23:59'}]
        })
    }
    teacherAvailableTimeSlots.value = timeSlots;

    // 清空当前学生选择，重新获取该教师的学生列表
    const originalStudentId = form.value.studentId; // 保存原有的学生ID
    form.value.studentId = '';
    students.value = [];

    // 自动获取该教师的学生列表（不需要关键词）
    studentsLoading.value = true;
    try {
      await curriculumStore.fetchStudents({
        keyword: '',
        teacherId: teacherId
      });
      students.value = curriculumStore.students;

      // 如果有从props传入的学生ID或者之前选择的学生ID，尝试恢复选择
      const targetStudentId = studentIdFromProps.value || originalStudentId;
      if (targetStudentId && students.value.length > 0) {
        const targetStudent = students.value.find(student =>
          String(student.id) === String(targetStudentId)
        );
        if (targetStudent) {
          form.value.studentId = targetStudent.id;
        }
      }
    } catch (error) {
      console.error('获取教师学生列表失败:', error);
    //   ElMessage.error('获取教师学生列表失败');
    } finally {
      studentsLoading.value = false;
    }

    // 检查是否有可排课时间段
    if (timeSlots && timeSlots.availableTimeSlots) {
      const hasAvailableTime = timeSlots.availableTimeSlots.some(slot =>
        slot.timeSlots && slot.timeSlots.length > 0
      );

      if (!hasAvailableTime) {
        ElMessage.warning('该教师暂无可排课时间段');
      } else {
        // 验证并重置当前的时间段选择
        validateAndResetSchedules();
      }
    }
  } catch (error) {
    console.error('获取教师可排课时间段失败:', error);
    ElMessage.error('获取教师可排课时间段失败');
  }
};

// 验证并重置不符合教师可排课时间的时间段
const validateAndResetSchedules = () => {
  if (!teacherAvailableTimeSlots.value) return;

  // 如果有从props传入的时间段，优先保持这些时间段
  if (timeSlotsFromProps.value && timeSlotsFromProps.value.length > 0) {
    // 重新设置从props传入的时间段，确保它们不被重置
    const propsSchedules = timeSlotsFromProps.value.map(slot => {
      const schedule = {
        dayOfWeek: slot.dayOfWeek || slot.weekday || 1,
        startTime: slot.startTime || "09:00",
        endTime: slot.endTime || "",
        duration: slot.duration || 60,
      };
      // 如果没有结束时间，自动计算
      if (!schedule.endTime && schedule.startTime && schedule.duration) {
        updateEndTime(schedule);
      }
      return schedule;
    });

    // 只有在当前时间段数量少于props传入的时间段时才更新
    if (form.value.weeklySchedules.length < propsSchedules.length) {
      form.value.weeklySchedules = propsSchedules;
    }

    // 重新计算结束日期
    calculateEndDate();
    return;
  }

  form.value.weeklySchedules.forEach(schedule => {
    // 检查当前选择的星期几和时长是否有可排课时间
    const availableOptions = getAvailableStartTimeOptions(schedule.dayOfWeek, schedule.duration);

    // 如果当前选择的开始时间不在可用选项中，重置为第一个可用时间
    if (schedule.startTime && !availableOptions.find(opt => opt.value === schedule.startTime)) {
      if (availableOptions.length > 0) {
        schedule.startTime = availableOptions[0].value;
        updateEndTime(schedule);
      } else {
        // 如果该星期几没有可排课时间，清空时间选择
        schedule.startTime = '';
        schedule.endTime = '';
      }
    }
  });

  // 重新计算结束日期
  calculateEndDate();
};

// 检查时间段冲突
const checkScheduleConflicts = () => {
  const conflicts = [];

  for (let i = 0; i < form.value.weeklySchedules.length; i++) {
    const schedule1 = form.value.weeklySchedules[i];

    // 跳过不完整的时间段
    if (!schedule1.dayOfWeek || !schedule1.startTime || !schedule1.endTime) {
      continue;
    }

    for (let j = i + 1; j < form.value.weeklySchedules.length; j++) {
      const schedule2 = form.value.weeklySchedules[j];

      // 跳过不完整的时间段
      if (!schedule2.dayOfWeek || !schedule2.startTime || !schedule2.endTime) {
        continue;
      }

      // 只检查同一天的时间段
      if (schedule1.dayOfWeek !== schedule2.dayOfWeek) {
        continue;
      }

      // 检查时间重叠
      const start1 = schedule1.startTime;
      const end1 = schedule1.endTime;
      const start2 = schedule2.startTime;
      const end2 = schedule2.endTime;

      const hasConflict = (
        (start1 >= start2 && start1 < end2) || // schedule1开始时间在schedule2内
        (end1 > start2 && end1 <= end2) ||     // schedule1结束时间在schedule2内
        (start1 <= start2 && end1 >= end2)     // schedule1完全包含schedule2
      );

      if (hasConflict) {
        conflicts.push({
          schedule1: { ...schedule1, index: i },
          schedule2: { ...schedule2, index: j }
        });
      }
    }
  }

  return conflicts;
};

// 处理星期几变更
const onDayOfWeekChange = (schedule) => {
  // 获取新选择星期几和当前时长的可用开始时间选项
  const availableOptions = getAvailableStartTimeOptions(schedule.dayOfWeek, schedule.duration);

  // 如果该星期几有可排课时间，设置为第一个可用时间
  if (availableOptions.length > 0) {
    schedule.startTime = availableOptions[0].value;
    updateEndTime(schedule);
  } else {
    // 如果该星期几没有可排课时间，清空时间选择并提示
    schedule.startTime = '';
    schedule.endTime = '';
    if (teacherAvailableTimeSlots.value) {
      ElMessage.warning(`该教师在${getWeekdayName(schedule.dayOfWeek)}没有可排课时间`);
    }
  }

  // 重新计算结束日期
  calculateEndDate();
};

// 处理时长变更
const onDurationChange = (schedule) => {
  // 获取新时长下的可用开始时间选项
  const availableOptions = getAvailableStartTimeOptions(schedule.dayOfWeek, schedule.duration);

  // 检查当前开始时间是否仍然有效
  const currentStartTimeValid = availableOptions.find(opt => opt.value === schedule.startTime);

  if (!currentStartTimeValid) {
    // 如果当前开始时间无效，设置为第一个可用时间
    if (availableOptions.length > 0) {
      schedule.startTime = availableOptions[0].value;
    } else {
      // 如果没有可用时间，清空时间选择
      schedule.startTime = '';
      schedule.endTime = '';
      if (teacherAvailableTimeSlots.value) {
        ElMessage.warning(`该时长在${getWeekdayName(schedule.dayOfWeek)}没有可排课时间`);
      }
      return;
    }
  }

  // 更新结束时间
  updateEndTime(schedule);

  // 重新计算结束日期
  calculateEndDate();
};

// 获取星期名称
const getWeekdayName = (weekday) => {
  const weekdays = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日'
  };
  return weekdays[weekday] || `星期${weekday}`;
};

// 获取有可排课时间的星期几选项
const getAvailableWeekDays = () => {
  // 如果没有选择教师或没有可排课时间段限制，返回所有星期选项
  if (!teacherAvailableTimeSlots.value) {
    return weekDays;
  }

  // 过滤出有可排课时间的星期几
  const availableWeekDays = weekDays.filter(day => {
    const weekdaySlot = teacherAvailableTimeSlots.value.availableTimeSlots.find(
      slot => slot.weekday === day.value
    );
    return weekdaySlot && weekdaySlot.timeSlots && weekdaySlot.timeSlots.length > 0;
  });

  return availableWeekDays;
};

// 获取指定星期几的可用时间选项（旧方法，保留兼容性）
const getAvailableTimeOptions = (dayOfWeek) => {
  // 如果没有选择教师或没有可排课时间段限制，返回所有时间选项
  if (!teacherAvailableTimeSlots.value || !dayOfWeek) {
    return timeOptions;
  }

  const weekdaySlot = teacherAvailableTimeSlots.value.availableTimeSlots.find(
    slot => slot.weekday === dayOfWeek
  );

  // 如果该星期几没有可排课时间，返回空数组
  if (!weekdaySlot || !weekdaySlot.timeSlots || weekdaySlot.timeSlots.length === 0) {
    return [];
  }

  // 过滤时间选项，只保留在可排课时间段内的时间
  const availableOptions = [];

  weekdaySlot.timeSlots.forEach(availableSlot => {
    timeOptions.forEach(timeOption => {
      // 使用字符串比较，更简单可靠
      const optionTime = timeOption.value;

      // 检查该时间是否在可排课时间段内
      if (optionTime >= availableSlot.startTime && optionTime < availableSlot.endTime) {
        // 避免重复添加
        if (!availableOptions.find(opt => opt.value === timeOption.value)) {
          availableOptions.push(timeOption);
        }
      }
    });
  });

  // 按时间排序
  return availableOptions.sort((a, b) => a.value.localeCompare(b.value));
};

// 获取指定星期几和时长的可用开始时间选项
const getAvailableStartTimeOptions = (dayOfWeek, duration) => {
  // 如果没有选择教师或没有可排课时间段限制，返回所有时间选项
  if (!teacherAvailableTimeSlots.value || !dayOfWeek || !duration) {
    return timeOptions;
  }

  const weekdaySlot = teacherAvailableTimeSlots.value.availableTimeSlots.find(
    slot => slot.weekday === dayOfWeek
  );

  // 如果该星期几没有可排课时间，返回空数组
  if (!weekdaySlot || !weekdaySlot.timeSlots || weekdaySlot.timeSlots.length === 0) {
    return [];
  }

  // 过滤时间选项，确保开始时间+时长后的结束时间也在可排课时间段内
  const availableOptions = [];

  weekdaySlot.timeSlots.forEach(availableSlot => {
    timeOptions.forEach(timeOption => {
      const startTime = timeOption.value;

      // 计算结束时间
      const [hours, minutes] = startTime.split(':').map(Number);
      const startDate = new Date();
      startDate.setHours(hours, minutes, 0, 0);
      const endDate = new Date(startDate.getTime() + duration * 60 * 1000);

      // 检查是否跨越了午夜
      if (endDate.getDate() !== startDate.getDate()) {
        // 如果跨越了午夜，这个时间段不可用
        return;
      }

      const endHours = endDate.getHours().toString().padStart(2, '0');
      const endMinutes = endDate.getMinutes().toString().padStart(2, '0');
      const endTime = `${endHours}:${endMinutes}`;

      // 检查开始时间和结束时间都在可排课时间段内
      if (startTime >= availableSlot.startTime && endTime <= availableSlot.endTime) {
        // 避免重复添加
        if (!availableOptions.find(opt => opt.value === timeOption.value)) {
          availableOptions.push(timeOption);
        }
      }
    });
  });

  // 按时间排序
  return availableOptions.sort((a, b) => a.value.localeCompare(b.value));
};

// 验证时间段是否在教师可排课时间内
const isTimeSlotAvailable = (dayOfWeek, startTime, endTime) => {
  if (!teacherAvailableTimeSlots.value) return true; // 如果没有限制，默认可用

  const weekdaySlot = teacherAvailableTimeSlots.value.availableTimeSlots.find(
    slot => slot.weekday === dayOfWeek
  );

  if (!weekdaySlot || !weekdaySlot.timeSlots || weekdaySlot.timeSlots.length === 0) {
    return false; // 该星期几没有可排课时间
  }

  // 检查时间段是否在任一可用时间段内
  return weekdaySlot.timeSlots.some(availableSlot => {
    const availableStart = new Date(`2000-01-01 ${availableSlot.startTime}`);
    const availableEnd = new Date(`2000-01-01 ${availableSlot.endTime}`);
    const scheduleStart = new Date(`2000-01-01 ${startTime}`);
    const scheduleEnd = new Date(`2000-01-01 ${endTime}`);

    return scheduleStart >= availableStart && scheduleEnd <= availableEnd;
  });
};

const disabledDate = (date) => {
  // 禁用过去的日期
  return date < new Date().setHours(0, 0, 0, 0);
};

// 自动计算结束时间
const updateEndTime = (schedule) => {
  if (!schedule.startTime || !schedule.duration) {
    schedule.endTime = "";
    return;
  }

  const [hours, minutes] = schedule.startTime.split(":").map(Number);
  const startDate = new Date();
  startDate.setHours(hours, minutes, 0, 0);

  const endDate = new Date(startDate.getTime() + schedule.duration * 60 * 1000);

  // 检查是否跨越了午夜
  if (endDate.getDate() !== startDate.getDate()) {
    // 如果跨越了午夜，清空结束时间并提示
    schedule.endTime = "";
    ElMessage.warning('课程时长过长，结束时间不能跨越午夜，请调整时长或开始时间');
    return;
  }

  const endHours = endDate.getHours().toString().padStart(2, "0");
  const endMinutes = endDate.getMinutes().toString().padStart(2, "0");

  schedule.endTime = `${endHours}:${endMinutes}`;

  // 触发重新计算结束日期
  calculateEndDate();
};

const addWeeklySchedule = () => {
  // 找到第一个有可排课时间的星期几
  let defaultDayOfWeek = 1;
  let defaultStartTime = "09:00";
  let defaultDuration = 60;

  if (teacherAvailableTimeSlots.value) {
    const availableSlot = teacherAvailableTimeSlots.value.availableTimeSlots.find(
      slot => slot.timeSlots && slot.timeSlots.length > 0
    );

    if (availableSlot) {
      defaultDayOfWeek = availableSlot.weekday;

      // 使用新的方法获取可用的开始时间
      const availableStartTimes = getAvailableStartTimeOptions(defaultDayOfWeek, defaultDuration);
      if (availableStartTimes.length > 0) {
        defaultStartTime = availableStartTimes[0].value;
      }
    }
  }

  const newSchedule = {
    dayOfWeek: defaultDayOfWeek,
    startTime: defaultStartTime,
    endTime: "",
    duration: defaultDuration,
  };

  updateEndTime(newSchedule);
  form.value.weeklySchedules.push(newSchedule);
  calculateEndDate(); // 重新计算结束日期
};

const removeWeeklySchedule = (index) => {
  form.value.weeklySchedules.splice(index, 1);
  calculateEndDate(); // 重新计算结束日期
};

const getScheduleDuration = (schedule) => {
  if (!schedule.startTime || !schedule.endTime) return "";

  const start = new Date(`2000-01-01 ${schedule.startTime}`);
  const end = new Date(`2000-01-01 ${schedule.endTime}`);
  const duration = (end - start) / (1000 * 60);

  return duration > 0 ? `${duration}分钟` : "";
};

const formatPreviewDate = (date) => {
  return date.toLocaleDateString("zh-CN", {
    month: "2-digit",
    day: "2-digit",
  });
};

const getDayName = (dayOfWeek) => {
  const dayNames = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  return dayNames[dayOfWeek] || "";
};

// 日历导航方法
const prevMonth = () => {
  currentMonth.value = new Date(
    currentMonth.value.getFullYear(),
    currentMonth.value.getMonth() - 1,
    1
  );
};

const nextMonth = () => {
  currentMonth.value = new Date(
    currentMonth.value.getFullYear(),
    currentMonth.value.getMonth() + 1,
    1
  );
};

// 获取日期的CSS类
const getDayClass = (day) => {
  const classes = [];

  if (!day.isCurrentMonth) {
    classes.push("other-month");
  }

  if (day.courses.length > 0) {
    classes.push("has-courses");
  }

  // 检查是否是今天
  const today = new Date();
  if (day.date.toDateString() === today.toDateString()) {
    classes.push("today");
  }

  return classes;
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    if (previewData.value.length === 0) {
      ElMessage.warning("请设置有效的上课时间");
      return;
    }

    // 确保教师ID存在
    if (!userStore.id) {
      try {
        await userStore.getInfo();
      } catch (error) {
        console.error("获取用户信息失败:", error);
        ElMessage.error("获取用户信息失败，请重新登录");
        return;
      }
    }

    if (!userStore.id) {
      ElMessage.error("无法获取教师信息，请重新登录");
      return;
    }

    submitting.value = true;

    // 检查时间段冲突
    const conflicts = checkScheduleConflicts();
    if (conflicts.length > 0) {
      const conflictMessages = conflicts.map(conflict => {
        const day1 = getWeekdayName(conflict.schedule1.dayOfWeek);
        const day2 = getWeekdayName(conflict.schedule2.dayOfWeek);
        return `${day1} ${conflict.schedule1.startTime}-${conflict.schedule1.endTime} 与 ${day2} ${conflict.schedule2.startTime}-${conflict.schedule2.endTime}`;
      });
      ElMessage.error(`存在时间冲突：${conflictMessages.join('；')}`);
      return;
    }

    // 验证教师可排课时间段
    if (teacherAvailableTimeSlots.value) {
      const invalidSchedules = form.value.weeklySchedules.filter(schedule => {
        if (!schedule.dayOfWeek || !schedule.startTime || !schedule.endTime) return true; // 空的时间段也算无效
        return !isTimeSlotAvailable(schedule.dayOfWeek, schedule.startTime, schedule.endTime);
      });

      if (invalidSchedules.length > 0) {
        const invalidDays = invalidSchedules.map(s => getWeekdayName(s.dayOfWeek)).join('、');
        ElMessage.error(`${invalidDays}的时间段不在教师可排课时间范围内，请重新选择`);
        return;
      }

      // 检查是否有空的时间段
      const emptySchedules = form.value.weeklySchedules.filter(schedule =>
        !schedule.startTime || !schedule.endTime
      );

      if (emptySchedules.length > 0) {
        ElMessage.error('请完善所有时间段的设置');
        return;
      }
    }

    // 获取选中学生的年级信息
    const selectedStudent = students.value.find(student => student.id === form.value.studentId);
    const studentGrade = selectedStudent?.grade || '';

    const scheduleData = {
      studentId: form.value.studentId,
      teacherId: form.value.teacherId, // 使用表单中选择的教师ID
      subject: form.value.subject,
      specification: form.value.specification, // 新增规格字段
      type: form.value.type,
      courseType: form.value.courseType, // 新增课程性质字段
      useSystem: form.value.useSystem, // 新增使用系统字段
      dateRange: form.value.dateRange,
      totalLessons: form.value.totalLessons,
      weeklySchedules: form.value.weeklySchedules,
      duration: form.value.duration,
      grade: studentGrade, // 添加学生年级信息
    };


    const success = await curriculumStore.createSchedule(scheduleData);

    if (success) {
      ElMessage.success(`排课成功，共创建${previewData.value.length}节课程`);
      emit("success");
      handleClose();
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitting.value = false;
  }
};

const handleClose = () => {
  visible.value = false;
  resetForm();
};

const resetForm = () => {
  // 重置表单验证
  formRef.value?.resetFields();

  // 根据教师可排课时间设置默认时间段
  let defaultDayOfWeek = 1;
  let defaultStartTime = "09:00";
  let defaultDuration = 60;

  if (teacherAvailableTimeSlots.value) {
    const availableSlot = teacherAvailableTimeSlots.value.availableTimeSlots.find(
      slot => slot.timeSlots && slot.timeSlots.length > 0
    );

    if (availableSlot) {
      defaultDayOfWeek = availableSlot.weekday;

      // 使用新的方法获取可用的开始时间
      const availableStartTimes = getAvailableStartTimeOptions(defaultDayOfWeek, defaultDuration);
      if (availableStartTimes.length > 0) {
        defaultStartTime = availableStartTimes[0].value;
      }
    }
  }

  const defaultSchedule = {
    dayOfWeek: defaultDayOfWeek,
    startTime: defaultStartTime,
    endTime: "",
    duration: defaultDuration,
  };
  updateEndTime(defaultSchedule);

  // 处理从props传入的时间段数据
  let initialSchedules = [defaultSchedule];
  if (timeSlotsFromProps.value && timeSlotsFromProps.value.length > 0) {
    initialSchedules = timeSlotsFromProps.value.map(slot => {
      const schedule = {
        dayOfWeek: slot.dayOfWeek || slot.weekday || 1,
        startTime: slot.startTime || "09:00",
        endTime: slot.endTime || "",
        duration: slot.duration || 60,
      };
      // 如果没有结束时间，自动计算
      if (!schedule.endTime && schedule.startTime && schedule.duration) {
        updateEndTime(schedule);
      }
      return schedule;
    });
  }

  // 设置表单数据
  form.value = {
    teacherId: teacherIdFromProps.value || "",
    studentId: studentIdFromProps.value || "",
    subject: "英语",
    specification: "单词课",
    type: "学习课",
    courseType: "正式课",
    useSystem: true,
    totalLessons: 10,
    startDate: startDateFromProps.value || "",
    endDate: "",
    dateRange: startDateFromProps.value ? [startDateFromProps.value, ""] : [],
    weeklySchedules: initialSchedules,
    duration: 60,
  };

  showPreviewDetail.value = false;

  // 如果不是从props传入的教师ID，则清空教师可排课时间段
  if (!teacherIdFromProps.value) {
    teacherAvailableTimeSlots.value = null;
  }

  // 如果设置了开始日期和时间段，延迟计算结束日期
  if (startDateFromProps.value && initialSchedules.length > 0) {
    setTimeout(() => {
      calculateEndDate();
    }, 0);
  }
};

// 初始化时间段
const initializeSchedules = () => {
  form.value.weeklySchedules.forEach((schedule) => {
    if (!schedule.duration) {
      schedule.duration = 60; // 默认60分钟
    }

    // 如果有教师可排课时间限制，验证并调整时间段
    if (teacherAvailableTimeSlots.value) {
      const availableOptions = getAvailableStartTimeOptions(schedule.dayOfWeek, schedule.duration);

      // 如果当前时间不可用，设置为第一个可用时间
      if (schedule.startTime && !availableOptions.find(opt => opt.value === schedule.startTime)) {
        if (availableOptions.length > 0) {
          schedule.startTime = availableOptions[0].value;
        } else {
          schedule.startTime = '';
          schedule.endTime = '';
          return; // 跳过updateEndTime
        }
      }
    }

    updateEndTime(schedule);
  });
};

// 监听对话框显示状态
watch(visible, (newValue) => {
  if (newValue) {
    // 对话框打开时，首先重置表单并设置props数据
    resetForm();

    // 延迟执行，确保 props 数据已经传递完成
    setTimeout(() => {
      // 如果有完整的教师和学生信息，跳过加载列表
      if (hasCompleteTeacherInfo.value && hasCompleteStudentInfo.value) {
        // 直接设置表单数据
        form.value.teacherId = props.teacherId;
        form.value.studentId = props.studentId;

        // 确保开始日期被正确设置
        if (startDateFromProps.value) {
          // 使用 nextTick 确保 DOM 更新完成
          setTimeout(() => {
            form.value.startDate = startDateFromProps.value;
            form.value.dateRange = [startDateFromProps.value, ""];

            // 再次延迟计算结束日期
            setTimeout(() => {
              calculateEndDate();
            }, 50);
          }, 0);
        }

        // 加载教师可排课时间段
        if (props.teacherId) {
          loadAndValidateTeacher(props.teacherId);
        }
      } else {
        // 如果从props传入了teacherId，需要先加载教师列表然后验证
        if (teacherIdFromProps.value) {
          loadAndValidateTeacher(teacherIdFromProps.value);
        } else {
          // 没有传入teacherId时，正常加载教师列表
          searchTeachers("");
        }
      }
    }, 50);

    // 不在这里加载学生列表，等教师选择后再加载
    // searchStudents(""); // 移除这行，避免在没有教师时调用
  }
});

// 监听props中的teacherId变化
watch(() => props.teacherId, (newTeacherId) => {
  if (newTeacherId && visible.value) {
    // 只有在对话框可见时才进行验证，避免在初始化时重复执行
    loadAndValidateTeacher(newTeacherId);
  }
}, { immediate: false }); // 改为false，避免在组件初始化时执行

// 监听教师可排课时间段变化
watch(
  () => teacherAvailableTimeSlots.value,
  (newTimeSlots) => {
    // 当教师可排课时间段变化时，重新验证当前的时间段选择
    if (newTimeSlots) {
      validateAndResetSchedules();
    }
  },
  { deep: true }
);

// 监听日期范围变化，自动设置日历月份
watch(
  () => form.value.dateRange,
  (newDateRange) => {
    if (newDateRange && newDateRange.length === 2) {
      // 设置日历显示包含开始日期的月份
      const startDate = new Date(newDateRange[0]);
      currentMonth.value = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
    }
  }
);

// 监听 props 中的时间段变化
watch(
  () => props.timeSlots,
  (newTimeSlots) => {
    if (newTimeSlots && newTimeSlots.length > 0 && visible.value) {
      // 当时间段props变化时，更新表单中的时间段
      const updatedSchedules = newTimeSlots.map(slot => {
        const schedule = {
          dayOfWeek: slot.dayOfWeek || slot.weekday || 1,
          startTime: slot.startTime || "09:00",
          endTime: slot.endTime || "",
          duration: slot.duration || 60,
        };
        // 如果没有结束时间，自动计算
        if (!schedule.endTime && schedule.startTime && schedule.duration) {
          updateEndTime(schedule);
        }
        return schedule;
      });

      form.value.weeklySchedules = updatedSchedules;

      // 重新计算结束日期
      setTimeout(() => {
        calculateEndDate();
      }, 0);
    }
  },
  { deep: true, immediate: false }
);

// 监听 props 中的学生ID变化
watch(
  () => props.studentId,
  (newStudentId) => {
    if (newStudentId && visible.value) {
      // 如果有教师ID，尝试加载学生列表并选中学生
      if (form.value.teacherId) {
        searchStudents("").then(() => {
          // 在学生列表加载完成后，尝试选中学生
          if (students.value.length > 0) {
            const targetStudent = students.value.find(student =>
              String(student.id) === String(newStudentId)
            );
            if (targetStudent) {
              form.value.studentId = targetStudent.id;
            }
          }
        });
      } else {
        // 如果没有教师ID，直接设置学生ID（等教师选择后会自动处理）
        form.value.studentId = newStudentId;
      }
    }
  },
  { immediate: false }
);

// 监听 props 中的开始日期变化
watch(
  () => props.startDate,
  (newStartDate) => {
    if (newStartDate && visible.value) {
      form.value.startDate = newStartDate;
      form.value.dateRange = [newStartDate, form.value.endDate || ""];

      // 重新计算结束日期
      setTimeout(() => {
        calculateEndDate();
      }, 0);
    }
  },
  { immediate: false }
);

// 监听学科变化，更新课型选项
watch(
  () => form.value.subject,
  (newSubject) => {
    // 清空当前选择的课型
    form.value.specification = '';

    if (!newSubject) {
      // 没有选择学科，显示所有课型
      specificationOptions.value = allSpecificationOptions;
    } else if (newSubject === '英语') {
      // 英语学科，显示英语相关课型
      specificationOptions.value = englishSpecificationOptions;
    } else {
      // 其他学科，只显示通用课
      specificationOptions.value = otherSubjectSpecificationOptions;
    }
  }
);

// 监听表单中开始日期的变化（用户手动修改）
watch(
  () => form.value.startDate,
  (newStartDate, oldStartDate) => {
    // 只有在对话框可见且日期确实发生变化时才处理
    if (visible.value && newStartDate !== oldStartDate && newStartDate) {
      // 更新日历月份显示
      const startDate = new Date(newStartDate);
      currentMonth.value = new Date(startDate.getFullYear(), startDate.getMonth(), 1);

      // 计算结束日期
      calculateEndDate();
    }
  },
  { immediate: false }
);

onMounted(async () => {
  // 确保用户信息已加载
  if (!userStore.id) {
    try {
      await userStore.getInfo();
    } catch (error) {
      console.error("获取用户信息失败:", error);
      ElMessage.error("获取用户信息失败");
    }
  }

  // 初始化默认时间段
  initializeSchedules();
  
  // 如果组件挂载时对话框就是可见的，手动触发数据加载
  if (visible.value) {
    if (teacherIdFromProps.value) {
      loadAndValidateTeacher(teacherIdFromProps.value);
    } else {
      searchTeachers("");
    }
    // 不在这里加载学生列表，等教师选择后再加载
    // searchStudents(""); // 移除这行，避免在没有教师时调用
  }
});
</script>

<style lang="scss" scoped>
.schedule-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 24px;
  }
}

.schedule-form {
  .teacher-select,
  .student-select,
  .subject-select,
  .specification-select,
  .start-date-picker {
    width: 100%;
  }

  .teacher-locked-tip {
    margin-top: 4px;
    font-size: 12px;
  }

  .student-disabled-tip {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }

  .teacher-available-time {
    .no-available-time {
      padding: 8px 12px;
      background-color: #fef3c7;
      border-radius: 4px;
      font-size: 14px;
    }

    .available-time-list {
      .weekday-slot {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;

        .weekday-name {
          font-weight: 500;
          color: #374151;
          min-width: 50px;
        }

        .no-time {
          color: #9ca3af;
          font-style: italic;
        }

        .time-slots {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .time-slot {
            background-color: #e0f2fe;
            color: #0891b2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            border: 1px solid #0891b2;
          }
        }
      }
    }
  }

  .teacher-option {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .teacher-name {
      font-weight: 500;
    }

    .teacher-phone {
      color: #6b7280;
      font-size: 12px;
    }
  }

  .lessons-input {
    width: calc(100% - 30px);
  }

  .unit-text {
    margin-left: 8px;
    color: #6b7280;
    font-size: 14px;
  }

  .readonly-date {
    .el-input__inner {
      background-color: #f5f5f5;
      color: #666;
      cursor: not-allowed;
    }

    .date-info {
      color: #6b7280;
      font-size: 12px;
      margin-right: 8px;
    }
  }

  .student-option {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .student-name {
      font-weight: 500;
    }

    .student-phone {
      color: #6b7280;
      font-size: 12px;
    }
  }

  .date-range-picker {
    width: 100%;
  }

  .duration-select {
    width: 100%;
  }

  .duration-unit {
    margin-left: 8px;
    color: #6b7280;
  }
}

.weekly-schedules {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: #f9fafb;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  span {
    font-weight: 500;
    color: #374151;
  }
}

.schedule-item {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .readonly-time {
    :deep(.el-input__inner) {
      background-color: #f5f5f5;
      color: #666;
      cursor: not-allowed;
    }
  }

  .time-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .duration-label {
      color: #6b7280;
      font-size: 14px;
      white-space: nowrap;
    }

    .delete-btn {
      margin-left: auto;
    }
  }
}

.preview-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 12px;
  background: #f8f9fa;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  span {
    font-weight: 500;
    color: #374151;
  }
}

.preview-detail {
  border-top: 1px solid #e5e7eb;
  padding-top: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  margin-bottom: 4px;
  background: white;
  border-radius: 4px;
  font-size: 14px;

  .preview-date {
    color: #374151;
    font-weight: 500;
  }

  .preview-time {
    color: #6b7280;
  }

  .preview-day {
    color: #9ca3af;
    font-size: 12px;
  }
}

.preview-more {
  text-align: center;
  color: #6b7280;
  font-size: 14px;
  margin-top: 8px;
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .schedule-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }
  }

  .schedule-item {
    .el-row {
      flex-direction: column;
      gap: 8px;
    }

    .el-col {
      width: 100% !important;
    }
  }

  .preview-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

// 日历样式
.calendar-container {
  width: 100%;

  .calendar-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 8px;

    .current-month {
      font-weight: 600;
      color: #374151;
      font-size: 16px;
    }
  }

  .calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 1px;

    .weekday-header {
      background: #f3f4f6;
      color: #6b7280;
      font-weight: 500;
      font-size: 12px;
      text-align: center;
      padding: 8px 4px;
      border-radius: 4px;
    }
  }

  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e5e7eb;
    border-radius: 8px;
    overflow: hidden;

    .calendar-day {
      background: white;
      min-height: 60px;
      min-width: 48px;
      padding: 4px;
      position: relative;
      display: flex;
      flex-direction: column;

      &.other-month {
        background: #f9fafb;
        color: #9ca3af;
      }

      &.today {
        background: #eff6ff;

        .day-number {
          background: #3b82f6;
          color: white;
          border-radius: 50%;
          width: 40px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          font-weight: 600;
        }
      }

      &.has-courses {
        background: #fef3c7;

        &.other-month {
          background: #fefcf3;
        }

        &.today {
          background: #dbeafe;
        }
      }

      .day-number {
        font-size: 12px;
        font-weight: 500;
        color: #374151;
        line-height: 1;
        margin-bottom: 2px;
      }

      .day-courses {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1px;

        .course-item {
          background: #f59e0b;
          color: white;
          font-size: 10px;
          padding: 1px 3px;
          border-radius: 2px;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.2;
          font-weight: 500;
        }
      }
    }
  }
}

// 移动端日历适配
@media (max-width: 768px) {
  .calendar-container {
    .calendar-grid {
      .calendar-day {
        min-height: 50px;
        padding: 2px;

        .day-number {
          font-size: 11px;
        }

        .day-courses {
          .course-item {
            font-size: 9px;
            padding: 1px 2px;
          }
        }
      }
    }

    .calendar-nav {
      .current-month {
        font-size: 14px;
      }
    }

    .calendar-header {
      .weekday-header {
        font-size: 11px;
        padding: 6px 2px;
      }
    }
  }
}

// 紧凑布局样式
.schedule-dialog {
  .el-dialog__body {
    padding: 15px 20px;
    max-height: 75vh;
    overflow-y: auto;
  }

  .el-dialog__header {
    padding: 15px 20px 10px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
  }
}

.schedule-form {
  .el-form-item {
    margin-bottom: 16px;

    &.compact-form-item {
      margin-bottom: 12px;
    }
  }

  .el-form-item__label {
    line-height: 32px;
    font-size: 14px;
  }

  .end-date-display {
    display: flex;
    align-items: center;
    gap: 8px;

    .date-info {
      font-size: 12px;
      color: #909399;
    }
  }
}

// 时间安排样式
.weekly-schedules {
  .schedule-header {
    &.compact {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .header-title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }
    }
  }
}

.schedule-row {
  margin-bottom: 8px;

  .el-select {
    width: 100%;
  }

  .end-time-display {
    display: flex;
    align-items: center;

    .time-range {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }

  .time-actions {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// 教师可排课时间紧凑显示
.teacher-available-time {
  &.compact {
    .available-time-list {
      &.compact {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .weekday-slot {
          &.compact {
            display: inline-flex;
            align-items: center;
            background: #f5f7fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;

            .weekday-name {
              font-weight: 500;
              color: #303133;
              margin-right: 4px;
            }

            .time-slots {
              .time-slot {
                color: #409eff;
                font-weight: 500;

                &:not(:last-child)::after {
                  content: ', ';
                  color: #909399;
                }
              }
            }

            .no-time {
              color: #909399;
            }
          }
        }
      }
    }
  }
}

// 预览容器紧凑样式
.preview-container {
  &.compact {
    .preview-summary {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .toggle-detail-btn {
        padding: 0;
        font-size: 12px;
      }
    }

    .preview-detail {
      &.compact {
        margin-top: 8px;
      }
    }
  }
}

// 紧凑日历样式
.calendar-container {
  &.compact {
    .calendar-nav {
      &.compact {
        padding: 8px 0;
        margin-bottom: 8px;

        .current-month {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    .calendar-header {
      &.compact {
        .weekday-header {
          padding: 6px 4px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }

    .calendar-grid {
      &.compact {
        gap: 1px;

        .calendar-day {
          &.compact {
            min-height: 45px;
            padding: 3px;

            .day-number {
              font-size: 11px;
              margin-bottom: 1px;
            }

            .day-courses {
              .course-item {
                &.compact {
                  font-size: 9px;
                  padding: 1px 2px;
                  line-height: 1.1;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式优化
@media (max-height: 800px) {
  .schedule-dialog {
    .el-dialog__body {
      max-height: 70vh;
      padding: 12px 20px;
    }

    .el-dialog__header {
      padding: 12px 20px 8px;
    }

    .el-dialog__footer {
      padding: 8px 20px 12px;
    }
  }

  .schedule-form {
    .el-form-item {
      margin-bottom: 12px;

      &.compact-form-item {
        margin-bottom: 8px;
      }
    }
  }
}

/* 锁定状态样式 */
.teacher-display,
.student-display {
  :deep(.el-input__wrapper) {
    background-color: #f5f7fa;
    border-color: #dcdfe6;
    cursor: not-allowed;
  }

  :deep(.el-input__inner) {
    background-color: #f5f7fa;
    color: #606266;
    cursor: not-allowed;
  }
}

.lock-icon {
  color: #909399;
}
</style>
