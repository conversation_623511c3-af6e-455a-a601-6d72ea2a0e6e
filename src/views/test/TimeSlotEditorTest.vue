<template>
  <div class="test-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>时间段编辑器测试</span>
          <el-button type="primary" @click="showDialog = true">
            测试对话框
          </el-button>
        </div>
      </template>
      
      <div class="test-content">
        <h3>当前时间段数据：</h3>
        <pre>{{ JSON.stringify(timeSlots, null, 2) }}</pre>
        
        <h3>高级时间段编辑器：</h3>
        <div class="editor-instructions">
          <el-alert
            title="使用说明"
            type="info"
            :closable="false"
            show-icon
          >
            <ul>
              <li><strong>拖拽选择</strong>：在时间网格上拖拽选择时间段（5分钟精度）</li>
              <li><strong>点击删除</strong>：点击绿色时间段可以删除</li>
              <li><strong>模板应用</strong>：使用预设模板快速设置常用时间</li>
              <li><strong>可视化</strong>：直观显示时间段，支持精确拖拽</li>
            </ul>
          </el-alert>
        </div>

        <AdvancedTimeSlotEditor
          v-model="timeSlots"
          @change="handleChange"
        />
      </div>
    </el-card>

    <!-- 测试对话框 -->
    <TeacherTimeSlotEditDialog
      v-model="showDialog"
      :teacher="testTeacher"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import AdvancedTimeSlotEditor from '../management/teacher/components/AdvancedTimeSlotEditor.vue'
import TeacherTimeSlotEditDialog from '../management/teacher/components/TeacherTimeSlotEditDialog.vue'

const timeSlots = ref([
  {
    id: 'test1',
    weekday: 1,
    startTime: '09:00',
    endTime: '12:00',
    status: 'available',
    remark: '上午时段'
  },
  {
    id: 'test2',
    weekday: 1,
    startTime: '14:00',
    endTime: '17:30',
    status: 'available',
    remark: '下午时段'
  }
])

const showDialog = ref(false)

const testTeacher = ref({
  id: 123,  // 使用数字ID
  name: '测试老师',
  phone: '13800138000'
})

const handleChange = (newTimeSlots) => {
  console.log('时间段变化:', newTimeSlots)
}

const handleSuccess = () => {
  ElMessage.success('时间段编辑成功')
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .test-content {
    h3 {
      margin: 20px 0 10px 0;
      color: #303133;
    }

    .editor-instructions {
      margin-bottom: 20px;

      :deep(.el-alert__content) {
        ul {
          margin: 8px 0 0 0;
          padding-left: 20px;

          li {
            margin-bottom: 4px;
            line-height: 1.5;
          }
        }
      }
    }

    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}
</style>
