<template>
  <div class="tree-contains">
    <div class="search-zone-class">
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 5px;
        "
      >
        <!-- <div class="add-tag"> -->
        <el-select
          v-model="dynamicTags"
          filterable
          multiple
          size="small"
          clearable
          :collapse-tags="true"
          :collapse-tags-tooltip="true"
          @change="onChangeMaterial"
          placeholder="请输入教材名称"
          style="width: 200px"
          placement="left-start"
        >
          <el-option
            v-for="item in searchOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <!-- </div> -->
        <div
          style="
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: flex-end;
            flex: 1;
            min-width: 160px;
          "
          v-if="props.openClass && currentBook?.textbookType"
        >
          <el-button-group class="ml-4" size="small">
            <el-button type="warning" @click="checkWord(5)">5个</el-button>
            <el-button type="warning" @click="checkWord(10)">10个</el-button>
            <el-button type="warning" @click="checkWord(15)">15个</el-button>
          </el-button-group>
        </div>
      </div>

      <div style="margin-bottom: 5px" class="material-tabs">
        <div
          v-for="item in tabs"
          :key="item.value"
          :class="[
            'material-tab-item f-12',
            { active: tabSelect.includes(item.value) },
          ]"
          :data-color="item.value"
          @click="toggleTabSelect(item.value)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <el-menu
      v-loading="loading"
      :class="[
        'material-menu',
        props.openClass
          ? 'material-menu-height-course'
          : 'material-menu-height',
      ]"
      :default-active="currentNodeIndex"
      :default-openeds="defaultOpeneds"
      :unique-opened="true"
      @select="handleNodeClick"
      @open="handleNodeClick"
    >
      <template v-for="(item, bookIndex) in treeData" :key="item.id">
        <el-sub-menu
          :ref="(el) => setTextbookRef(item.nodeId, el)"
          :index="
            item.textbookId +
            ';' +
            item.parentNodeId +
            ';' +
            item.nodeId +
            ';' +
            item.nodeType
          "
          :data-type="item.textbookType"
          :data-index="
            item.textbookId +
            ';' +
            item.parentNodeId +
            ';' +
            item.nodeId +
            ';' +
            item.nodeType
          "
        >
          <template #title>
            <span>{{ item.nodeName }}</span>
          </template>
          <template
            v-for="(child, index1) in item.childNodeList"
            :key="child.nodeId"
          >
            <el-sub-menu
              :key="child.nodeId + ';' + child.nodeType"
              :index="
                child.textbookId +
                ';' +
                child.parentNodeId +
                ';' +
                child.nodeId +
                ';' +
                child.nodeType
              "
              :data-index="
                child.textbookId +
                ';' +
                child.parentNodeId +
                ';' +
                child.nodeId +
                ';' +
                child.nodeType
              "
              v-if="[1, 2].includes(child.nodeType)"
            >
              <template #title>
                <div style="display: flex; align-items: center">
                  <el-checkbox
                    v-if="props.openClass"
                    v-model="child.checked"
                    style="margin-right: 10px"
                    @change="(val, event) => selectUnit(child, val, event)"
                    @click.stop
                  />
                  <span
                    >{{ child.nodeName
                    }}{{
                      props.openClass ? " (" + child.wordNum + "个单词)" : ""
                    }}</span
                  >
                </div>
              </template>
              <el-menu-item
                v-for="(subChild, index) in child.childNodeList"
                :key="subChild.nodeId"
                :ref="(el) => setWordRef(subChild.nodeId, el)"
                :index="
                  subChild.textbookId +
                  ';' +
                  subChild.parentNodeId +
                  ';' +
                  subChild.nodeId +
                  ';' +
                  subChild.nodeType +
                  ';' +
                  subChild.nodeName
                "
                style="height: 30px"
              >
                <div style="display: flex; align-items: center">
                  <el-checkbox
                    v-if="props.openClass"
                    v-model="subChild.checked"
                    style="margin-right: 10px"
                    @change="(val, event) => selectUnit(subChild, val, event)"
                    @click.stop
                  />
                  {{ subChild.displayOrder + ". " + subChild.nodeName }}
                  <span
                    :style="
                      subChild.nodeId == currentLastWord?.wordItemId
                        ? 'color:red'
                        : 'color:blue'
                    "
                  >
                    {{
                      subChild.learnStatus &&
                      subChild.nodeId == currentLastWord?.wordItemId
                        ? "（上次学到）"
                        : subChild.learnStatus
                        ? "（已学习）"
                        : ""
                    }}
                  </span>
                </div>
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item
              :key="child.nodeName + ';' + child.nodeType + ';'"
              :ref="(el) => setWordRef(child.nodeId, el)"
              :index="
                child.textbookId +
                ';' +
                child.parentNodeId +
                ';' +
                child.nodeId +
                ';' +
                child.nodeType +
                ';' +
                child.nodeName
              "
              style="height: 30px"
              v-else
            >
              <div style="display: flex; align-items: center">
                <el-checkbox
                  v-if="props.openClass"
                  v-model="child.checked"
                  style="margin-right: 10px"
                  @change="(val, event) => selectUnit(child, val, event)"
                  @click.stop
                />
                {{ child.displayOrder + ". " + child.nodeName }}
                {{
                  child.learnStatus &&
                  child.nodeId == currentLastWord?.wordItemId
                    ? "（上次学到）"
                    : child.learnStatus
                    ? "（已学习）"
                    : ""
                }}
              </div>
            </el-menu-item>
          </template>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from "vue";
import {
  BookTree,
  TextbookTreeParam,
  LastWordInfo,
  getTextbookTree,
  getLastTextbookId,
} from "../../../../api/textbook";
import { ElMessage } from "element-plus";

const textbookTreeParam = ref<TextbookTreeParam>({
  nodeId: "",
  nodeType: -1,
  searchType: "单词",
  studentId: "",
  types: [],
  tags: [],
});
const props = defineProps<{
  //是否是上课场景
  openClass: boolean;
  refreshTree?: boolean;
  studentId?: string;
}>();

const emit = defineEmits<{
  (e: "clickMaterialNode", nodeInfo: any): void;
  (e: "selectUnit", unitIdArray: BookTree[]): void;
}>();

// 存储选中项的父级路径
const selectedPath = ref<string[]>([]);
const currentBook = ref<BookTree>();
const currentUnit = ref<BookTree>();

//上课时勾选的教材
const checkBoxCurrentBook = ref<BookTree>();
const checkBoxCurrentUnit = ref<BookTree>();

const normalizeNode = (node: any): BookTree => {
  return {
    nodeId: node.nodeId,
    nodeType: node.nodeType,
    nodeName: node.nodeName,
    displayOrder: node.displayOrder,
    wordNum: node.wordNum ?? 0,
    checked: false, // 显式设置
    textbookType: node.textbookType,
    parentNodeId: node.parentNodeId,
    textbookId: node.textbookId,
    wordId: node.wordId,
    childNodeList: node.childNodeList?.map(normalizeNode) || [],
    learnStatus: node.learnStatus,
    mistakes: node.mistakes,
  };
};

const handleNodeClick = async (nodeInfo: string) => {
  const arr = nodeInfo.split(";");
  if (arr[3] == "3") {
    let wordNode = currentUnit.value?.childNodeList?.find(
      (item) => item.nodeId == arr[2]
    );
    if (!wordNode) {
      wordNode = currentBook.value?.childNodeList?.find(
        (item) => item.nodeId == arr[2]
      );
    }
    if (wordNode) {
      wordNode.checked = !wordNode.checked;
      if (wordNode.checked) {
        lasCheckWordId.value = wordNode.nodeId;
      } else {
        lasCheckWordId.value = "";
      }
      calcCheckedNodeId(wordNode, wordNode.checked);
      emit("selectUnit", selectUnitIdArray.value);
    }
  } else if (arr[3] == "2") {
    lasCheckWordId.value = "";
  }
  console.log("当前时间：", new Date());
  // 处理菜单项点击事件
  if (currentNodeIndex.value && currentNodeIndex.value == nodeInfo) {
    return;
  }

  if (arr[3] == "1") {
    if (currentUnit.value) {
      currentUnit.value.checked = false;
    }

    if (checkBoxCurrentUnit.value) {
      checkBoxCurrentUnit.value.checked = false;
    }
    clearSelectedNode();
    emit("selectUnit", selectUnitIdArray.value);
  }
  currentNodeIndex.value = nodeInfo;

  updateSelectedPath(nodeInfo);
  setCurrentBookAndUnit(arr, "normal");

  console.log("当前时间1：", new Date());

  if (!props.openClass) {
    emit("clickMaterialNode", {
      textbookId: currentBook.value?.nodeId || "",
      unitId: currentUnit.value?.nodeId || "",
      nodeId: arr[2],
      nodeType: Number(arr[3]),
      word: arr[4] || "",
      bookName: currentBook.value?.nodeName,
      unitName: currentUnit.value?.nodeName,
    });
  }

  if (
    arr[3] === "3" ||
    (arr[3] == "2" &&
      currentUnit.value?.childNodeList &&
      currentUnit.value.childNodeList.length > 0) ||
    (arr[3] == "1" &&
      currentBook.value?.childNodeList &&
      currentBook.value.childNodeList.length > 0)
  ) {
    if (arr[3] == "1") {
      processLastWordNext(arr[0]);
    }
    return;
  }
  textbookTreeParam.value.nodeType = Number(arr[3]);
  textbookTreeParam.value.nodeId = arr[2];
  await fetchTreeData();

  if (arr[3] == "1") {
    processLastWordNext(arr[0]);
  }
};

const getTreeNode = (treeNodeId: string) => {
  return treeData.value.filter((item) => item.nodeId == treeNodeId)[0];
};

const getUnitNode = (unitNodeId: string, type: string) => {
  if (type == "checkbox") {
    return checkBoxCurrentBook.value?.childNodeList?.filter(
      (item) => item.nodeId == unitNodeId
    )[0];
  }

  return currentBook.value?.childNodeList?.filter(
    (item) => item.nodeId == unitNodeId
  )[0];
};

// 更新选中路径
const updateSelectedPath = (nodeInfo: string) => {
  // 清空当前路径
  selectedPath.value = [];

  // 查找选中项的所有父级
  findNodePath(treeData.value, nodeInfo);

  // 添加当前选中项到路径
  selectedPath.value.push(nodeInfo);

  // 添加自定义类名到DOM元素
  nextTick(() => {
    // 先移除所有选中状态
    document.querySelectorAll(".el-sub-menu").forEach((el) => {
      el.classList.remove("is-parent-active");
    });

    // 为路径中的每个父级添加选中状态
    selectedPath.value.forEach((path) => {
      const selector = `.el-sub-menu[data-index="${path}"]`;
      const element = document.querySelector(selector);
      if (element) {
        element.classList.add("is-parent-active");
      }
    });
  });
};

// 递归查找节点路径
const findNodePath = (
  nodes: any[],
  targetIndex: string,
  currentPath: string[] = []
): boolean => {
  if (!nodes) return false;

  for (const node of nodes) {
    // 检查当前节点
    const nodeIndex =
      node.textbookId +
      ";" +
      node.parentNodeId +
      ";" +
      node.nodeId +
      ";" +
      node.nodeType;
    const wordIndex =
      node.textbookId +
      ";" +
      node.parentNodeId +
      ";" +
      node.nodeId +
      ";" +
      node.nodeType +
      ";" +
      node.nodeName;
    // 如果找到目标节点
    if (nodeIndex === targetIndex || wordIndex === targetIndex) {
      selectedPath.value = [...currentPath];
      return true;
    }

    // 如果有子节点，递归查找
    if (node.childNodeList && node.childNodeList.length > 0) {
      const newPath = [...currentPath, nodeIndex];
      if (findNodePath(node.childNodeList, targetIndex, newPath)) {
        return true;
      }
    }
  }

  return false;
};

/**
 *
 * @param arr 设置选中的教材和单元
 * @param type normal：展开操作，checkbox：勾选操作
 */
const setCurrentBookAndUnit = (arr, type) => {
  if (arr[3] == "1" && currentBook.value?.nodeId != arr[2]) {
    if (type == "checkbox") {
      checkBoxCurrentBook.value = getTreeNode(arr[0]);
      checkBoxCurrentUnit.value = undefined;
    } else {
      currentBook.value = getTreeNode(arr[0]);
      currentUnit.value = undefined;
    }
  }

  if (
    arr[3] == "2" &&
    (currentUnit.value?.nodeId != arr[2] ||
      checkBoxCurrentUnit.value?.nodeId != arr[2])
  ) {
    if (type == "checkbox") {
      if (checkBoxCurrentBook.value?.nodeId != arr[0]) {
        checkBoxCurrentBook.value = getTreeNode(arr[0]);
      }

      if (checkBoxCurrentUnit.value?.nodeId != arr[2]) {
        checkBoxCurrentUnit.value = getUnitNode(arr[2], "checkbox");
      }
    } else {
      if (currentBook.value?.nodeId != arr[0]) {
        currentBook.value = getTreeNode(arr[0]);
      }

      if (currentUnit.value?.nodeId != arr[2]) {
        currentUnit.value = getUnitNode(arr[2], "normal");
      }
    }
  }

  if (arr[3] == "3") {
    if (type == "checkbox") {
      if (checkBoxCurrentBook.value?.nodeId != arr[0]) {
        checkBoxCurrentBook.value = getTreeNode(arr[0]);
      }

      if (checkBoxCurrentUnit.value?.nodeId != arr[1]) {
        checkBoxCurrentUnit.value = getUnitNode(arr[1], "checkbox");
      }
    } else {
      if (currentBook.value?.nodeId != arr[0]) {
        currentBook.value = getTreeNode(arr[0]);
      }

      if (currentUnit.value?.nodeId != arr[1]) {
        currentUnit.value = getUnitNode(arr[1], "normal");
      }
    }
  }
};

const selectUnit = async (node: BookTree, checked: boolean, event?: Event) => {
  if (event) {
    // 阻止事件冒泡
    event.stopPropagation();
  }

  node.checked = checked;

  const arr: string[] = [];
  arr[0] = node.textbookId;
  arr[1] = node.parentNodeId;
  arr[2] = node.nodeId;
  arr[3] = node.nodeType.toString();
  arr[4] = node.nodeName;
  setCurrentBookAndUnit(arr, "checkbox");
  if (node.nodeType == 2) {
    lasCheckWordId.value = "";
  }
  if (node.nodeType === 2 && node.childNodeList?.length === 0) {
    await handleNodeClick(
      node.textbookId +
        ";" +
        node.parentNodeId +
        ";" +
        node.nodeId +
        ";" +
        node.nodeType
    );
    checkBoxCurrentUnit.value?.childNodeList?.forEach((element) => {
      calcCheckedNodeId(element, element.checked);
    });
  }

  if (
    node.nodeType === 2 &&
    checkBoxCurrentUnit.value &&
    checkBoxCurrentUnit.value.childNodeList
  ) {
    checkBoxCurrentUnit.value?.childNodeList?.forEach((element) => {
      if (node.checked && !element.checked) {
        calcCheckedNodeId(element, node.checked);
      } else if (!node.checked) {
        calcCheckedNodeId(element, node.checked);
      }
      element.checked = node.checked;
    });
  }

  if (
    node.nodeType === 3 &&
    checkBoxCurrentUnit.value &&
    checkBoxCurrentUnit.value.childNodeList
  ) {
    let isAllChecked = true;
    checkBoxCurrentUnit.value.childNodeList.forEach((item) => {
      !item.checked ? (isAllChecked = false) : "";
    });

    checkBoxCurrentUnit.value.checked = isAllChecked;
    calcCheckedNodeId(node, node.checked);
    if (node.checked) {
      lasCheckWordId.value = node.nodeId;
    } else {
      lasCheckWordId.value = "";
    }
  } else if (
    node.nodeType === 3 &&
    checkBoxCurrentBook.value &&
    checkBoxCurrentBook.value.childNodeList
  ) {
    calcCheckedNodeId(node, node.checked);
    if (node.checked) {
      lasCheckWordId.value = node.nodeId;
    } else {
      lasCheckWordId.value = "";
    }
  }

  emit("selectUnit", selectUnitIdArray.value);
};

/**
 *
 * @param node 获取上课单词的信息
 */
const getCourseWordInfo = (node: BookTree) => {
  return (
    node.textbookId +
    ";" +
    node.parentNodeId +
    ";" +
    node.wordId +
    ";" +
    node.nodeId
  );
};

const calcCheckedNodeId = (nodeInfo: BookTree, checked: boolean) => {
  if (checked) {
    // 选中时添加ID
    selectUnitIdArray.value.push(nodeInfo);
  } else {
    // 取消选中时移除ID
    const index = selectUnitIdArray.value.findIndex(
      (item) => item.nodeId === nodeInfo.nodeId
    );
    if (index !== -1) {
      selectUnitIdArray.value.splice(index, 1);
    }
  }
};

const clearSelectedNode = () => {
  selectUnitIdArray.value.forEach((item) => {
    item.checked = false;
  });
  selectUnitIdArray.value = [];
};

const selectUnitIdArray = ref<BookTree[]>([]);
const dynamicTags = ref<string[]>([]);
const treeData = ref<BookTree[]>([]);
const loading = ref(false);
const currentNodeIndex = ref<string>();
const defaultOpeneds = ref<string[]>([]);

const tabSelect = ref<string[]>([]);
const tabs = [
  { label: "学校教材", value: "1" },
  { label: "特色教材", value: "2" },
  { label: "个性化教材", value: "3" },
];

// 搜索词选项数据
const searchOptions = ref<any[]>([]);
const onChangeMaterial = () => {
  textbookTreeParam.value.tags = dynamicTags.value;
  textbookTreeParam.value.nodeId = "";
  textbookTreeParam.value.nodeType = -1;
  refreshTreeData();
};

const toggleTabSelect = (bookType: string) => {
  const index = tabSelect.value.indexOf(bookType);
  if (index === -1) {
    tabSelect.value.push(bookType);
  } else {
    tabSelect.value.splice(index, 1);
  }
  textbookTreeParam.value.nodeId = "";
  textbookTreeParam.value.nodeType = -1;
  textbookTreeParam.value.types = tabSelect.value;

  refreshTreeData();
};

const refreshTreeData = async () => {
  if (tabSelect.value.length == 0 && dynamicTags.value.length == 0) {
    treeData.value = originalTreeData.value;
  } else {
    treeData.value = originalTreeData.value.filter((item) => {
      let isMatch = true;
      if (tabSelect.value.length > 0) {
        isMatch = isMatch && tabSelect.value.includes(item.textbookType);
      }

      if (dynamicTags.value.length > 0) {
        isMatch = isMatch && dynamicTags.value.includes(item.nodeName);
      }
      return isMatch;
    });
  }

  if (currentUnit.value) {
    currentUnit.value.checked = false;
  }

  if (checkBoxCurrentUnit.value) {
    checkBoxCurrentUnit.value.checked = false;
  }

  clearSelectedNode();
  emit("selectUnit", selectUnitIdArray.value);
};

const currentLastWord = ref<LastWordInfo>();
const lastWordMap = ref<Map<string, LastWordInfo>>(new Map());
const lasCheckWordId = ref<string>();

onMounted(async () => {
  // 初始化数据
  await fetchTreeData();
  treeData.value = originalTreeData.value;
});

const getLastWordInfo = (textbookId: string) => {
  getLastTextbookId(props.studentId || "", textbookId).then((res) => {
    if (res.code == 200) {
      const data = res.data as LastWordInfo;
      if (data.textbookId) {
        lastWordMap.value.set(data.textbookId, data);

        if (!textbookId) {
          setTimeout(() => {
            scrollToTextbook(data.textbookId);
          }, 300);
        } else {
          processLastWordNext(data.textbookId);
        }
      }
    }
  });
};

const processLastWordNext = (textbookId: string) => {
  let data = lastWordMap.value.get(textbookId) as LastWordInfo;
  if (!data || !data.textbookId) {
    currentLastWord.value = undefined;
    return;
  }

  const book = treeData.value.find((item) => item.nodeId == data.textbookId);
  if (!book) {
    currentLastWord.value = undefined;
    return;
  }
  currentLastWord.value = data;
  // defaultOpeneds.value = [
  //   data.textbookId + ";" + "" + ";" + data.textbookId + ";" + "1",
  // ];
  // if(data.unitItemId !== '0'){
  //   defaultOpeneds.value.push(
  //     data.textbookId + ";" + data.textbookId + ";" + data.unitItemId + ";" + "2"
  //   );
  // }

  currentNodeIndex.value =
    data.textbookId +
    ";" +
    data.unitItemId +
    ";" +
    data.wordItemId +
    ";" +
    "3" +
    ";" +
    data.word;

  setTimeout(() => {
    scrollToWord(data.wordItemId);
  }, 800);
};

const textbookRef = ref({});

function setTextbookRef(textbookId, el) {
  if (el) {
    textbookRef.value[textbookId] = el;
  }
}

async function scrollToTextbook(textbookId) {
  console.log("滚动到目标单词：", textbookId);
  await nextTick(); // 等待 DOM 更新

  const el = textbookRef.value[textbookId];
  console.log("滚动到目标单词：", textbookId, el);
  if (el?.$el) {
    // el-menu-item 是组件实例，需要访问 .$el 获取真实 DOM
    el.$el.scrollIntoView({ behavior: "smooth", block: "center" });
  }
}

const wordRefs = ref({}); // 存储 wordId -> element 映射

// 收集每个 word 的 DOM 引用
function setWordRef(wordId, el) {
  if (el) {
    wordRefs.value[wordId] = el;
  }
}

async function scrollToWord(wordId) {
  console.log("滚动到目标单词：", wordId);
  await nextTick(); // 等待 DOM 更新

  const el = wordRefs.value[wordId];
  console.log("滚动到目标单词：", wordId, el);
  if (el?.$el) {
    // el-menu-item 是组件实例，需要访问 .$el 获取真实 DOM
    el.$el.scrollIntoView({ behavior: "smooth", block: "center" });
  }
}

const checkWord = (num: number) => {
  clearSelectedNode();
  const unitCount = currentBook.value?.childNodeList?.filter(x=> x.nodeType == 2)?.length
  let target = false;
  let isSelectedWord = !!lasCheckWordId.value;
  let count = 0;
  if (
    !currentLastWord.value?.textbookId ||
    (currentLastWord.value?.unitItemId !== "0" &&
      currentLastWord.value?.unitItemId !== currentUnit.value?.nodeId)
  ) {
    // if (currentBook.value?.textbookType !== "1") {
    if (unitCount == 0) {
      currentBook.value?.childNodeList?.forEach((item) => {
        if (item.nodeId === lasCheckWordId.value) {
          target = true;
        }
        if ((!isSelectedWord || target) && count < num) {
          item.checked = true;
          count++;
          calcCheckedNodeId(item, item.checked);
        }
      });
    } else {
      if (currentUnit.value?.nodeId) {
        currentUnit.value?.childNodeList?.forEach((item) => {
          if (item.nodeId === lasCheckWordId.value) {
            target = true;
          }

          if ((!isSelectedWord || target) && count < num) {
            item.checked = true;
            count++;
            calcCheckedNodeId(item, item.checked);
          }
        });
      } else {
        ElMessage.error("请选择单元");
      }
    }

    emit("selectUnit", selectUnitIdArray.value);
    return;
  }
  const book = treeData.value.find(
    (item) => item.textbookId == currentLastWord.value?.textbookId
  );

  let wordNodeList = book?.childNodeList;

  if (currentLastWord.value?.unitItemId) {
    book?.childNodeList?.forEach((item) => {
      if (item.nodeId == currentLastWord.value?.unitItemId) {
        wordNodeList = item.childNodeList;
      }
    });
  }

  wordNodeList?.forEach((item) => {
    if (isSelectedWord && item.nodeId === lasCheckWordId.value) {
      target = true;
    }

    if (!isSelectedWord && item.nodeId == currentLastWord.value?.wordItemId) {
      target = true;
    }

    if (target && count < num) {
      item.checked = true;
      count++;
      calcCheckedNodeId(item, item.checked);
    }

    emit("selectUnit", selectUnitIdArray.value);
  });
};

watch(
  () => props.refreshTree,
  async (newVal) => {
    textbookTreeParam.value.nodeId = "";
    textbookTreeParam.value.nodeType = -1;
    await fetchTreeData();
    treeData.value = originalTreeData.value;
  }
);

const originalTreeData = ref<BookTree[]>([]);

const fetchTreeData = async () => {
  loading.value = true;
  textbookTreeParam.value.searchType = props.openClass ? "上课" : "单词";
  textbookTreeParam.value.studentId = props.studentId;

  const res = await getTextbookTree(textbookTreeParam.value);
  if (res.code === 200) {
    if (searchOptions.value.length == 0) {
      res.data.forEach((item: BookTree) => {
        searchOptions.value.push({
          value: item.nodeName,
          label: item.nodeName,
        });
      });
    }
    if (textbookTreeParam.value.nodeType == -1) {
      originalTreeData.value = [];
      originalTreeData.value.push(...res.data.map(normalizeNode));
    } else if (textbookTreeParam.value.nodeType == 1) {
      const childNode = originalTreeData.value.find(
        (item) => item.nodeId == textbookTreeParam.value.nodeId
      ) as BookTree;
      childNode.childNodeList = [];
      childNode.childNodeList.push(...res.data.map(normalizeNode));
    } else if (textbookTreeParam.value.nodeType == 2) {
      originalTreeData.value.forEach((item: BookTree) => {
        const childNode = item.childNodeList?.find(
          (item1) => item1.nodeId == textbookTreeParam.value.nodeId
        ) as BookTree;
        if (!childNode) {
          return;
        }
        childNode.childNodeList = [];
        childNode.childNodeList.push(...res.data.map(normalizeNode));
        console.log(childNode)

        if (
          props.openClass &&
          childNode.checked &&
          childNode.childNodeList.length > 0
        ) {
          childNode.childNodeList.forEach((item) => {
            item.checked = true;
          });
        }
        console.log("树节点刷新完啦");
      });
    }

    if (props.openClass && [-1, 1].includes(textbookTreeParam.value.nodeType)) {
      getLastWordInfo(textbookTreeParam.value.nodeId || "");
    }
  }

  loading.value = false;
};
</script>
<style scoped>
.material-menu {
  overflow-y: auto;
  border-right: none;
  --el-menu-active-color: #6cdaf5;
}

.material-menu-height {
  height: calc(100vh - 75px - 89px);
}

.material-menu-height-course {
  height: calc(100vh - 75px - 89px - 60px);
}

.tree-contains {
  height: 100%;
  overflow-y: hidden;
}

.search-zone-class {
  height: 75px;
}
.add-tag {
  margin-bottom: 5px;
  max-height: 100px;
  overflow-y: auto;
  overflow-x: hidden;
}

.material-tabs {
  display: flex;
  gap: 10px;
}

.material-tab-item {
  position: relative;
  padding: 0px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.f-16 {
  font-size: 18px;
  min-height: 32px;
  background-color: #0008ff26;
}

.f-12 {
  font-size: 12px;
  height: 32px;
  background-color: #f5f7fa;
}

.material-tab-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  border-radius: 2px 0 0 2px;
}

.material-tab-item[data-color="1"]::before {
  background-color: #f56c6c; /* 红色 */
}

.material-tab-item[data-color="2"]::before {
  background-color: #e6a23c; /* 黄色 */
}

.material-tab-item[data-color="3"]::before {
  background-color: #409eff; /* 蓝色 */
}

.material-tab-item.active {
  background-color: #1378e9;
  color: rgb(248, 237, 237);
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 菜单样式 */
.material-menu .el-sub-menu {
  position: relative;
}

/* 根据教材类型添加左侧竖线 */
.material-menu .el-sub-menu[data-type="1"]::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  height: 90%;
  background-color: #f56c6c;
}

.material-menu .el-sub-menu[data-type="2"]::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  height: 90%;
  background-color: #e6a23c; /* 黄色 - 公司教材 */
}

.material-menu .el-sub-menu[data-type="3"]::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  height: 90%;
  background-color: #409eff; /* 蓝色 - 特色教材 */
}

/* 父级菜单选中状态样式 */
.material-menu :deep(.el-sub-menu.is-parent-active) > .el-sub-menu__title {
  color: #409eff !important;
  background-color: #ecf5ff !important;
  font-weight: bold;
}

.material-menu :deep(.el-sub-menu.is-parent-active) > .el-sub-menu__title i {
  color: #409eff !important;
}

/* 父级菜单右侧指示器 */
.material-menu
  :deep(.el-sub-menu.is-parent-active)
  > .el-sub-menu__title::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  /* background-color: #409eff; */
  border-radius: 2px;
}
</style>
