<template>
  <div class="elementary-word-card">
    <!-- 单词标签切换按钮组 -->
    <!-- 装饰元素 -->
    <div class="decoration-element pencil"></div>
    <div class="decoration-element book"></div>
    <div class="word-header">
      <div style="display: flex; align-items: center">
        <span class="ellipsis word-class">{{ props.item.word }}</span>
      </div>
    </div>
    <el-divider style="margin: 12px 0" />
    <el-tooltip effect="dark" placement="top" :disabled="posStr.length <= 1">
      <template #content>
        <div v-for="(line, index) in posStr" :key="index">
          {{ line.pos }}.{{ line.def }}
        </div>
      </template>
      <div class="ellipsis def">
        <div v-for="(line, index) in posStr" :key="index">
          {{ line.pos }}.{{ line.def }}
        </div>
      </div>
    </el-tooltip>

    <el-form label-width="50px" label-position="left">
      <div class="word-info">
        <el-form-item label="英" class="word-info-item">
          <div class="item-content-class">
            <div class="ellipsis mr-5">{{ props.item.phoneticUk }}</div>
            <el-icon
              @click="playAudio(props.item.audioUkUrl)"
              v-if="props.item.audioUkUrl"
              class="iconClass"
              style="color: #ff9800; width: 1.2em; height: 1.2em"
            >
              <Headset />
            </el-icon>
          </div>
        </el-form-item>
        <el-form-item label="美" class="word-info-item">
          <div class="item-content-class">
            <div class="ellipsis mr-5">{{ props.item.phoneticUs }}</div>
            <el-icon
              @click="playAudio(props.item.audioUsUrl)"
              v-if="props.item.audioUsUrl"
              class="iconClass"
              style="color: #ff9800; width: 1.2em; height: 1.2em"
            >
              <Headset />
            </el-icon>
          </div>
        </el-form-item>

        <el-form-item label="例句" class="word-info-item">
          <div>
            <div class="item-content-class">
              <el-tooltip
                effect="dark"
                :content="sentenceEnContent"
                placement="top"
                :disabled="sentenceEnContent && sentenceEnContent.length < 16"
              >
                <div class="ellipsis">
                  {{ sentenceEnContent }}
                </div>
              </el-tooltip>
              <el-icon
                v-if="sentenceAudioUsUrl"
                @click="playAudio(sentenceAudioUsUrl)"
                class="iconClass"
                style="color: #ff9800; width: 1.2em; height: 1.2em"
              >
                <Headset />
              </el-icon>
            </div>
            <!-- <el-divider style="margin: 1px 0;"/> -->
            <el-tooltip
              effect="dark"
              :content="sentenceCnContent"
              placement="top"
              :disabled="sentenceCnContent && sentenceCnContent?.length < 16"
            >
              <div class="ellipsis fill-height">
                {{ sentenceCnContent || "\u00A0" }}
              </div>
            </el-tooltip>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <!-- 底部操作区域 -->
    <div class="card-footer">
      <div class="difficulty-indicator">
        <div class="difficulty-label">难度：</div>
        <div
          class="difficulty-level"
          :class="{
            'difficulty-low': [1, 2].includes(props.item.difficulty || 0),
            'difficulty-medium': [3].includes(props.item.difficulty || 0),
            'difficulty-high': [4, 5].includes(props.item.difficulty || 0),
          }"
        >
          {{ getDifficultyText(props.item.difficulty || 0) }}
        </div>
      </div>
      <div class="footer-buttons">
        <el-icon
          class="play-icon"
          @click="playVideo(sentenceVideoContent)"
          v-if="sentenceVideoContent"
          size="2em"
          style="margin-top: 5px; color: #42b983; cursor: pointer"
        >
          <VideoCamera />
        </el-icon>
        <el-icon class="edit-icon" @click="openEditDialog('view')"
          ><ZoomIn
        /></el-icon>
        <el-icon
          class="edit-icon"
          @click="openEditDialog('edit')"
          style="width: 1.5em; height: 1.5em"
          ><Edit
        /></el-icon>
      </div>
    </div>

    <!-- 视频播放对话框 -->
    <el-dialog
      v-model="videoDialogVisible"
      title="单词视频"
      width="800px"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      @closed="currentPlayVideoUrl = ''"
      center
    >
      <div style="display: flex; justify-content: center; align-items: center">
        <video
          v-if="currentPlayVideoUrl"
          :src="currentPlayVideoUrl"
          controls
          autoplay
          style="max-width: 100%; max-height: 70vh"
        ></video>
      </div>
    </el-dialog>

    <el-dialog
      v-model="openZoomVisible"
      title="查看单词信息"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="resetForm"
      class="word-detail-dialog"
    >
      <!-- <template #header>
        <div class="dialog-header-content">
          <span class="dialog-title">{{ wordTab.length > 1 ? '查看单词信息 - ' + activeWordTab : '查看单词信息' }}</span>
        </div>
      </template> -->
      <div class="word-detail-content">
        <div class="word-tabs-buttons" v-if="wordTab.length > 1">
          <div class="tab-icon-container">
            <div
              class="tab-icon-button"
              :class="{ active: wordTab[0] === activeWordTab }"
              @click="handleWordTabClick(wordTab[0])"
            >
              <div class="tab-icon-content">
                <!-- <i class="tab-icon">📚</i> -->
                <span class="tab-icon-label">{{ wordTab[0] }}</span>
              </div>
            </div>
            <div
              class="tab-icon-button"
              :class="{ active: wordTab[1] === activeWordTab }"
              @click="handleWordTabClick(wordTab[1])"
            >
              <div class="tab-icon-content">
                <!-- <i class="tab-icon">🌟</i> -->
                <span class="tab-icon-label">{{ wordTab[1] }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 装饰元素 -->
        <div class="detail-decoration pencil"></div>
        <div class="detail-decoration book"></div>
        <div class="detail-decoration star"></div>

        <el-form label-width="100px" label-position="left">
          <!-- 单词和难度部分 -->
          <div class="detail-header">
            <div class="detail-section word-section">
              <el-form-item class="word-info-item">
                <template #label>
                  <span class="detail-label">单词</span>
                </template>
                <div class="word-display">
                  <span class="word-text">{{ props.item.word }}</span>
                </div>
              </el-form-item>
            </div>

            <div class="detail-section difficulty-section">
              <el-form-item class="word-info-item">
                <template #label>
                  <span class="detail-label">难度等级</span>
                </template>
                <div class="difficulty-display">
                  <div class="difficulty-stars">
                    <span
                      v-for="i in 5"
                      :key="i"
                      :class="[
                        'difficulty-star',
                        i <= (props.item.difficulty || 1) ? 'active' : '',
                      ]"
                      >★</span
                    >
                  </div>
                  <span class="difficulty-text">{{
                    getDifficultyText(props.item.difficulty || 1)
                  }}</span>
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 音节部分 -->
          <div class="detail-section syllables-section">
            <el-form-item class="word-info-item">
              <template #label>
                <span class="detail-label">音节</span>
              </template>
              <div class="syllables-display">{{ props.item.syllables }}</div>
            </el-form-item>
          </div>

          <!-- 发音部分 -->
          <div class="detail-pronunciation-container">
            <div class="detail-section uk-pronunciation-section">
              <el-form-item class="word-info-item">
                <template #label>
                  <span class="detail-label">英式发音</span>
                </template>
                <div class="pronunciation-display">
                  <div class="phonetic-text">{{ props.item.phoneticUk }}</div>
                  <el-icon
                    @click="playAudio(props.item.audioUkUrl)"
                    v-if="props.item.audioUkUrl"
                    class="audio-icon"
                  >
                    <Headset />
                  </el-icon>
                </div>
              </el-form-item>
            </div>

            <div class="detail-section us-pronunciation-section">
              <el-form-item class="word-info-item">
                <template #label>
                  <span class="detail-label">美式发音</span>
                </template>
                <div class="pronunciation-display">
                  <div class="phonetic-text">{{ props.item.phoneticUs }}</div>
                  <el-icon
                    @click="playAudio(props.item.audioUsUrl)"
                    v-if="props.item.audioUsUrl"
                    class="audio-icon"
                  >
                    <Headset />
                  </el-icon>
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 视频部分 -->
          <div class="detail-section video-section" v-if="currentVideoUrl">
            <el-form-item class="word-info-item">
              <template #label>
                <span class="detail-label">单词视频</span>
              </template>
              <div class="video-display">
                <div class="video-button" @click="playVideo(currentVideoUrl)">
                  <el-icon class="video-icon"><VideoCamera /></el-icon>
                  <span class="video-text">观看单词视频</span>
                </div>
              </div>
            </el-form-item>
          </div>
          <!-- 释义部分 -->
          <div class="detail-section meaning-section">
            <el-form-item class="word-info-item">
              <template #label>
                <span class="detail-label">单词释义</span>
              </template>
              <div class="meaning-display">
                <div v-for="(line, index) in currentMeanings.pos" :key="index">
                  {{ line.pos }}.{{ line.def }}
                </div>
              </div>
            </el-form-item>
          </div>

          <!-- 例句部分 -->
          <div class="detail-section sentence-section">
            <el-form-item class="word-info-item">
              <template #label>
                <div style="display: flex; align-items: center">
                  <span class="detail-label">例句</span>
                </div>
              </template>
              <div class="sentences-container">
                <div
                  v-for="(item, index) in currentSentences"
                  :key="item.sentenceEn"
                  class="sentence-item"
                  :style="{
                    width: '100%',
                  }"
                >
                  <div style="display: flex">
                    <div class="sentence-number">{{ index + 1 }}</div>
                    <div
                      v-if="item.stage"
                      :class="[
                        'stage-class',
                        {
                          'little-stage': item.stage === '小学',
                          'middle-stage': item.stage === '初中',
                          'high-stage': item.stage === '高中',
                        },
                      ]"
                    >
                      {{ item.stage }}
                    </div>
                  </div>
                  <div class="sentence-content">
                    <div class="sentence-en">{{ item.sentenceEn }}</div>
                    <div class="sentence-cn">{{ item.sentenceCn }}</div>
                    <div class="sentence-audio">
                      <template v-if="item.audioUkUrl">
                        <span class="audio-label">英音</span>
                        <el-icon
                          @click="playAudio(item.audioUkUrl)"
                          class="audio-icon"
                        >
                          <Headset />
                        </el-icon>
                      </template>

                      <template v-if="item.audioUsUrl">
                        <span class="audio-label">美音</span>
                        <el-icon
                          @click="playAudio(item.audioUsUrl)"
                          class="audio-icon"
                        >
                          <Headset />
                        </el-icon>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <el-dialog
      v-model="editWordVisible"
      title="编辑单词信息"
      width="60%"
      height="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="resetForm"
    >
      <div style="height: 600px; overflow-y: auto">
        <el-form :model="editForm" label-width="90px" label-position="left">
          <el-form-item class="word-info-item">
            <template #label>
              <span style="font-size: 14px">单词：</span>
            </template>
            <div>{{ props.item.word }}</div>
          </el-form-item>

          <el-form-item label="音节" class="word-info-item">
            <div style="display: flex; align-items: center; width: 200px">
              <el-input
                v-model="editForm.syllables"
                type="textarea"
                placeholder="请输入单词音节"
              ></el-input>
            </div>
          </el-form-item>

          <el-form-item label="音标(美)：" class="word-info-item">
            <div style="display: flex; align-items: center">
              <div style="width: 200px">
                <el-input
                  v-model="editForm.phoneticUs"
                  placeholder="请输入美式音标"
                ></el-input>
              </div>

              <el-icon
                @click="playAudio(props.item.audioUsUrl)"
                v-if="props.item.audioUsUrl"
                class="iconClass iconClassEdit"
                style="color: #ff9800; width: 1.2em; height: 1.2em"
              >
                <Headset />
              </el-icon>
              <div style="display: flex; align-items: center; margin-left: 5px">
                <el-upload
                  class="upload-demo"
                  action
                  :show-file-list="false"
                  :on-change="(file:UploadFile) => beforeAudioUpload(file, 'audioUs')"
                  :on-exceed="(files:UploadFile[]) => handleExceed(files, 'audioUs')"
                  :auto-upload="false"
                  :limit="1"
                  accept="audio/*"
                >
                  <el-button type="primary">上传音频</el-button>
                </el-upload>
                <span style="color: red; margin-left: 10px">{{
                  audioUsFile?.name
                }}</span>
                <el-icon
                  style="margin-left: 2px; cursor: pointer"
                  @click="audioUsFile = null"
                  v-if="audioUsFile?.name"
                  ><DeleteFilled
                /></el-icon>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="音标(英)：" class="word-info-item">
            <div style="display: flex; align-items: center">
              <div style="width: 200px">
                <el-input
                  v-model="editForm.phoneticUk"
                  placeholder="请输入英式音标"
                ></el-input>
              </div>
              <el-icon
                @click="playAudio(props.item.audioUkUrl)"
                v-if="props.item.audioUkUrl"
                class="iconClass iconClassEdit"
                style="color: #ff9800; width: 1.2em; height: 1.2em"
              >
                <Headset />
              </el-icon>
              <div style="display: flex; align-items: center; margin-left: 5px">
                <el-upload
                  class="upload-demo"
                  action
                  :show-file-list="false"
                  :on-change="(file:UploadFile) => beforeAudioUpload(file, 'audioUk')"
                  :on-exceed="(files:UploadFile[]) => handleExceed(files, 'audioUk')"
                  :auto-upload="false"
                  :limit="1"
                  accept="audio/*"
                >
                  <el-button type="primary">上传音频</el-button>
                </el-upload>
                <span style="color: red; margin-left: 10px">{{
                  audioUkFile?.name
                }}</span>
                <el-icon
                  style="margin-left: 2px; cursor: pointer"
                  @click="audioUkFile = null"
                  v-if="audioUkFile?.name"
                  ><DeleteFilled
                /></el-icon>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="难度：" class="word-info-item">
            <div style="display: flex; align-items: center; width: 200px">
              <el-select
                v-model="editForm.difficulty"
                class="w-20"
                default-first-option
                placeholder="请选择"
              >
                <el-option
                  v-for="item in difficultyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-form-item>

          <el-tabs
            type="border-card"
            v-model="activeWordTab"
            class="demo-tabs"
            style="width: 90%; margin-bottom: 15px"
            v-if="wordTab.length > 1"
            @tab-change="handleWordTabClick"
          >
            <el-tab-pane
              v-for="tab in wordTab"
              :label="tab"
              :name="tab"
              :key="tab"
            >
              <el-form-item label="视频：" class="word-info-item">
                <div style="display: flex; align-items: center">
                  <el-icon
                    @click="playVideo(currentVideoUrl)"
                    style="
                      margin-left: 10px;
                      margin-right: 20px;
                      cursor: pointer;
                      color: #42b983;
                    "
                    v-if="currentVideoUrl"
                    size="2em"
                    ><VideoCamera
                  /></el-icon>
                  <div style="display: flex; align-items: center">
                    <el-upload
                      class="upload-demo"
                      action="#"
                      :show-file-list="false"
                      :on-change="(uploadFile: UploadFile) => beforeAudioUpload(uploadFile, 'video')"
                      :on-exceed="(files:UploadFile[]) => handleExceed(files, 'video')"
                      :auto-upload="false"
                      :limit="1"
                      accept="video/*"
                    >
                      <el-button type="primary">上传视频</el-button>
                    </el-upload>
                    <span style="color: red; margin-left: 10px">{{
                      editForm.videoFiles[activeWordTab]?.name
                    }}</span>
                    <el-icon
                      style="margin-left: 2px; cursor: pointer"
                      @click="editForm.videoFiles[activeWordTab] = null"
                      v-if="editForm.videoFiles[activeWordTab]?.name"
                      ><DeleteFilled
                    /></el-icon>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="" class="word-info-item">
                <span style="display: flex; align-items: center; width: 100%">
                  <span style="color: red; margin-left: 10px"
                    >* 一行为一个释义，以 . 分隔词性</span
                  ></span
                >
              </el-form-item>
              <el-form-item label="单词释义：" class="word-info-item">
                <div style="display: flex; align-items: center; width: 90%">
                  <el-input
                    v-model="editForm.meanings[activeWordTab].posStr"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入单词释义"
                    width="100%"
                  ></el-input>
                </div>
              </el-form-item>

              <el-form-item label="单词测验：" class="word-info-item">
                <div style="display: flex; align-items: center; width: 90%">
                  <el-input
                    v-model="editForm.meanings[activeWordTab].practicesStr"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入单词测验答案"
                  ></el-input>
                </div>
              </el-form-item>
              <el-form-item label="例句：" class="word-info-item">
                <div style="width: 90%">
                  <div
                    v-for="(sentence, index) in editForm.sentences[
                      activeWordTab
                    ]"
                    :key="index"
                    style="
                      border: 1px dashed yellowgreen;
                      border-radius: 8px;
                      padding: 8px;
                      margin-bottom: 8px;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        flex-direction: column;
                        margin-bottom: 10px;
                      "
                    >
                      <div
                        style="display: flex; justify-content: space-between"
                      >
                        <span><span class="sen-tip-class">阶段</span></span>
                        <el-button
                          type="danger"
                          v-if="index != 0"
                          @click="deleteSentence(index)"
                          >删除</el-button
                        >
                      </div>

                      <el-select v-model="sentence.stage" style="width: 425px">
                        <el-option
                          v-for="item in stageOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </div>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 10px;
                      "
                    >
                      <div style="display: flex; align-items: center">
                        <span><span class="sen-tip-class">例句</span></span>
                        <span
                          style="margin-left: 5px"
                          v-if="sentence.audioUsUrl"
                          >美：</span
                        >
                        <el-icon
                          @click="playAudio(sentence.audioUsUrl)"
                          v-if="sentence.audioUsUrl"
                          class="iconClass iconClassEdit"
                          style="color: #ff9800; width: 1.2em; height: 1.2em"
                        >
                          <Headset />
                        </el-icon>
                        <span v-if="sentence.audioUkUrl">英：</span>
                        <el-icon
                          @click="playAudio(sentence.audioUkUrl)"
                          v-if="sentence.audioUkUrl"
                          class="iconClass iconClassEdit"
                          style="color: #ff9800; width: 1.2em; height: 1.2em"
                        >
                          <Headset />
                        </el-icon>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            margin-left: 10px;
                          "
                        >
                          <el-upload
                            class="upload-demo"
                            action
                            :show-file-list="false"
                            :on-change="(file:UploadFile) => (sentence.audioUsFile = file)"
                            :on-exceed="(files:UploadFile[]) => (sentence.audioUsFile = files[0])"
                            :auto-upload="false"
                            :limit="1"
                            accept="audio/*"
                          >
                            <el-button type="primary">上传音频(美)</el-button>
                          </el-upload>
                          <span style="color: red; margin-left: 10px">{{
                            sentence.audioUsFile?.name
                          }}</span>
                          <el-icon
                            style="margin-left: 2px; cursor: pointer"
                            @click="sentence.audioUsFile = null"
                            v-if="sentence.audioUsFile?.name"
                            ><DeleteFilled
                          /></el-icon>
                        </div>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            margin-left: 5px;
                          "
                        >
                          <el-upload
                            class="upload-demo"
                            action
                            :show-file-list="false"
                            :on-change="(file:UploadFile) => (sentence.audioUkFile = file)"
                            :on-exceed="(files:UploadFile[]) => (sentence.audioUkFile = files[0])"
                            :auto-upload="false"
                            :limit="1"
                            accept="audio/*"
                          >
                            <el-button type="primary">上传音频(英)</el-button>
                          </el-upload>
                          <span style="color: red; margin-left: 10px">{{
                            sentence.audioUkFile?.name
                          }}</span>
                          <el-icon
                            style="margin-left: 2px; cursor: pointer"
                            @click="sentence.audioUkFile = null"
                            v-if="sentence.audioUkFile?.name"
                            ><DeleteFilled
                          /></el-icon>
                        </div>
                      </div>
                    </div>

                    <el-input
                      v-model="sentence.sentenceEn"
                      type="textarea"
                      placeholder="请输入例句"
                    ></el-input>
                    <span class="sen-tip-class">释义</span>
                    <el-input
                      v-model="sentence.sentenceCn"
                      type="textarea"
                      placeholder="请输入例句中文释义"
                    ></el-input>
                    <span style="width: 10%"
                      ><span class="sen-tip-class">例句测验</span>
                      <span style="color: red; margin-left: 10px"
                        >* 一行为一个例句测验答案</span
                      ></span
                    >
                    <el-input
                      v-model="sentence.practicesStr"
                      type="textarea"
                      rows="3"
                      placeholder="请输入例句测验选项"
                    ></el-input>
                    <span style="width: 10%"
                      ><span class="sen-tip-class">排序</span>
                      <span style="color: red; margin-left: 10px"
                        >* 以 | 分隔例句</span
                      ></span
                    >
                    <el-input
                      v-model="sentence.structurePartsEnStr"
                      type="textarea"
                      :rows="2"
                      placeholder="请对例句进行排序"
                    ></el-input>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addSentence(activeWordTab)"
                  >新增例句</el-button
                >
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
          <div v-else>
            <el-form-item label="视频：" class="word-info-item">
              <div style="display: flex; align-items: center">
                <el-icon
                  @click="playVideo(currentVideoUrl)"
                  style="
                    margin-left: 10px;
                    margin-right: 10px;
                    cursor: pointer;
                    color: #42b983;
                  "
                  v-if="currentVideoUrl"
                  size="2em"
                  ><VideoCamera
                /></el-icon>
                <div style="display: flex; align-items: center">
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :show-file-list="false"
                    :on-change="(uploadFile: UploadFile) => beforeAudioUpload(uploadFile, 'video')"
                    :on-exceed="(files:UploadFile[]) => handleExceed(files, 'video')"
                    :auto-upload="false"
                    :limit="1"
                    accept="video/*"
                  >
                    <el-button type="primary">上传视频</el-button>
                  </el-upload>
                  <span style="color: red; margin-left: 10px">{{
                    editForm.videoFiles[activeWordTab]?.name
                  }}</span>
                  <el-icon
                    style="margin-left: 2px; cursor: pointer"
                    @click="videoFile = null"
                    v-if="editForm.videoFiles[activeWordTab]?.name"
                    ><DeleteFilled
                  /></el-icon>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="" class="word-info-item">
              <span style="display: flex; align-items: center; width: 100%">
                <span style="color: red; margin-left: 10px"
                  >* 一行为一个释义，以 . 分隔词性</span
                ></span
              >
            </el-form-item>
            <el-form-item label="单词释义：" class="word-info-item">
              <div style="display: flex; align-items: center; width: 90%">
                <el-input
                  v-model="editForm.meanings[activeWordTab].posStr"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入单词释义"
                  width="100%"
                ></el-input>
              </div>
            </el-form-item>

            <el-form-item label="单词测验：" class="word-info-item">
              <div style="display: flex; align-items: center; width: 90%">
                <el-input
                  v-model="editForm.meanings[activeWordTab].practicesStr"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入单词测验答案"
                ></el-input>
              </div>
            </el-form-item>
            <el-form-item label="例句：" class="word-info-item">
              <div style="width: 90%">
                <div
                  v-for="(sentence, index) in editForm.sentences[activeWordTab]"
                  :key="index"
                  style="
                    border: 1px dashed yellowgreen;
                    border-radius: 8px;
                    padding: 8px;
                    margin-bottom: 8px;
                  "
                >
                  <div
                    style="
                      display: flex;
                      flex-direction: column;
                      margin-bottom: 10px;
                    "
                  >
                    <div style="display: flex; justify-content: space-between">
                      <span><span class="sen-tip-class">阶段</span></span>
                      <el-button
                        type="danger"
                        v-if="index != 0"
                        @click="deleteSentence(index)"
                        >删除</el-button
                      >
                    </div>

                    <el-select v-model="sentence.stage" style="width: 425px">
                      <el-option
                        v-for="item in stageOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      margin-bottom: 10px;
                    "
                  >
                    <div style="display: flex; align-items: center">
                      <span><span class="sen-tip-class">例句</span></span>
                      <span style="margin-left: 5px" v-if="sentence.audioUsUrl"
                        >美：</span
                      >
                      <el-icon
                        @click="playAudio(sentence.audioUsUrl)"
                        v-if="sentence.audioUsUrl"
                        class="iconClass iconClassEdit"
                        style="color: #ff9800; width: 1.2em; height: 1.2em"
                      >
                        <Headset />
                      </el-icon>
                      <span v-if="sentence.audioUkUrl">英：</span>
                      <el-icon
                        @click="playAudio(sentence.audioUkUrl)"
                        v-if="sentence.audioUkUrl"
                        class="iconClass iconClassEdit"
                        style="color: #ff9800; width: 1.2em; height: 1.2em"
                      >
                        <Headset />
                      </el-icon>
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          margin-left: 10px;
                        "
                      >
                        <el-upload
                          class="upload-demo"
                          action
                          :show-file-list="false"
                          :on-change="(file:UploadFile) => (sentence.audioUsFile = file)"
                          :on-exceed="(files:UploadFile[]) => (sentence.audioUsFile = files[0])"
                          :auto-upload="false"
                          :limit="1"
                          accept="audio/*"
                        >
                          <el-button type="primary">上传音频(美)</el-button>
                        </el-upload>
                        <span style="color: red; margin-left: 10px">{{
                          sentence.audioUsFile?.name
                        }}</span>
                        <el-icon
                          style="margin-left: 2px; cursor: pointer"
                          @click="sentence.audioUsFile = null"
                          v-if="sentence.audioUsFile?.name"
                          ><DeleteFilled
                        /></el-icon>
                      </div>
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          margin-left: 5px;
                        "
                      >
                        <el-upload
                          class="upload-demo"
                          action
                          :show-file-list="false"
                          :on-change="(file:UploadFile) => (sentence.audioUkFile = file)"
                          :on-exceed="(files:UploadFile[]) => (sentence.audioUkFile = files[0])"
                          :auto-upload="false"
                          :limit="1"
                          accept="audio/*"
                        >
                          <el-button type="primary">上传音频(英)</el-button>
                        </el-upload>
                        <span style="color: red; margin-left: 10px">{{
                          sentence.audioUkFile?.name
                        }}</span>
                        <el-icon
                          style="margin-left: 2px; cursor: pointer"
                          @click="sentence.audioUkFile = null"
                          v-if="sentence.audioUkFile?.name"
                          ><DeleteFilled
                        /></el-icon>
                      </div>
                    </div>
                  </div>

                  <el-input
                    v-model="sentence.sentenceEn"
                    type="textarea"
                    placeholder="请输入例句"
                  ></el-input>
                  <span class="sen-tip-class">释义</span>
                  <el-input
                    v-model="sentence.sentenceCn"
                    type="textarea"
                    placeholder="请输入例句中文释义"
                  ></el-input>
                  <span style="width: 10%"
                    ><span class="sen-tip-class">例句测验</span>
                    <span style="color: red; margin-left: 10px"
                      >* 一行为一个例句测验答案</span
                    ></span
                  >
                  <el-input
                    v-model="sentence.practicesStr"
                    type="textarea"
                    rows="3"
                    placeholder="请输入例句测验选项"
                  ></el-input>
                  <span style="width: 10%"
                    ><span class="sen-tip-class">排序</span>
                    <span style="color: red; margin-left: 10px"
                      >* 以 | 分隔例句</span
                    ></span
                  >
                  <el-input
                    v-model="sentence.structurePartsEnStr"
                    type="textarea"
                    :rows="2"
                    placeholder="请对例句进行排序"
                  ></el-input>
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addSentence(activeWordTab)"
                >新增例句</el-button
              >
            </el-form-item>
          </div>
          <el-form-item label="备注：" class="word-info-item">
            <div style="display: flex; align-items: center; width: 90%">
              <el-input
                v-model="editForm.changeDescription"
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
              ></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editWordVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="loading"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  Edit,
  ZoomIn,
  VideoCamera,
  Headset,
  DeleteFilled,
} from "@element-plus/icons-vue";
import {
  Word,
  Meanings,
  Sentences,
  editWord,
  uploadFile,
} from "../../../../api/word";
import { ref, reactive, computed, onBeforeUnmount } from "vue";
import { ElMessage, UploadFile } from "element-plus";
import { ITEM_RENDER_EVT } from "element-plus/es/components/virtual-list/src/defaults.mjs";
import { el, vi } from "element-plus/es/locales.mjs";

const editWordVisible = ref(false);
const openZoomVisible = ref(false);
const videoDialogVisible = ref(false);
const currentVideoUrl = ref("");
const currentPlayVideoUrl = ref("");
const loading = ref(false);
const props = defineProps<{
  item: Word;
}>();

const isShortSentence = (sentence: any) => {
  return sentence.sentenceEn.length < 50; // 可以根据实际需求调整长度阈值
};

// 获取难度等级文本
const getDifficultyText = (level: number) => {
  const difficultyMap = {
    1: "初级",
    2: "初级",
    3: "中级",
    4: "高级",
    5: "高级",
  };
  return difficultyMap[level as keyof typeof difficultyMap] || "未知";
};

const difficultyOptions = ref([
  { value: "1", label: "1级" },
  { value: "2", label: "2级" },
  { value: "3", label: "3级" },
  { value: "4", label: "4级" },
  { value: "5", label: "5级" },
]);

const stageOptions = ref([
  { value: "小学", label: "小学" },
  { value: "初中", label: "初中" },
  { value: "高中", label: "高中" },
]);

const editForm = ref({
  id: "",
  sentences: {} as any,
  phoneticUk: "" as string,
  phoneticUs: "" as string,
  meanings: {} as any,
  word: "" as string,
  syllables: "" as string,
  difficulty: "1",
  changeDescription: "",
  videoFiles: {} as any,
  videoFile: {} as any,
  audioUkUrl: "",
  audioUsUrl: "",
});

const audioUkFile = ref<UploadFile | null>(null);
const audioUsFile = ref<UploadFile | null>(null);
const videoFile = ref({} as any);

const emit = defineEmits<{
  (e: "updateWordList"): void;
}>();

const addSentence = (tabName) => {
  editForm.value.sentences[tabName].push({
    sentenceEn: "",
    sentenceCn: "",
    audioUkUrl: "",
    audioUsUrl: "",
    structurePartsEn: [],
    structurePartsEnStr: "",
    practices: [],
    practicesStr: "",
    audioUkFile: null,
    audioUsFile: null,
  });
};

const deleteSentence = (index: number) => {
  editForm.value.sentences[activeWordTab.value].splice(index, 1);
};

const wordTab = ref<string[]>([]);
const activeWordTab = ref<string>("");
const currentSentences = ref<Sentences[]>([]);
const currentMeanings = ref<Meanings>({} as any);

const handleWordTabClick = (tab: string) => {
  // 更新当前选中的标签
  activeWordTab.value = tab;

  // 更新内容
  currentSentences.value = props.item.sentences[tab] || [];
  currentMeanings.value = props.item.meanings[tab] || {};
  currentVideoUrl.value = props.item.videoUrl?.[tab] || "";
};

const openEditDialog = (type: string) => {
  const sentenceKeys = Object.keys(props.item.sentences || {});
  const meaningKeys = Object.keys(props.item.meanings || {});
  const videoKeys = Object.keys(props.item.videoUrl || {});

  wordTab.value = [...new Set([...sentenceKeys, ...meaningKeys, ...videoKeys])];
  wordTab.value.sort((a, b) => {
    if (a == "通用") {
      return -1;
    }
    if (b == "特色") {
      return 11;
    }
    return 0;
  });

  handleWordTabClick(wordTab.value[0]);

  if (type == "view") {
    openZoomVisible.value = true;
    editWordVisible.value = false;
    if (wordTab.value.length > 0) {
      activeWordTab.value = wordTab.value[0];
    }
  } else {
    editWordVisible.value = true;
    openZoomVisible.value = false;

    const clonedItem = JSON.parse(JSON.stringify(props.item));
    // 复制当前单词数据到编辑表单
    editForm.value.word = clonedItem.word || "";
    editForm.value.difficulty = clonedItem.difficulty + "" || "1";
    editForm.value.phoneticUs = clonedItem.phoneticUs || "";
    editForm.value.phoneticUk = clonedItem.phoneticUk || "";
    editForm.value.audioUkUrl = clonedItem.audioUkUrl || "";
    editForm.value.audioUsUrl = clonedItem.audioUsUrl || "";
    editForm.value.syllables = clonedItem.syllables || "";
    editForm.value.id = clonedItem.wordId || "";
    editForm.value.videoFile = clonedItem.videoUrl || {};

    wordTab.value.forEach((tabName: any) => {
      editForm.value.videoFiles[tabName] = null;
      editForm.value.meanings[tabName] = clonedItem.meanings[tabName] || {};
      editForm.value.meanings[tabName].posStr = clonedItem.meanings[
        tabName
      ].pos
        .map((item: any) => item.pos + "." + item.def)
        .join("\n");
      editForm.value.meanings[tabName].practicesStr =
        editForm.value.meanings[tabName]?.practices?.join("\n");

      editForm.value.sentences[tabName] = clonedItem.sentences[tabName] || [];
      editForm.value.sentences[tabName].map((item: any) => {
        item.structurePartsEnStr = item.structurePartsEn?.join("\n");
        item.practicesStr = item.practices?.join("\n");
      });

      if (
        !clonedItem.sentences[tabName] ||
        clonedItem.sentences[tabName].length == 0
      ) {
        addSentence(tabName);
      }
    });

    audioUkFile.value = null;
    audioUsFile.value = null;
    videoFile.value = null;
  }
};

const handleExceed = (files, type: string) => {
  console.log("上传文件111222：", audioUsFile.value);
  if (type == "audioUk") {
    audioUkFile.value = files[0];
  }

  if (type == "audioUs") {
    audioUsFile.value = files[0];
  }

  if (type == "video") {
    editForm.value.videoFiles[activeWordTab.value] = files[0];
  }
};

const beforeAudioUpload = (file: UploadFile, type: string) => {
  console.log("上传文件：", file, type);

  if (type == "audioUk") {
    audioUkFile.value = file;
  }

  if (type == "audioUs") {
    audioUsFile.value = file;
  }

  if (type == "video") {
    editForm.value.videoFiles[activeWordTab.value] = file;
  }
  return false;
};

const resetForm = () => {
  // 重置表单
  // editForm.value = {} as Word;
};

const getFileName = (file: any, key: string, index:number, i:number,type:string) => {
  if (file && file.raw instanceof File) {
    const ext = file.name.split(".").pop() || "mp3";
    return `${key}_sentence_${index}_${i}_${type}.${ext}`;
  }
  return "";
};

/**
 * 上传文件到OSS
 */
const uploadFileToOss = (wordId: string) => {
  const formData = new FormData();
  Object.entries(editForm.value.sentences).forEach(([key, value], index) => {
    const arr = value as any[];
    arr.forEach((s: any, i: number) => {
      const ukFileName = getFileName(s.audioUkFile, key, index, i,'audioUkFile');
      if (ukFileName) {
        const newFile = new File([s.audioUkFile.raw], ukFileName, {
          type: s.audioUkFile.raw.type,
        });
        formData.append("files", newFile);
      }

      const usFileName = getFileName(s.audioUsFile, key, index, i,'audioUsFile');
      if (usFileName) {
        const newFile = new File([s.audioUsFile.raw], usFileName, {
          type: s.audioUsFile.raw.type,
        });
        formData.append("files", newFile);
      }
    });
  });

  Object.entries(editForm.value.videoFiles).forEach(([key, value], index) => {
    const file = value as any;
    if (file && file.raw instanceof File) {
      const ext = file.name.split(".").pop() || "mp4";
      const newFile = new File([file.raw], `${key}_videoFile.${ext}`, {
        type: file.raw.type,
      });
      formData.append("files", newFile);
    }
  });

  if (audioUkFile.value?.raw) {
    const ext = audioUkFile.value.name.split(".").pop() || "mp4";
    const newFile = new File([audioUkFile.value.raw], `audioUkFile.${ext}`, {
      type: audioUkFile.value.raw.type,
    });
    formData.append("files", newFile);
  }

  if (audioUsFile.value?.raw) {
    const ext = audioUsFile.value.name.split(".").pop() || "mp4";
    const newFile = new File([audioUsFile.value.raw], `audioUsFile.${ext}`, {
      type: audioUsFile.value.raw.type,
    });
    formData.append("files", newFile);
  }

  for (const [key, value] of formData.entries()) {
    console.log("提交的form表单内容为：", key, value);
  }
  if ([...formData.entries()].length > 0) {
    uploadFile(wordId, formData)
      .then((res: any) => {
        if (res.code == 200) {
          const result = res.data;
          setOssUrl(result);
          const suc = submitData();
          if (!suc) {
            return;
          }
        } else {
          ElMessage.error("上传文件失败1");
        }
      })
      .catch((e) => {
        console.log("上传文件失败2：", e);
        ElMessage.error("上传文件失败2");
        return;
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    submitData();
  }
};

const setOssUrl = (result: any) => {
  //例句音频的oss链接
  Object.entries(editForm.value.sentences).forEach(([key, value], index) => {
    const arr = value as any[];
    arr.forEach((s: any, i: number) => {
      // 英音
      const ukFileName = getFileName(s.audioUkFile, key, index, i,'audioUkFile');

      if (ukFileName && result[ukFileName]) {
        s.audioUkUrl = result[ukFileName];
      }

      const usFileName = getFileName(s.audioUsFile, key, index, i,'audioUsFile');

      if (usFileName && result[usFileName]) {
        s.audioUsUrl = result[usFileName];
      }
    });
  });

  // 视频的oss链接
  Object.entries(editForm.value.videoFiles).forEach(([key, value], index) => {
    const file = value as any;
    if (file && file.raw instanceof File) {
      const ext = file.name.split(".").pop() || "mp4";
      const fileName = `${key}_videoFile.${ext}`;
      if (result[fileName]) {
        editForm.value.videoFile[key] = result[fileName];
      }
    }
  });

  // 美音
  if (audioUsFile.value) {
    const ext = audioUsFile.value.name.split(".").pop() || "mp4";
    const fileName = `audioUsFile.${ext}`;
    if (result[fileName]) {
      editForm.value.audioUsUrl = result[fileName];
    }
  }

  // 英音
  if (audioUkFile.value) {
    const ext = audioUkFile.value.name.split(".").pop() || "mp4";
    const fileName = `audioUkFile.${ext}`;
    if (result[fileName]) {
      editForm.value.audioUkUrl = result[fileName];
    }
  }
};
/**
 * 提交数据
 */
const submitData = (): boolean => {
  Object.keys(editForm.value.sentences).forEach((key) => {
    editForm.value.sentences[key].map((item: any, i: number) => {
      item.structurePartsEn = item.structurePartsEnStr?.split("\n");
      item.practices = item.practicesStr?.split("\n");
    });
  });

  Object.keys(editForm.value.meanings).forEach((key) => {
    const posArr = [] as any;
    editForm.value.meanings[key].posStr.split("\n").map((item: any) => {
      const arr = item.split(".");
      const pos = arr.length > 1 ? arr[0].trim() : "";
      const def = arr.length > 1 ? item.split(".")[1].trim() : arr[0].trim();
      posArr.push({ pos: pos, def: def });
    });
    editForm.value.meanings[key].pos = posArr;
    editForm.value.meanings[key].practices =
      editForm.value.meanings?.[key]?.practicesStr?.split("\n") || [];
  });

  const diff = diffObjects( editForm.value,props.item as any);
  console.log("提交的表单的修订内容为：", diff);

  editWord(editForm.value)
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success("编辑成功");
        editWordVisible.value = false;
        emit("updateWordList");
      }
    })
    .catch((e) => {
      ElMessage.error("编辑单词失败");
      return false;
    })
    .finally(() => {
      loading.value = false;
    });
  return true;
};

interface DiffItem {
  path: string;
  oldValue: any;
  newValue: any;
}

// 深度对比两个对象，返回所有差异
const diffObjects = (obj1: any, obj2: any, path = ""): DiffItem[] => {
  const diffs: DiffItem[] = [];
  const keys = new Set([...Object.keys(obj1 || {}),...Object.keys(obj2 || {})]);
  keys.forEach((key) => {
    if (!["id", "wordId","videoUrlStr","meaningsStr","sentencesStr","videoFiles","changeDescription"].includes(key) && key.indexOf('Str') === -1) {
      let newKey = key;
      if(key == "videoUrl"){
        newKey="videoFile";
      }

      let newKey1 = key;
      if(key == "videoFile"){
        newKey="videoUrl";
      }

      // if(key == 'practices'){
      //   debugger
      // }
      const fullPath = path ? `${path}.${key}` : key;
      const val1 = obj1 ? obj1[newKey1] : undefined;
      const val2 = obj2 ? obj2[newKey] : undefined;
      if (
        typeof val1 === "object" &&
        val1 !== null &&
        typeof val2 === "object" &&
        val2 !== null
      ) {
        diffs.push(...diffObjects(val1, val2, fullPath));
      } else if (val1 != val2) {
        diffs.push({
          path: fullPath,
          oldValue: val2,
          newValue: val1,
        });
      }
    }
  });
  return diffs;
};

const submitForm = () => {
  loading.value = true;
  uploadFileToOss(editForm.value.id);
};

const currentAudio = ref<HTMLAudioElement | null>(null);

const playAudio = (url?: string) => {
  console.log("播放音频：", url);

  // 停止当前音频
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value.currentTime = 0;
    currentAudio.value = null;
  }

  // 播放新音频
  if (url) {
    const audio = new Audio(`${url}?t=${Date.now()}`);
    audio.play().catch((err) => {
      console.error("音频播放失败：", err);
    });
    currentAudio.value = audio;
  }
};

// 组件卸载时释放资源
onBeforeUnmount(() => {
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
  }
});

const playVideo = (url?: string) => {
  console.log("播放视频：", url);
  if (url) {
    currentPlayVideoUrl.value = url;
    videoDialogVisible.value = true;
  }
};

const getAllMeanings = (meanings: any) => {
  if (!meanings || !meanings.pos || !meanings.pos.length) return "";

  return meanings.pos;
};
const standard = ref("通用");
const special = ref("特色");
const getContent = (contentMap, key, type: string = "sentence") => {
  if (type == "sentence") {
    return (
      contentMap?.[standard.value || ""]?.[0]?.[key] ||
      contentMap?.[special.value || ""]?.[0]?.[key] ||
      ""
    );
  }

  if (type == "video") {
    return (
      contentMap?.[standard.value || ""] ||
      contentMap?.[special.value || ""] ||
      ""
    );
  }

  if (type == "meanings") {
    return (
      contentMap?.[standard.value || ""] ||
      contentMap?.[special.value || ""] ||
      {}
    );
  }
  return "";
};

const sentenceVideoContent = computed(() =>
  getContent(props.item.videoUrl, "", "video")
);

const sentenceEnContent = computed(() =>
  getContent(props.item.sentences, "sentenceEn")
);

const sentenceCnContent = computed(() =>
  getContent(props.item.sentences, "sentenceCn")
);

const sentenceAudioUsUrl = computed(() =>
  getContent(props.item.sentences, "audioUsUrl")
);

const posStr = computed(() => {
  const mea = getContent(props.item.meanings, "", "meanings");
  return getAllMeanings(mea);
});
</script>

<style>
.word-info {
  font-size: 14px;
  margin-bottom: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 4px;
}

.word-info .el-form-item__label {
  width: 40px;
  padding: 4px 8px;
  height: 100%;
  font-weight: 700;
}
</style>

<style scoped>
.elementary-word-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 3px solid #8bc4fb;
  border-radius: 16px;
  padding: 15px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  background-image: radial-gradient(
      circle at 10% 20%,
      rgba(255, 200, 200, 0.1) 15px,
      transparent 16px
    ),
    radial-gradient(
      circle at 85% 40%,
      rgba(200, 255, 200, 0.1) 12px,
      transparent 13px
    ),
    radial-gradient(
      circle at 30% 80%,
      rgba(200, 200, 255, 0.1) 18px,
      transparent 19px
    );
}

.elementary-word-card::before {
  content: "";
  position: absolute;
  top: -10px;
  right: -10px;
  width: 80px;
  height: 80px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>')
    no-repeat;
  opacity: 0.3;
  transform: rotate(15deg);
  z-index: 0;
}

/* 装饰元素样式 */
.decoration-element {
  position: absolute;
  z-index: 0;
  opacity: 0.15;
}

.pencil {
  width: 60px;
  height: 60px;
  bottom: 10px;
  left: 10px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234CAF50"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>')
    no-repeat;
  transform: rotate(-15deg);
}

.book {
  width: 70px;
  height: 70px;
  top: 20px;
  right: 20px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF5722"><path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/></svg>')
    no-repeat;
  transform: rotate(10deg);
}

.word-card {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.word-header {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  margin-bottom: 10px;
  background-color: rgba(255, 222, 173, 0.7);
  border-radius: 12px;
  padding: 10px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  border: 2px dashed #ffb74d;
  position: relative;
  z-index: 1;
}

.word-header span {
  color: #5d4037;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.edit-icon {
  cursor: pointer;
  color: #409eff;
  min-width: 1.5em;
  min-height: 1.5em;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 4px;
  margin-left: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, background-color 0.2s;
}

.edit-icon:hover {
  transform: rotate(15deg);
  background-color: rgba(255, 255, 255, 1);
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-word;
}
.word-class {
  font-size: 20px;
}

.fill-height {
  min-height: 32px;
  height: 32px;
  line-height: 32px;
}

.mr-5 {
  margin-right: 5px;
}

.def {
  font-size: 16px;
  margin-bottom: 5px;
  color: #2c3e50;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
}

.media-icons {
  display: flex;
  gap: 12px;
  margin-top: 8px;
  font-size: 20px;
  cursor: pointer;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.word-info-item {
  margin-bottom: 3px;
}

.word-info-item-zoom {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
  font-size: 18px;
}

.item-content-class {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  padding: 4px 8px;
  border-left: 3px solid #64b5f6;
}

.iconClass {
  min-width: 1.5em;
  min-height: 1.5em;
  color: #ff9800;
  transition: transform 0.2s;
  cursor: pointer;
  margin-left: 3px;
}

.iconClassEdit {
  margin: 0px 10px;
}

.iconClass:hover {
  transform: scale(1.2);
}

.upload-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.preview-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  padding: 10px 0;
}

.sen-tip-class {
  background-color: rgb(236, 212, 181);
  border: 1px dashed yellowgreen;
  border-radius: 5px;
  padding: 2px;
}

.card-footer {
  margin-top: 10px;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px dashed #e0e0e0;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 0 0 8px 8px;
}

.difficulty-indicator {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-top: 5px;
}

.difficulty-label {
  margin-right: 5px;
  font-weight: 500;
}

.difficulty-level {
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: bold;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.difficulty-low {
  background-color: #4caf50;
}

.difficulty-medium {
  background-color: #ff9800;
}

.difficulty-high {
  background-color: #f44336;
}

.footer-buttons {
  display: flex;
  gap: 10px;
}

.edit-icon {
  color: #64b5f6;
  font-size: 1.5em;
  cursor: pointer;
  transition: transform 0.2s;
}

.edit-icon:hover {
  transform: scale(1.2);
  color: #1976d2;
}

/* 查看单词信息弹出框样式 */
.word-detail-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #8ec5fc 0%, #e0c3fc 100%);
  border-radius: 16px 16px 0 0;
  padding: 15px 20px;
}

.word-detail-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #8ec5fc 0%, #e0c3fc 100%);
  border-radius: 16px 16px 0 0;
  padding: 20px;
}

.dialog-header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.dialog-title {
  font-size: 28px;
  color: #5d4037;
  font-weight: bold;
  font-family: "Comic Sans MS", cursive, sans-serif;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
  margin-bottom: 15px;
  position: relative;
}

.dialog-title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.8),
    transparent
  );
  border-radius: 3px;
}

.word-tabs-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;
}

.tab-icon-container {
  display: flex;
  gap: 15px;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 20px;
  padding: 5px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  pointer-events: none; /* 容器不阻止点击 */
}

.tab-icon-button {
  width: 30px;
  height: 30px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f5f7fa 0%, #e0e6ed 100%);
  border: 3px solid #fff;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  pointer-events: auto; /* 按钮可点击 */
}

.tab-icon-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.7) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.tab-icon-button:hover {
  transform: scale(1.1);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
}

.tab-icon-button:hover::before {
  opacity: 1;
}

.tab-icon-button.active {
  background: linear-gradient(135deg, #ffa000 0%, #ff8f00 100%);
  border-color: #ffca28;
  box-shadow: 0 5px 15px rgba(255, 160, 0, 0.4);
  transform: scale(1.1);
}

.tab-icon-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2; /* 确保内容在伪元素之上 */
  pointer-events: none; /* 内容不阻止点击 */
}

.tab-icon {
  font-size: 24px;
  margin-bottom: 2px;
}

.tab-icon-label {
  font-size: 12px;
  font-weight: bold;
  color: #5d4037;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.tab-icon-button.active .tab-icon-label {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tab-icon-button:active {
  transform: scale(0.95);
  transition: transform 0.1s;
}

.word-detail-dialog :deep(.el-dialog) {
  display: flex;
  flex-direction: column;
  max-height: 80vh; /* 设置弹框最大高度为视口高度的80% */
  margin-top: 10vh !important; /* 确保弹框居中 */
}

.word-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 0 0 16px 16px;
  overflow: hidden; /* 防止内容溢出 */
  flex: 1; /* 让内容区域占据剩余空间 */
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直排列 */
}

.word-detail-dialog :deep(.el-dialog__headerbtn) {
  font-size: 22px;
}

.word-detail-dialog :deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: #ff5722;
  transform: rotate(90deg);
  transition: all 0.3s;
}

.word-detail-content {
  padding: 0 20px 20px 20px;
  position: relative;
  overflow-y: auto; /* 添加垂直滚动条 */
  overflow-x: hidden; /* 隐藏水平滚动条 */
  min-height: 400px;
  max-height: calc(80vh - 120px); /* 减去标题和其他元素的高度 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #c3cfe2 #f5f7fa; /* Firefox */
}

/* 自定义滚动条样式 */
.word-detail-content::-webkit-scrollbar {
  width: 8px;
}

.word-detail-content::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 4px;
}

.word-detail-content::-webkit-scrollbar-thumb {
  background-color: #c3cfe2;
  border-radius: 4px;
  border: 2px solid #f5f7fa;
}

.word-detail-content::-webkit-scrollbar-thumb:hover {
  background-color: #8ec5fc;
}

/* 装饰元素 */
.detail-decoration {
  position: absolute;
  z-index: 0;
  opacity: 0.15;
}

.detail-decoration.pencil {
  width: 80px;
  height: 80px;
  bottom: 20px;
  left: 20px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234CAF50"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>')
    no-repeat;
  transform: rotate(-15deg);
}

.detail-decoration.book {
  width: 90px;
  height: 90px;
  top: 30px;
  right: 30px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF5722"><path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/></svg>')
    no-repeat;
  transform: rotate(10deg);
}

.detail-decoration.star {
  width: 70px;
  height: 70px;
  bottom: 50px;
  right: 50px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>')
    no-repeat;
  opacity: 0.2;
  transform: rotate(15deg);
}

/* 各部分样式 */
.detail-section {
  margin-bottom: 20px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  transition: transform 0.2s, box-shadow 0.2s;
}

.detail-section:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 单词和难度等级横向布局 */
.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-header .detail-section {
  flex: 1;
  margin-bottom: 0;
}

/* 发音部分横向布局 */
.detail-pronunciation-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-pronunciation-container .detail-section {
  flex: 1;
  margin-bottom: 0;
}

/* 各部分背景色 */
.word-section {
  background-color: rgba(255, 222, 173, 0.7);
  border: 2px dashed #ffb74d;
}

.difficulty-section {
  background-color: rgba(255, 236, 179, 0.7);
  border: 2px dashed #ffd54f;
}

.syllables-section {
  background-color: rgba(255, 204, 128, 0.7);
  border: 2px dashed #ffb300;
}

.us-pronunciation-section {
  background-color: rgba(179, 229, 252, 0.7);
  border: 2px dashed #29b6f6;
}

.uk-pronunciation-section {
  background-color: rgba(209, 232, 255, 0.7);
  border: 2px dashed #64b5f6;
}

.meaning-section {
  background-color: rgba(220, 237, 200, 0.7);
  border: 2px dashed #81c784;
}

.sentence-section {
  background-color: rgba(255, 204, 188, 0.7);
  border: 2px dashed #ff8a65;
}

/* 标签样式 */
.detail-label {
  font-size: 16px;
  font-weight: bold;
  color: #5d4037;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stage-class {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #fff;
  width: 30px;
  height: 20px;
  font-size: 10px;
  border-radius: 5px;
  margin-top: 5px;
}
.little-stage {
  background-color: #59cbeeaf;
}
.middle-stage {
  background-color: #226df8;
}
.high-stage {
  background-color: #fa3022;
}

/* 单词显示样式 */
.word-display {
  display: flex;
  align-items: center;
}

.word-text {
  font-size: 32px;
  font-weight: bold;
  color: #5d4037;
  margin-right: 15px;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
}

/* 难度等级样式 */
.difficulty-display {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.difficulty-stars {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
}

.difficulty-star {
  font-size: 24px;
  color: #d1d1d1;
}

.difficulty-star.active {
  color: #ffc107;
  text-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
}

.difficulty-text {
  font-size: 16px;
  font-weight: bold;
  color: #5d4037;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 2px 8px;
  border-radius: 10px;
}

.syllables-display {
  font-size: 20px;
  color: #7b5e57;
  font-style: italic;
}

/* 发音部分样式 */
.pronunciation-display {
  display: flex;
  align-items: center;
}

.phonetic-text {
  font-size: 18px;
  color: #1565c0;
  margin-right: 10px;
  font-family: Arial, sans-serif;
}

/* 释义部分样式 */
.meaning-display {
  font-size: 18px;
  color: #2e7d32;
  line-height: 1.5;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  border-left: 4px solid #4caf50;
}

/* 例句部分样式 */
.sentences-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.sentence-item {
  display: flex;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s;
}

.sentence-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

.sentence-number {
  width: 30px;
  height: 30px;
  background-color: #ff9800;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-right: 10px;
  flex-shrink: 0;
}

.sentence-content {
  flex-grow: 1;
}

.sentence-en {
  font-size: 18px;
  color: #1565c0;
  margin-bottom: 5px;
  font-weight: 500;
}

.sentence-cn {
  font-size: 16px;
  color: #5d4037;
  margin-bottom: 8px;
}

.sentence-audio {
  display: flex;
  align-items: center;
  gap: 10px;
}

.audio-label {
  font-size: 14px;
  color: #7b5e57;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 图标样式 */
.video-icon {
  font-size: 28px;
  color: #42b983;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, color 0.3s;
}

.video-icon:hover {
  transform: scale(1.2);
  color: #2c9465;
}

.audio-icon {
  font-size: 22px;
  color: #ff9800;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, color 0.3s;
}

.audio-icon:hover {
  transform: scale(1.2) rotate(15deg);
  color: #f57c00;
}

/* 单词标签切换按钮组样式 - 现已移至标题栏，这些样式保留用于兼容性 */
.word-tabs-section {
  background-color: rgba(255, 248, 225, 0.7);
  border: 2px dashed #ffd54f;
}

.word-tabs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 5px;
}

.word-tab-button {
  background: linear-gradient(135deg, #ffeb3b 0%, #ffc107 100%);
  border-radius: 12px;
  padding: 8px 16px;
  cursor: pointer;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid #fff59d;
  position: relative;
  overflow: hidden;
}

.word-tab-button:before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.5s ease;
}

.word-tab-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
  border-color: #ffee58;
}

.word-tab-button:hover:before {
  transform: scale(5);
  opacity: 0;
}

.word-tab-button.active {
  background: linear-gradient(135deg, #ffa000 0%, #ff8f00 100%);
  border-color: #ffca28;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tab-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.tab-icon {
  font-size: 18px;
}

.tab-text {
  font-size: 16px;
  font-weight: bold;
  color: #5d4037;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
  font-family: "Comic Sans MS", cursive, sans-serif;
}

/* 视频部分样式 */
.video-section {
  background-color: rgba(255, 236, 179, 0.7);
  border: 2px dashed #ffb300;
}

.video-display {
  display: flex;
  justify-content: center;
  padding: 10px;
}

.video-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
  border-radius: 15px;
  padding: 10px 20px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 2px solid #a5d6a7;
  color: white;
}

.video-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #43a047 0%, #2e7d32 100%);
}

.video-button .video-icon {
  font-size: 24px;
  background: none;
  box-shadow: none;
  color: white;
  padding: 0;
}

.video-text {
  font-size: 18px;
  font-weight: bold;
  font-family: "Comic Sans MS", cursive, sans-serif;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.video-button:hover .video-icon {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 标签切换动画效果 */
.tab-transition {
  animation: tabChange 0.5s ease;
}

@keyframes tabChange {
  0% {
    opacity: 0.7;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
