<template>
  <div class="teacher-profile-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>个人详情</h2>
      <p class="page-description">查看和管理您的个人信息</p>
    </div>

    <!-- 教师详情内容 -->
    <div v-if="currentTeacher" v-loading="loading" class="teacher-detail-container">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" size="small" @click="handleEdit">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>教师姓名：</label>
              <span>{{ currentTeacher.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>昵称：</label>
              <span>{{ currentTeacher.nickname || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>手机号码：</label>
              <span>{{ currentTeacher.phone }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>性别：</label>
              <span>{{ getGenderText(currentTeacher.gender) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>年龄：</label>
              <span>{{ currentTeacher.age || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ currentTeacher.email || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>目前所在地：</label>
              <span>{{ currentTeacher.currentLocation || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>工作性质：</label>
              <el-tag v-if="currentTeacher.employmentType" size="small" :type="getEmploymentTypeTagType(currentTeacher.employmentType)">
                {{ getEmploymentTypeText(currentTeacher.employmentType) }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>目前状态：</label>
              <span>{{ currentTeacher.currentStatus || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>教学组：</label>
              <span>{{ currentTeacher.groupName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>正式入职时间：</label>
              <span>{{ currentTeacher.formalEntryDate || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>暑期课上课时间：</label>
              <el-tag :type="getSummerScheduleTagType(currentTeacher.summerScheduleType)" size="small">
                {{ getSummerScheduleText(currentTeacher.summerScheduleType) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>状态：</label>
              <el-tag :type="getStatusTagType(currentTeacher.status)" size="small">
                {{ getStatusText(currentTeacher.status) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>入职时间：</label>
              <span>{{ formatDateTime(currentTeacher.createTime) }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>最后更新：</label>
              <span>{{ formatDateTime(currentTeacher.updateTime) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 教育背景 -->
      <el-card class="info-card">
        <template #header>
          <span>教育背景</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>最高学历：</label>
              <span>{{ currentTeacher.education || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>毕业院校：</label>
              <span>{{ currentTeacher.graduateSchool || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>毕业专业：</label>
              <span>{{ currentTeacher.major || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>大学属性：</label>
              <el-tag v-if="currentTeacher.universityType" size="small" :type="getUniversityTypeTagType(currentTeacher.universityType)">
                {{ currentTeacher.universityType }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>是否师范类：</label>
              <el-tag :type="currentTeacher.isNormalUniversity ? 'success' : 'info'" size="small">
                {{ currentTeacher.isNormalUniversity ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>是否留学：</label>
              <el-tag :type="currentTeacher.studyAbroad ? 'success' : 'info'" size="small">
                {{ currentTeacher.studyAbroad ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="currentTeacher.studyAbroad">
          <el-col :span="8">
            <div class="info-item">
              <label>留学国家：</label>
              <span>{{ currentTeacher.studyAbroadCountry || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>教龄：</label>
              <span>{{ currentTeacher.teachingYears || 0 }} 年</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>教资级别：</label>
              <span>{{ currentTeacher.teachingCertificateLevel || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>教授学科：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="subject in (currentTeacher.subjects || [])"
                  :key="subject"
                  size="small"
                  class="subject-tag"
                >
                  {{ subject }}
                </el-tag>
                <span v-if="!currentTeacher.subjects || currentTeacher.subjects.length === 0">-</span>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>已通过培训科目：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="subject in (currentTeacher.trainingSubjects || [])"
                  :key="subject"
                  size="small"
                  class="subject-tag"
                  type="success"
                >
                  {{ subject }}
                </el-tag>
                <span v-if="!currentTeacher.trainingSubjects || currentTeacher.trainingSubjects.length === 0">-</span>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>英语资质：</label>
              <span>{{ currentTeacher.englishQualification || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>普通话资质：</label>
              <span>{{ currentTeacher.mandarinQualification || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>沟通能力：</label>
              <el-tag v-if="currentTeacher.communicationAbility" size="small" :type="getCommunicationAbilityTagType(currentTeacher.communicationAbility)">
                {{ currentTeacher.communicationAbility }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>英语发音：</label>
              <el-tag v-if="currentTeacher.englishPronunciation" size="small" :type="getEnglishPronunciationTagType(currentTeacher.englishPronunciation)">
                {{ currentTeacher.englishPronunciation }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 个人信息 -->
      <el-card class="info-card">
        <template #header>
          <span>个人信息</span>
        </template>

        <el-row v-if="currentTeacher.taughtCourses && currentTeacher.taughtCourses.length > 0" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>教过课程：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="course in currentTeacher.taughtCourses"
                  :key="course"
                  size="small"
                  class="subject-tag"
                  type="info"
                >
                  {{ course }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="currentTeacher.teachingStyle && currentTeacher.teachingStyle.length > 0" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>上课风格：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="style in currentTeacher.teachingStyle"
                  :key="style"
                  size="small"
                  class="subject-tag"
                  type="success"
                >
                  {{ style }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8" v-if="currentTeacher.suitableGrades && currentTeacher.suitableGrades.length > 0">
            <div class="info-item">
              <label>适合学生年级：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="grade in currentTeacher.suitableGrades"
                  :key="grade"
                  size="small"
                  class="subject-tag"
                  type="primary"
                >
                  {{ grade }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="8" v-if="currentTeacher.suitableLevels && currentTeacher.suitableLevels.length > 0">
            <div class="info-item">
              <label>适合学生程度：</label>
              <div class="subjects-tags">
                <el-tag
                  v-for="level in currentTeacher.suitableLevels"
                  :key="level"
                  size="small"
                  class="subject-tag"
                  type="warning"
                >
                  {{ level }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="8" v-if="currentTeacher.suitablePersonality">
            <div class="info-item">
              <label>适合学生性格：</label>
              <el-tag size="small" type="info">
                {{ currentTeacher.suitablePersonality }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="currentTeacher.teachingExperience" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>教学经历：</label>
              <div class="text-content">{{ currentTeacher.teachingExperience }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="currentTeacher.awards" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>获奖奖项：</label>
              <div class="text-content">{{ currentTeacher.awards }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="currentTeacher.introduction" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>个人简介：</label>
              <div class="text-content">{{ currentTeacher.introduction }}</div>
            </div>
          </el-col>
        </el-row>

        <!-- 资质证书 -->
        <el-row v-if="currentTeacher.qualificationCertificates && currentTeacher.qualificationCertificates.length > 0" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>资质证书：</label>
              <div class="file-list">
                <div
                  v-for="(cert, index) in currentTeacher.qualificationCertificates"
                  :key="index"
                  class="file-item"
                >
                  <el-link
                    :href="cert"
                    target="_blank"
                    type="primary"
                    :underline="false"
                  >
                    <el-icon><Document /></el-icon>
                    证书{{ index + 1 }}
                  </el-link>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 示范上课视频 -->
        <el-row v-if="currentTeacher.demoVideos && currentTeacher.demoVideos.length > 0" :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>示范上课视频：</label>
              <div class="file-list">
                <div
                  v-for="(video, index) in currentTeacher.demoVideos"
                  :key="index"
                  class="file-item"
                >
                  <el-link
                    :href="video"
                    target="_blank"
                    type="primary"
                    :underline="false"
                  >
                    <el-icon><VideoPlay /></el-icon>
                    示范视频{{ index + 1 }}
                  </el-link>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 教师编辑对话框 -->
    <TeacherEditDialog
      v-model="editDialogVisible"
      :teacher="currentTeacher"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup name="TeacherProfile">
import { ref, onMounted } from 'vue'
import { Edit, Document, VideoPlay } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import { useTeachingGroupStore } from '@/stores/teachingGroup'
import useUserStore from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import TeacherEditDialog from '@/views/management/teaching-group/components/TeacherEditDialog.vue'

// 响应式数据
const currentTeacher = ref(null)
const loading = ref(false)
const editDialogVisible = ref(false)

// Store
const teachingGroupStore = useTeachingGroupStore()
const userStore = useUserStore()

// 方法
const handleEdit = () => {
  if (!currentTeacher.value) {
    ElMessage.warning('正在获取教师信息，请稍后再试')
    return
  }
  editDialogVisible.value = true
}

const handleEditSuccess = () => {
  editDialogVisible.value = false
  // 重新获取教师信息以更新显示
  fetchCurrentTeacher()
  ElMessage.success('教师信息更新成功')
}

const getGenderText = (gender) => {
  const genderMap = {
    '0': '男',
    '1': '女',
    '2': '未知'
  }
  return genderMap[gender] || gender
}

const getStatusText = (status) => {
  const statusMap = {
    '1': '正常',
    '0': '停用'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status) => {
  return status === '1' ? 'success' : 'danger'
}

const getUniversityTypeTagType = (type) => {
  const typeMap = {
    '双一流': 'danger',
    '985': 'warning',
    '211': 'primary',
    '一本': 'info',
    '普通': '',
  }
  return typeMap[type] || ''
}

const getCommunicationAbilityTagType = (ability) => {
  const abilityMap = {
    '优秀': 'success',
    '良好': 'primary',
    '一般': 'warning',
    '较差': 'danger'
  }
  return abilityMap[ability] || ''
}

const getEnglishPronunciationTagType = (pronunciation) => {
  const pronunciationMap = {
    '标准': 'success',
    '良好': 'primary',
    '一般': 'warning',
    '较差': 'danger'
  }
  return pronunciationMap[pronunciation] || ''
}

const getSummerScheduleText = (type) => {
  const typeMap = {
    'full': '全满档',
    'golden': '黄金档',
    'other': '其他档'
  }
  return typeMap[type] || '其他档'
}

const getSummerScheduleTagType = (type) => {
  const typeMap = {
    'full': 'danger',
    'golden': 'warning',
    'other': 'info'
  }
  return typeMap[type] || 'info'
}

// 获取工作性质标签类型
const getEmploymentTypeTagType = (type) => {
  const typeMap = {
    'full_time': 'success',
    'intended_full_time': 'primary',
    'part_time': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取工作性质文本
const getEmploymentTypeText = (type) => {
  const textMap = {
    'full_time': '全职',
    'intended_full_time': '意向全职',
    'part_time': '兼职'
  }
  return textMap[type] || type
}

// 获取当前登录老师的信息
const fetchCurrentTeacher = async () => {
  loading.value = true
  try {
    const currentUserId = userStore.id
    if (!currentUserId) {
      ElMessage.error('无法获取当前用户信息')
      return
    }

    // 获取教师详细信息
    const detail = await teachingGroupStore.fetchTeacherDetail(currentUserId)
    if (detail) {
      currentTeacher.value = {
        ...detail,
        id: currentUserId, // 确保有ID字段
        userId: currentUserId,
        teacherId: currentUserId
      }
    }
  } catch (error) {
    console.error('获取教师信息失败:', error)
    ElMessage.error('获取教师信息失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchCurrentTeacher()
})
</script>

<style lang="scss" scoped>
.teacher-profile-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 30px;

  h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }

  .page-description {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.teacher-detail-container {
  .info-card {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .info-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-weight: 600;
        color: #606266;
        margin-right: 8px;
      }

      .text-content {
        margin-top: 4px;
        padding: 8px 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #303133;
        line-height: 1.5;
      }

      .subjects-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .subject-tag {
          margin: 0;
        }
      }

      .file-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-top: 8px;

        .file-item {
          .el-link {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            background-color: #f5f7fa;
            border-radius: 4px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s;

            &:hover {
              background-color: #ecf5ff;
              border-color: #409eff;
            }

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      .stats-item {
        text-align: center;
        padding: 16px;
        background-color: #f5f7fa;
        border-radius: 8px;

        .stats-number {
          font-size: 24px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 4px;
        }

        .stats-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}
</style>
