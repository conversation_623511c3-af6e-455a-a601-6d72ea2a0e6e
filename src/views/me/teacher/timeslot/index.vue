<template>
  <div class="teacher-schedule-container">

    <!-- 需要更新的警告提示 -->
    <el-alert
      v-if="lastUpdateInfo && lastUpdateInfo.needsUpdate"
      :title="lastUpdateInfo.message"
      type="error"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;"
    >
    </el-alert>

    <!-- 更新提示 -->
    <el-alert
      v-if="showUpdateTip"
      title="请点击编辑按钮更新您的可上课时间"
      type="warning"
      :closable="true"
      @close="showUpdateTip = false"
      style="margin-bottom: 20px;"
    >
      <template #default>
        <p>为了确保课程安排的准确性，请及时更新您的可上课时间段。</p>
      </template>
    </el-alert>

    <!-- 主要内容区域 -->
    <div v-loading="loading" class="schedule-content">
      <!-- 暑期课上课时间设置 -->
      <el-card class="summer-schedule-card">
        <template #header>
          <div class="card-header">
            <span>暑期课上课时间</span>
            <el-button v-if="!isEditingSummer" type="primary" size="small" @click="handleEditSummer">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <div v-else class="edit-actions">
              <el-button size="small" @click="handleCancelEditSummer">取消</el-button>
              <el-button type="primary" size="small" :loading="savingSummer" @click="handleSaveSummer">
                保存
              </el-button>
            </div>
          </div>
        </template>

        <div class="summer-schedule-content">
          <div v-if="!isEditingSummer" class="summer-schedule-display">
            <el-tag :type="getSummerScheduleTagType(currentSummerScheduleType)" size="large">
              {{ getSummerScheduleText(currentSummerScheduleType) }}
            </el-tag>
            <div class="summer-schedule-description">
              {{ getSummerScheduleDescription(currentSummerScheduleType) }}
            </div>
          </div>
          <div v-else class="summer-schedule-edit">
            <el-select
              v-model="editingSummerScheduleType"
              placeholder="请选择暑期课上课时间"
              style="width: 100%"
            >
              <el-option label="全满档（全天，一周6-7天，均可排课）" value="full" />
              <el-option label="黄金档（周一到周五晚上，周末2天，均可排课）" value="golden" />
              <el-option label="其他档（其他指定时间可排课）" value="other" />
            </el-select>
          </div>
        </div>
      </el-card>

      <el-card class="schedule-card">
        <template #header>
          <div class="card-header">
          <div>
            <span>时间段设置</span>
            <!-- 最后更新时间信息 -->
      <div v-if="lastUpdateInfo" class="update-time-info">
        <el-text size="small" type="info">
          最后更新时间：{{ formatUpdateTime(lastUpdateInfo.lastUpdateTime) }}
          <span v-if="lastUpdateInfo.daysSinceLastUpdate !== null">
            （{{ lastUpdateInfo.daysSinceLastUpdate }}天前）
          </span>
        </el-text>
      </div>
          </div>
            <!-- 时间段统计信息 -->
        <div class="time-stats">
          <div class="stat-item">
            <span class="stat-label">总时间段数：</span>
            <span class="stat-value">{{ timeSlots.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">本周可用时间：</span>
            <span class="stat-value">{{ calculateWeeklyHours() }} 小时</span>
          </div>
        </div>
            <div class="header-actions">
              <el-button v-if="!isEditing" type="primary" @click="handleEdit">
                <el-icon><Edit /></el-icon>
                编辑时间表
              </el-button>
              <div v-else class="edit-actions">
                <el-button @click="handleCancelEdit">取消</el-button>
                <el-button type="primary" :loading="saving" @click="handleSave">
                  保存
                </el-button>
              </div>
              <el-button @click="handleReset">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </div>
        </template>

        <!-- 时间段编辑器 -->
        <div class="time-slot-editor">
          <AdvancedTimeSlotEditor
            v-model="timeSlots"
            :readonly="!isEditing"
            @change="handleTimeSlotsChange"
          />
        </div>
      </el-card>


    </div>
  </div>
</template>

<script setup name="TeacherTimeSlotManagement">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Edit, Refresh } from '@element-plus/icons-vue'
import { useTeachingGroupStore } from '@/stores/teachingGroup'
import useUserStore from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import AdvancedTimeSlotEditor from '@/views/management/teacher/components/AdvancedTimeSlotEditor.vue'
import { checkTeacherTimeSlotUpdateTimeApi } from '@/api/management/teachingGroup'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const isEditing = ref(false)
const timeSlots = ref([])
const originalTimeSlots = ref([])
const showUpdateTip = ref(false)
const lastUpdateInfo = ref(null)

// 暑期课上课时间相关
const currentSummerScheduleType = ref('other')
const editingSummerScheduleType = ref('other')
const isEditingSummer = ref(false)
const savingSummer = ref(false)

// 路由相关
const route = useRoute()

// Store
const teachingGroupStore = useTeachingGroupStore()
const userStore = useUserStore()

// 方法
const calculateWeeklyHours = () => {
  let totalMinutes = 0
  timeSlots.value.forEach(slot => {
    const start = slot.startTime.split(':')
    const end = slot.endTime.split(':')
    const startMinutes = parseInt(start[0]) * 60 + parseInt(start[1])
    const endMinutes = parseInt(end[0]) * 60 + parseInt(end[1])
    totalMinutes += endMinutes - startMinutes
  })
  return (totalMinutes / 60).toFixed(1)
}

const handleEdit = () => {
  isEditing.value = true
}

const handleCancelEdit = () => {
  isEditing.value = false
  timeSlots.value = JSON.parse(JSON.stringify(originalTimeSlots.value))
}

const handleReset = () => {
  if (isEditing.value) {
    handleCancelEdit()
  } else {
    fetchTimeSlots()
  }
}

const handleTimeSlotsChange = (newTimeSlots) => {
  console.log('时间段变化:', newTimeSlots)
}

// 暑期课上课时间相关方法
const getSummerScheduleText = (type) => {
  const typeMap = {
    'full': '全满档',
    'golden': '黄金档',
    'other': '其他档'
  }
  return typeMap[type] || '其他档'
}

const getSummerScheduleTagType = (type) => {
  const typeMap = {
    'full': 'danger',
    'golden': 'warning',
    'other': 'info'
  }
  return typeMap[type] || 'info'
}

const getSummerScheduleDescription = (type) => {
  const descMap = {
    'full': '全天，一周6-7天，均可排课',
    'golden': '周一到周五晚上，周末2天，均可排课',
    'other': '其他指定时间可排课'
  }
  return descMap[type] || '其他指定时间可排课'
}

const handleEditSummer = () => {
  editingSummerScheduleType.value = currentSummerScheduleType.value
  isEditingSummer.value = true
}

const handleCancelEditSummer = () => {
  editingSummerScheduleType.value = currentSummerScheduleType.value
  isEditingSummer.value = false
}

const handleSaveSummer = async () => {
  const currentUserId = userStore.id
  if (!currentUserId) {
    ElMessage.error('无法获取当前用户信息')
    return
  }

  try {
    savingSummer.value = true

    // 使用独立的暑期课上课时间更新接口
    const success = await teachingGroupStore.updateTeacherSummerSchedule(
      currentUserId,
      editingSummerScheduleType.value
    )

    if (success) {
      currentSummerScheduleType.value = editingSummerScheduleType.value
      isEditingSummer.value = false
    }
  } catch (error) {
    console.error('保存暑期课上课时间失败:', error)
    ElMessage.error('保存暑期课上课时间失败')
  } finally {
    savingSummer.value = false
  }
}



// 验证时间段数据
const validateTimeSlots = (slots) => {
  for (let i = 0; i < slots.length; i++) {
    const slot = slots[i]

    // 检查必填字段
    if (!slot.startTime || !slot.endTime) {
      return { valid: false, message: `第${i + 1}个时间段的开始时间或结束时间为空` }
    }

    // 检查时间格式
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    if (!timeRegex.test(slot.startTime) || !timeRegex.test(slot.endTime)) {
      return { valid: false, message: `第${i + 1}个时间段的时间格式不正确` }
    }

    // 检查时间逻辑
    if (slot.startTime >= slot.endTime) {
      return { valid: false, message: `第${i + 1}个时间段的开始时间必须小于结束时间` }
    }

    // 检查是否有无效的00:00结束时间
    if (slot.endTime === '00:00') {
      return { valid: false, message: `第${i + 1}个时间段的结束时间不能为00:00，请选择有效的结束时间` }
    }
  }

  return { valid: true }
}

const handleSave = async () => {
  const currentUserId = userStore.id
  if (!currentUserId) {
    ElMessage.error('无法获取当前用户信息')
    return
  }

  // 验证时间段数据
  const validation = validateTimeSlots(timeSlots.value)
  if (!validation.valid) {
    ElMessage.error(validation.message)
    return
  }

  try {
    saving.value = true

    // 保存时间段数据
    const success = await teachingGroupStore.updateTeacherTimeSlots({
      teacherId: currentUserId,
      timeSlots: timeSlots.value
    })

    if (success) {
      originalTimeSlots.value = JSON.parse(JSON.stringify(timeSlots.value))
      isEditing.value = false
      ElMessage.success('保存成功')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 获取教师时间表
const fetchTimeSlots = async () => {
  const currentUserId = userStore.id
  if (!currentUserId) {
    ElMessage.error('无法获取当前用户信息')
    return
  }

  loading.value = true
  try {
    const teacherTimeSlots = await teachingGroupStore.fetchTeacherTimeSlots(currentUserId)

    // 确保数据格式正确，并处理字段映射
    const validTimeSlots = (teacherTimeSlots || []).filter(slot =>
      slot &&
      (slot.weekday || slot.dayOfWeek) &&
      slot.startTime &&
      slot.endTime
    ).map(slot => ({
      id: slot.id || `slot_${Date.now()}_${Math.random()}`,
      weekday: slot.weekday || slot.dayOfWeek, // 处理字段名差异
      startTime: slot.startTime,
      endTime: slot.endTime,
      status: slot.status || 'available',
      remark: slot.remark || ''
    }))

    timeSlots.value = validTimeSlots
    originalTimeSlots.value = JSON.parse(JSON.stringify(validTimeSlots))

    console.log('获取教师时间表成功:', { teacherId: currentUserId, count: validTimeSlots.length, data: validTimeSlots })

    // 获取更新时间信息
    await fetchUpdateTimeInfo()
  } catch (error) {
    console.error('获取教师时间表失败:', error)
    ElMessage.error('获取教师时间表失败')
    timeSlots.value = []
    originalTimeSlots.value = []
  } finally {
    loading.value = false
  }
}

// 获取教师详细信息（包含暑期课上课时间）
const fetchTeacherDetail = async () => {
  const currentUserId = userStore.id
  if (!currentUserId) {
    return
  }

  try {
    const detail = await teachingGroupStore.fetchTeacherDetail(currentUserId)
    if (detail) {
      currentSummerScheduleType.value = detail.summerScheduleType || 'other'
      editingSummerScheduleType.value = detail.summerScheduleType || 'other'
    }
  } catch (error) {
    console.error('获取教师详细信息失败:', error)
  }
}

// 获取时间表更新时间信息
const fetchUpdateTimeInfo = async () => {
  const currentUserId = userStore.id
  if (!currentUserId) return

  try {
    const response = await checkTeacherTimeSlotUpdateTimeApi(currentUserId)
    if (response.code === 200) {
      lastUpdateInfo.value = response.data
    }
  } catch (error) {
    console.error('获取时间表更新时间失败:', error)
    // 不显示错误信息，静默失败
  }
}

// 格式化更新时间
const formatUpdateTime = (updateTime) => {
  if (!updateTime) return '未知'

  try {
    const date = new Date(updateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '格式错误'
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchTimeSlots()
  fetchTeacherDetail()

  // 检查是否需要显示更新提示
  // 可以通过路由参数或其他方式判断是否从弹窗跳转过来
  if (route.query.showTip === 'true') {
    showUpdateTip.value = true
  }
})
</script>

<style lang="scss" scoped>
.teacher-schedule-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }

  .page-description {
    margin: 0 0 8px 0;
    color: #909399;
    font-size: 14px;
  }

  .update-time-info {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #f0f9ff;
    border-radius: 4px;
    border-left: 3px solid #409eff;
  }
}

.schedule-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.summer-schedule-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #303133;

    .edit-actions {
      display: flex;
      gap: 8px;
    }
  }

  .summer-schedule-content {
    .summer-schedule-display {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .summer-schedule-description {
        color: #606266;
        font-size: 14px;
        margin-top: 8px;
      }
    }

    .summer-schedule-edit {
      max-width: 400px;
    }
  }
}

.schedule-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #303133;
    
    .header-actions {
      display: flex;
      gap: 8px;
      
      .edit-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.time-slot-editor {
  margin-bottom: 20px;
}

.time-stats {
  display: flex;
  gap: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  
  .stat-item {
    display: flex;
    align-items: center;
    
    .stat-label {
      color: #606266;
      margin-right: 8px;
    }
    
    .stat-value {
      font-weight: 600;
      color: #409eff;
    }
  }
}


</style>
