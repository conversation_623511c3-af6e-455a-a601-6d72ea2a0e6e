import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { curriculumApi } from '@/api/curriculum'
import request from '@/utils/request'

export const useCurriculumStore = defineStore('curriculum', () => {
  // 状态
  const schedules = ref([])
  const reviewPlans = ref([])
  const currentView = ref('week')
  const selectedDate = ref(new Date())
  const loading = ref(false)
  const students = ref([])
  const teachers = ref([])
  const teacherAvailableTimeSlots = ref(null)

  // 计算属性
  const todayReviews = computed(() => {
    console.log('=== todayReviews computed DEBUG ===')
    console.log('reviewPlans.value:', reviewPlans.value, 'type:', typeof reviewPlans.value, 'isArray:', Array.isArray(reviewPlans.value))

    if (!Array.isArray(reviewPlans.value)) {
      console.error('reviewPlans.value is not an array:', reviewPlans.value)
      return []
    }

    const today = new Date()
    today.setHours(23, 59, 59, 999) // 设置到今天的结束时间，包含今天及之前的

    const result = reviewPlans.value.filter(review => {
      // 抗遗忘复习使用scheduledTime字段
      const reviewDate = review.scheduledTime || review.scheduledDate || review.startTime
      if (!reviewDate) return false

      const reviewDateObj = new Date(reviewDate)

      console.log('🔄 [抗遗忘复习] 处理今日复习时间:', {
        reviewId: review.id,
        scheduledTime: review.scheduledTime,
        reviewDateObj: reviewDateObj.toDateString(),
        today: today.toDateString(),
        isToday: reviewDateObj <= today,
        reviewType: review.reviewType
      })

      // "暂时到今天"包含今天及之前的所有待复习项目
      return reviewDateObj <= today
    })
    console.log('🔄 [抗遗忘复习] todayReviews result:', result)
    return result
  })

  const futureReviews = computed(() => {
    console.log('=== futureReviews computed DEBUG ===')
    console.log('reviewPlans.value:', reviewPlans.value, 'type:', typeof reviewPlans.value, 'isArray:', Array.isArray(reviewPlans.value))

    if (!Array.isArray(reviewPlans.value)) {
      console.error('reviewPlans.value is not an array:', reviewPlans.value)
      return []
    }

    console.log(reviewPlans.value.length, '抗遗忘复习数据条目')

    const today = new Date()
    today.setHours(23, 59, 59, 999) // 设置到今天的结束时间

    const result = reviewPlans.value.filter(review => {
      // 抗遗忘复习使用scheduledTime字段
      const reviewDate = review.scheduledTime || review.scheduledDate || review.startTime
      if (!reviewDate) return false

      const reviewDateObj = new Date(reviewDate)

      console.log('🔄 [抗遗忘复习] 处理待复习时间:', {
        reviewId: review.id,
        scheduledTime: review.scheduledTime,
        reviewDateObj: reviewDateObj.toDateString(),
        today: today.toDateString(),
        isFuture: reviewDateObj > today,
        reviewType: review.reviewType
      })

      // "待复习"包含明天及以后的复习项目
      return reviewDateObj > today
    })
    console.log('🔄 [抗遗忘复习] futureReviews result:', result)
    return result
  })

  const currentWeekSchedules = computed(() => {
    console.log('=== currentWeekSchedules computed DEBUG ===')
    console.log('schedules.value:', schedules.value, 'type:', typeof schedules.value, 'isArray:', Array.isArray(schedules.value))

    if (!Array.isArray(schedules.value)) {
      console.error('schedules.value is not an array:', schedules.value)
      return []
    }

    const startOfWeek = getStartOfWeek(selectedDate.value)
    const endOfWeek = getEndOfWeek(selectedDate.value)

    const result = schedules.value.filter(schedule => {
      const scheduleDate = new Date(schedule.startTime)
      return scheduleDate >= startOfWeek && scheduleDate <= endOfWeek
    })
    console.log('currentWeekSchedules result:', result)
    return result
  })

  const currentMonthSchedules = computed(() => {
    console.log('=== currentMonthSchedules computed DEBUG ===')
    console.log('schedules.value:', schedules.value, 'type:', typeof schedules.value, 'isArray:', Array.isArray(schedules.value))

    if (!Array.isArray(schedules.value)) {
      console.error('schedules.value is not an array:', schedules.value)
      return []
    }

    const year = selectedDate.value.getFullYear()
    const month = selectedDate.value.getMonth()

    const result = schedules.value.filter(schedule => {
      const scheduleDate = new Date(schedule.startTime)
      return scheduleDate.getFullYear() === year && scheduleDate.getMonth() === month
    })
    console.log('currentMonthSchedules result:', result)
    return result
  })

  // 方法
  const fetchSchedules = async (params) => {
    loading.value = true
    try {
      console.log('=== fetchSchedules DEBUG ===')
      console.log('请求参数:', params)
      console.log('调用前 schedules.value:', schedules.value, 'type:', typeof schedules.value, 'isArray:', Array.isArray(schedules.value))

      // 修复后的日志
      console.log('🔧 [修复] 现在支持课程类型参数:', params.courseType)
      console.log('🔧 [修复] 将通过统一接口获取指定类型的课程')

      // 保存当前查询参数（排除基础的视图参数）
      const { viewType, startDate, endDate, type, ...filterParams } = params
      currentQueryParams.value = filterParams
      console.log('🔧 [修复] 保存筛选参数用于刷新:', currentQueryParams.value)

      const response = await curriculumApi.getSchedule(params)
      console.log('API响应:', response)
      console.log('response.data:', response.data, 'type:', typeof response.data, 'isArray:', Array.isArray(response.data))
      
      // 验证返回数据的类型分布
      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        const typeDistribution = response.data.reduce((acc, item) => {
          const type = item.type || 'unknown'
          acc[type] = (acc[type] || 0) + 1
          return acc
        }, {})
        
        console.log('🔧 [修复] 返回数据的课程类型分布:', typeDistribution)
        console.log('🔧 [修复] 第一条数据结构:', response.data[0])
        console.log('🔧 [修复] 时间字段验证:', {
          hasStartTime: 'startTime' in response.data[0],
          hasEndTime: 'endTime' in response.data[0],
          hasScheduledDate: 'scheduledDate' in response.data[0],
          hasReviewType: 'reviewType' in response.data[0],
          hasWordCount: 'wordCount' in response.data[0]
        })
      }
      
      if (response.code === 200) {
        schedules.value = response.data
        
        // 修复：同时更新复习课数据（从统一的课程数据中筛选）
        const reviewCourses = response.data.filter(course => course.type === 'review')
        reviewPlans.value = reviewCourses
        console.log('🔧 [修复] 从统一数据中筛选出复习课:', reviewCourses.length, '条')
        
        console.log('设置后 schedules.value:', schedules.value, 'type:', typeof schedules.value, 'isArray:', Array.isArray(schedules.value))
      } else {
        console.error('API返回错误:', response.message)
        // 确保在错误情况下保持数组类型
        schedules.value = []
      }
    } catch (error) {
      console.error('获取课表数据失败:', error)
      // 确保在异常情况下保持数组类型
      schedules.value = []
    } finally {
      loading.value = false
    }
  }

  const fetchReviewPlans = async (params) => {
    try {
      console.log('=== fetchReviewPlans DEBUG ===')
      console.log('请求参数:', params)
      console.log('调用前 reviewPlans.value:', reviewPlans.value, 'type:', typeof reviewPlans.value, 'isArray:', Array.isArray(reviewPlans.value))

      // 修改：现在使用抗遗忘复习专用接口
      console.log('🔄 [抗遗忘复习] 现在使用抗遗忘复习专用接口')
      console.log('🔄 [抗遗忘复习] 复习参数结构:', params)

      const response = await curriculumApi.getReviewPlans(params)
      console.log('API响应:', response)
      console.log('response.data:', response.data, 'type:', typeof response.data, 'isArray:', Array.isArray(response.data))
    
      
      if (response.code === 200) {
        // 处理抗遗忘复习数据结构
        const reviewData = response.data || []
        console.log('🔄 [抗遗忘复习] 原始数据:', reviewData)
        console.log('🔄 [抗遗忘复习] 数据类型:', typeof reviewData, '是否数组:', Array.isArray(reviewData))

        // 转换数据结构以适配前端显示
        const processedReviews = reviewData.map(review => ({
          ...review,
          // 确保有学生姓名显示（老师查看时显示学生姓名，学生查看时可以不显示）
          studentName: review.studentName || '未知学生',
          // 使用scheduledTime作为显示时间
          startTime: review.scheduledTime,
          endTime: review.scheduledTime,
          // 保持原有字段
          scheduledDate: review.scheduledTime ? new Date(review.scheduledTime).toISOString().split('T')[0] : null
        }))

        reviewPlans.value = processedReviews
        console.log('🔄 [抗遗忘复习] 处理后数据:', reviewPlans.value)
        console.log('🔄 [抗遗忘复习] 数据条数:', reviewPlans.value.length)
        console.log('设置后 reviewPlans.value:', reviewPlans.value, 'type:', typeof reviewPlans.value, 'isArray:', Array.isArray(reviewPlans.value))
      } else {
        console.error('API返回错误:', response.message)
        // 确保在错误情况下保持数组类型
        reviewPlans.value = []
      }
    } catch (error) {
      console.error('获取复习课失败:', error)
      // 确保在异常情况下保持数组类型
      reviewPlans.value = []
    }
  }

  const fetchStudents = async (params) => {
    try {
      // 如果指定了教师ID，使用教师学生关系API
      if (params.teacherId) {
        const response = await request({
          url: `/management/teachers/${params.teacherId}/students`,
          method: 'get'
        })
        if (response.code === 200) {
          // 转换数据格式以匹配前端期望的结构
          const studentList = response.data || []
          students.value = studentList.map(student => ({
            id: String(student.id), // 确保ID为字符串类型
            name: student.name,
            phone: student.phone,
            grade: student.grade,
            school: student.school,
            status: 'active'
          }))
          console.log('获取到教师学生列表:', students.value)
        }
      } else {
        // 没有指定教师ID时，使用原有的课程表学生API
        const response = await curriculumApi.getStudents(params)
        if (response.code === 200) {
          // 确保学生ID为字符串类型
          const studentList = response.data.rows || []
          students.value = studentList.map(student => ({
            ...student,
            id: String(student.id) // 确保ID为字符串类型
          }))
        }
      }
    } catch (error) {
      console.error('获取学生列表失败:', error)
    }
  }

  const fetchTeachers = async (params) => {
    try {
      const response = await curriculumApi.getTeachers(params)
      if (response.code === 200) {
        // 处理后端返回的数据结构，确保教师ID为字符串类型
        const teacherList = response.data || []
        teachers.value = teacherList.map(teacher => ({
          ...teacher,
          id: String(teacher.id) // 确保ID为字符串类型
        }))
        console.log('获取到的教师列表:', teachers.value)
      }
    } catch (error) {
      console.error('获取教师列表失败:', error)
    }
  }

  const fetchTeacherAvailableTimeSlots = async (teacherId) => {
    try {
      const response = await curriculumApi.getTeacherAvailableTimeSlots(teacherId)

      if (response.code === 200) {
        teacherAvailableTimeSlots.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取教师可排课时间段失败:', error)
    }
    return null
  }

  const createSchedule = async (data) => {
    try {
      console.log('=== curriculum store createSchedule ===')
      console.log('排课数据:', data)
      console.log('API调用前')
      
      const response = await curriculumApi.createSchedule(data)
      
      console.log('API响应:', response)
      
      if (response.code === 200) {
        // 重新获取课表数据
        await refreshCurrentSchedules()
        console.log('排课成功，数据已刷新')
        return true
      } else {
        console.error('排课失败:', response.message)
        return false
      }
    } catch (error) {
      console.error('排课异常:', error)
      return false
    }
  }

  const startCourse = async (courseId) => {
    try {
      const response = await curriculumApi.startCourse(courseId)
      if (response.code === 200) {
        ElMessage.success('开始上课成功')
        updateCourseStatus(courseId, 'ongoing')
        // 打开新标签页到上课页面
        window.open(`/course/index?courseId=${courseId}`, '_blank')
        return true
      }
    } catch (error) {
      console.error('开始上课失败:', error)
      return false
    }
  }

  const cancelCourse = async (courseId, data) => {
    try {
      const response = await curriculumApi.cancelCourse(courseId, data)
      if (response.code === 200) {
        ElMessage.success('停课成功')
        updateCourseStatus(courseId, 'cancelled')
        return true
      }
    } catch (error) {
      console.error('停课失败:', error)
      return false
    }
  }

  const rescheduleCourse = async (data) => {
    try {
      const response = await curriculumApi.rescheduleCourse(data)
      if (response.code === 200) {
        // 更新课程时间
        const courseIndex = schedules.value.findIndex(course => course.id === data.courseId)
        if (courseIndex !== -1) {
          const course = schedules.value[courseIndex]
          const newStartTime = new Date(`${data.newDate} ${data.newStartTime}`)
          const newEndTime = new Date(`${data.newDate} ${data.newEndTime}`)

          course.startTime = newStartTime.toISOString()
          course.endTime = newEndTime.toISOString()
        }
        return true
      }
    } catch (error) {
      console.error('调课失败:', error)
      return false
    }
  }

  const endCourse = async (courseId) => {
    try {
      const response = await curriculumApi.endCourse(courseId)
      if (response.code === 200) {
        ElMessage.success('结束上课成功')
        updateCourseStatus(courseId, 'completed')
        return true
      } else {
        return false
      }
    } catch (error) {
      console.error('结束上课失败:', error)
      return false
    }
  }

  const startReview = async (reviewId, courseId) => {
    try {
      console.log('🔄 [抗遗忘复习] 开始复习:', { reviewId, courseId })
      const response = await curriculumApi.startReview(courseId, reviewId)
      if (response.code === 200) {
        ElMessage.success('开始抗遗忘复习成功')
        updateReviewStatus(reviewId, '进行中')
        // 打开新标签页到复习页面
        window.open(`/course/index?courseId=${courseId}&reviewId=${reviewId}`, '_blank')
        return true
      } else {
        return false
      }
    } catch (error) {
      console.error('开始抗遗忘复习失败:', error)
      return false
    }
  }

  const completeReview = async (reviewId) => {
    try {
      const response = await curriculumApi.completeReview(reviewId)
      if (response.code === 200) {
        ElMessage.success('完成复习成功')
        updateReviewStatus(reviewId, 'completed')
        return true
      } else {
        return false
      }
    } catch (error) {
      console.error('完成复习失败:', error)
      return false
    }
  }

  const downloadCourseMaterial = async (courseId, type) => {
    try {
      const response = await curriculumApi.downloadCourseMaterial(courseId, type)
      if (response.code === 200) {
        if (response.data && response.data.downloadUrl) {
          const link = document.createElement('a')
          link.href = response.data.downloadUrl
          link.target = '_blank'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
        return true
      } else {
        ElMessage.error(response.message || '下载失败')
        return false
      }
    } catch (error) {
      console.error('下载课程资料失败:', error)
      return false
    }
  }

  const consumeCourse = async (data) => {
    try {
      const response = await curriculumApi.consumeCourse(data)
      if (response.code === 200) {
        // 重新获取课表数据
        await refreshCurrentSchedules()
        return true
      } else {
        ElMessage.error(response.message || '消课失败')
        return false
      }
    } catch (error) {
      console.error('消课失败:', error)
      return false
    }
  }

  const updateCourseStatus = (courseId, status) => {
    const course = schedules.value.find(item => item.id === courseId)
    if (course) {
      course.status = status
    }
  }

  const updateReviewStatus = (reviewId, status) => {
    const review = reviewPlans.value.find(item => item.id === reviewId)
    if (review) {
      review.status = status
    }
  }

  const setCurrentView = (view) => {
    currentView.value = view
  }

  const setSelectedDate = (date) => {
    selectedDate.value = date
  }

  // 存储当前的查询参数，用于刷新时保持筛选条件
  const currentQueryParams = ref({})

  const refreshCurrentSchedules = async () => {
    const params = {
      viewType: currentView.value,
      startDate: formatDate(getViewStartDate()),
      endDate: formatDate(getViewEndDate()),
      type: 'all',
      // 保持之前的筛选参数
      ...currentQueryParams.value
    }
    await fetchSchedules(params)
  }

  const getViewStartDate = () => {
    if (currentView.value === 'week') {
      return getStartOfWeek(selectedDate.value)
    } else {
      return getStartOfMonth(selectedDate.value)
    }
  }

  const getViewEndDate = () => {
    if (currentView.value === 'week') {
      return getEndOfWeek(selectedDate.value)
    } else {
      return getEndOfMonth(selectedDate.value)
    }
  }

  // 工具函数
  const getStartOfWeek = (date) => {
    const start = new Date(date)
    const day = start.getDay()
    const diff = start.getDate() - day + (day === 0 ? -6 : 1) // 周一为起始
    start.setDate(diff)
    start.setHours(0, 0, 0, 0)
    return start
  }

  const getEndOfWeek = (date) => {
    const end = new Date(getStartOfWeek(date))
    end.setDate(end.getDate() + 6)
    end.setHours(23, 59, 59, 999)
    return end
  }

  const getStartOfMonth = (date) => {
    const start = new Date(date.getFullYear(), date.getMonth(), 1)
    start.setHours(0, 0, 0, 0)
    return start
  }

  const getEndOfMonth = (date) => {
    const end = new Date(date.getFullYear(), date.getMonth() + 1, 0)
    end.setHours(23, 59, 59, 999)
    return end
  }

  const formatDate = (date) => {
    return date.toISOString().split('T')[0]
  }

  return {
    // 状态
    schedules,
    reviewPlans,
    currentView,
    selectedDate,
    loading,
    students,
    teachers,
    teacherAvailableTimeSlots,
    currentQueryParams,
    
    // 计算属性
    todayReviews,
    futureReviews,
    currentWeekSchedules,
    currentMonthSchedules,
    
    // 方法
    fetchSchedules,
    fetchReviewPlans,
    fetchStudents,
    fetchTeachers,
    fetchTeacherAvailableTimeSlots,
    createSchedule,
    startCourse,
    cancelCourse,
    rescheduleCourse,
    endCourse,
    startReview,
    completeReview,
    downloadCourseMaterial,
    consumeCourse,
    updateCourseStatus,
    updateReviewStatus,
    setCurrentView,
    setSelectedDate,
    refreshCurrentSchedules
  }
})