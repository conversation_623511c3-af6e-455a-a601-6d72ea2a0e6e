import request from '@/utils/request'

// 获取可分配学生列表
export function getAvailableStudentsApi(params) {
  return request({
    url: '/student-assignment/available-students',
    method: 'get',
    params
  })
}

// 分配学生到销售
export function assignStudentsApi(data) {
  return request({
    url: '/student-assignment/assign',
    method: 'post',
    data
  })
}

// 批量分配学生
export function batchAssignStudentsApi(data) {
  return request({
    url: '/student-assignment/batch-assign',
    method: 'post',
    data
  })
}

// 取消学生分配
export function unassignStudentApi(studentId) {
  return request({
    url: `/student-assignment/unassign/${studentId}`,
    method: 'post'
  })
}

// 重新分配学生
export function reassignStudentApi(data) {
  return request({
    url: '/student-assignment/reassign',
    method: 'post',
    data
  })
}

// 智能自动分配
export function autoAssignStudentsApi(data) {
  return request({
    url: '/student-assignment/auto-assign',
    method: 'post',
    data
  })
}

// 获取分配历史
export function getAssignmentHistoryApi(params) {
  return request({
    url: '/student-assignment/history',
    method: 'get',
    params
  })
}

// 获取分配统计信息
export function getAssignmentStatsApi() {
  return request({
    url: '/student-assignment/stats',
    method: 'get'
  })
}

// 获取销售组选项
export function getSalesGroupOptionsApi() {
  return request({
    url: '/student-assignment/sales-group-options',
    method: 'get'
  })
}

// 获取销售人员选项
export function getSalesOptionsApi(params) {
  return request({
    url: '/student-assignment/sales-options',
    method: 'get',
    params
  })
}

// 获取工作负载分布
export function getWorkloadDistributionApi(params) {
  return request({
    url: '/student-assignment/workload-distribution',
    method: 'get',
    params
  })
}
