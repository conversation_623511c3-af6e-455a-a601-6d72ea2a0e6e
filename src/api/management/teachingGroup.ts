import request from '@/utils/request'
import type {
  ApiResponse,
  PageResponse,
  TeachingGroup,
  Teacher,
  TeacherTimeSlot,
  TeacherTeachingInfo,
  CreateTeachingGroupParams,
  UpdateTeachingGroupParams,
  GetTeachingGroupsParams,
  AssignTeacherParams,
  RemoveTeacherParams,
  GetGroupTeachersParams,
  GetTeacherTimeSlotsParams,
  UpdateTeacherTimeSlotsParams,
  GetTeacherTeachingInfoParams,
  UserRole,
  AvailableTeacher,
  TeachingGroupStats
} from './types'

/**
 * 获取教学组列表
 */
export function getTeachingGroupsApi(params: GetTeachingGroupsParams): Promise<ApiResponse<PageResponse<TeachingGroup>>> {
  return request({
    url: '/management/teaching-groups',
    method: 'get',
    params
  })
}

/**
 * 获取教学组详情
 */
export function getTeachingGroupApi(id: string): Promise<ApiResponse<TeachingGroup>> {
  return request({
    url: `/management/teaching-groups/${id}`,
    method: 'get'
  })
}

/**
 * 创建教学组
 */
export function createTeachingGroupApi(data: CreateTeachingGroupParams): Promise<ApiResponse<void>> {
  return request({
    url: '/management/teaching-groups',
    method: 'post',
    data
  })
}

/**
 * 更新教学组
 */
export function updateTeachingGroupApi(data: UpdateTeachingGroupParams): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除教学组
 */
export function deleteTeachingGroupApi(id: string): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除教学组
 */
export function deleteTeachingGroupsApi(ids: string[]): Promise<ApiResponse<void>> {
  return request({
    url: '/management/teaching-groups/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 获取教学组教师列表
 */
export function getGroupTeachersApi(params: GetGroupTeachersParams): Promise<ApiResponse<PageResponse<Teacher>>> {
  return request({
    url: `/management/teaching-groups/${params.groupId}/teachers`,
    method: 'get',
    params: {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      name: params.name,
      status: params.status
    }
  })
}

/**
 * 分配教师到教学组
 */
export function assignTeachersApi(data: AssignTeacherParams): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${data.groupId}/teachers`,
    method: 'post',
    data: { teacherIds: data.teacherIds }
  })
}

/**
 * 从教学组移除教师
 */
export function removeTeachersApi(data: RemoveTeacherParams): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${data.groupId}/teachers`,
    method: 'delete',
    data: { teacherIds: data.teacherIds }
  })
}

/**
 * 获取可分配的教师列表（未分配到任何教学组的教师）
 */
export function getAvailableTeachersApi(): Promise<ApiResponse<AvailableTeacher[]>> {
  return request({
    url: '/management/teachers/available',
    method: 'get'
  })
}

/**
 * 获取教师列表（支持搜索和分页）
 */
export function getTeachersApi(params: any): Promise<ApiResponse<PageResponse<Teacher>>> {
  return request({
    url: '/management/teachers',
    method: 'get',
    params
  })
}

/**
 * 获取所有教师列表（用于选择组长、教务）
 */
export function getAllTeachersApi(): Promise<ApiResponse<UserRole[]>> {
  return request({
    url: '/management/teachers/all',
    method: 'get'
  })
}

/**
 * 获取可选老师列表（根据权限控制）
 */
export function getTeacherSelectListsApi(): Promise<ApiResponse<{id: string, name: string}[]>> {
  return request({
    url: '/management/teacher/select-lists',
    method: 'get'
  })
}

/**
 * 获取教师可上课时间
 */
export function getTeacherTimeSlotsApi(params: GetTeacherTimeSlotsParams): Promise<ApiResponse<TeacherTimeSlot[]>> {
  return request({
    url: `/management/teachers/${params.teacherId}/time-slots`,
    method: 'get'
  })
}

/**
 * 更新教师可上课时间
 */
export function updateTeacherTimeSlotsApi(data: UpdateTeacherTimeSlotsParams): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teachers/${data.teacherId}/time-slots`,
    method: 'put',
    data: { timeSlots: data.timeSlots }
  })
}

/**
 * 检查教师时间表最后更新时间
 */
export function checkTeacherTimeSlotUpdateTimeApi(teacherId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/teachers/${teacherId}/time-slots/update-check`,
    method: 'get'
  })
}

/**
 * 获取教师详细信息
 */
export function getTeacherDetailApi(teacherId: string): Promise<ApiResponse<Teacher>> {
  return request({
    url: `/management/teachers/${teacherId}/detail`,
    method: 'get'
  })
}

/**
 * 获取教师带教信息
 */
export function getTeacherTeachingInfoApi(params: GetTeacherTeachingInfoParams): Promise<ApiResponse<TeacherTeachingInfo>> {
  return request({
    url: `/management/teachers/${params.teacherId}/teaching-info`,
    method: 'get'
  })
}

/**
 * 批量获取教师带教信息
 */
export function getTeacherTeachingInfoBatchApi(teacherIds: string[]): Promise<ApiResponse<TeacherTeachingInfo[]>> {
  return request({
    url: '/management/teachers/teaching-info/batch',
    method: 'post',
    data: { teacherIds }
  })
}

/**
 * 更新教师信息
 */
export function updateTeacherInfoApi(teacherId: string, data: any): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teachers/${teacherId}`,
    method: 'put',
    data
  })
}

/**
 * 更新教师暑期课上课时间
 */
export function updateTeacherSummerScheduleApi(teacherId: string, data: { summerScheduleType: string }): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teachers/${teacherId}/summer-schedule`,
    method: 'put',
    data
  })
}

/**
 * 获取教学组统计信息
 */
export function getTeachingGroupStatsApi(): Promise<ApiResponse<TeachingGroupStats>> {
  return request({
    url: '/management/teaching-groups/stats',
    method: 'get'
  })
}

/**
 * 导出教学组列表
 */
export function exportTeachingGroupsApi(params: GetTeachingGroupsParams): Promise<Blob> {
  return request({
    url: '/management/teaching-groups/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 导出教学组教师列表
 */
export function exportGroupTeachersApi(groupId: string): Promise<Blob> {
  return request({
    url: `/management/teaching-groups/${groupId}/teachers/export`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 获取教师课表
 */
export function getTeacherScheduleApi(teacherId: string, params?: any): Promise<ApiResponse<any[]>> {
  return request({
    url: `/management/teachers/${teacherId}/schedule`,
    method: 'get',
    params
  })
}

/**
 * 创建教师
 */
export function createTeacherApi(data: any): Promise<ApiResponse<any>> {
  return request({
    url: '/management/teachers',
    method: 'post',
    data
  })
}

/**
 * 分配学生给教师
 */
export function assignStudentsToTeacherApi(data: {
  teacherId: string;
  studentAssignments: Array<{
    studentId: string;
    subject: string;
    courseType: string;
  }>
}): Promise<ApiResponse<any>> {
  return request({
    url: '/management/teachers/assign-students',
    method: 'post',
    data
  })
}

/**
 * 获取教师的学生列表
 */
export function getTeacherStudentsApi(teacherId: string): Promise<ApiResponse<any[]>> {
  return request({
    url: `/management/teachers/${teacherId}/students`,
    method: 'get'
  })
}

/**
 * 删除教师
 */
export function deleteTeacherApi(teacherId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/teachers/${teacherId}`,
    method: 'delete'
  })
}

/**
 * 批量删除教师
 */
export function batchDeleteTeachersApi(teacherIds: string[]): Promise<ApiResponse<any>> {
  return request({
    url: '/management/teachers/batch-delete',
    method: 'delete',
    data: teacherIds
  })
}

/**
 * 重置教师密码
 */
export function resetTeacherPasswordApi(teacherId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/teachers/${teacherId}/reset-password`,
    method: 'put'
  })
}

/**
 * 导出教师列表
 */
export function exportTeachersApi(params?: any): Promise<ApiResponse<any[]>> {
  return request({
    url: '/management/teachers/export',
    method: 'get',
    params
  })
}

/**
 * 批量导入教师
 */
export function importTeachersApi(teacherDataList: any[]): Promise<ApiResponse<any>> {
  return request({
    url: '/management/teachers/import',
    method: 'post',
    data: teacherDataList
  })
}

/**
 * 更新教师状态（停课/复课）
 */
export function updateTeacherStatusApi(teacherId: string, status: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/teachers/${teacherId}/status`,
    method: 'put',
    params: { status }
  })
}

// 导出所有API
export const teachingGroupApi = {
  getTeachingGroups: getTeachingGroupsApi,
  getTeachingGroup: getTeachingGroupApi,
  createTeachingGroup: createTeachingGroupApi,
  updateTeachingGroup: updateTeachingGroupApi,
  deleteTeachingGroup: deleteTeachingGroupApi,
  deleteTeachingGroups: deleteTeachingGroupsApi,
  getGroupTeachers: getGroupTeachersApi,
  assignTeachers: assignTeachersApi,
  removeTeachers: removeTeachersApi,
  getAvailableTeachers: getAvailableTeachersApi,
  getTeachers: getTeachersApi,
  getAllTeachers: getAllTeachersApi,
  getTeacherDetail: getTeacherDetailApi,
  getTeacherTimeSlots: getTeacherTimeSlotsApi,
  updateTeacherTimeSlots: updateTeacherTimeSlotsApi,
  checkTeacherTimeSlotUpdateTime: checkTeacherTimeSlotUpdateTimeApi,
  getTeacherTeachingInfo: getTeacherTeachingInfoApi,
  getTeacherTeachingInfoBatch: getTeacherTeachingInfoBatchApi,
  updateTeacherInfo: updateTeacherInfoApi,
  updateTeacherSummerSchedule: updateTeacherSummerScheduleApi,
  getTeachingGroupStats: getTeachingGroupStatsApi,
  exportTeachingGroups: exportTeachingGroupsApi,
  exportGroupTeachers: exportGroupTeachersApi,
  getTeacherSchedule: getTeacherScheduleApi,
  createTeacher: createTeacherApi,
  deleteTeacher: deleteTeacherApi,
  batchDeleteTeachers: batchDeleteTeachersApi,
  resetTeacherPassword: resetTeacherPasswordApi,
  exportTeachers: exportTeachersApi,
  assignStudentsToTeacher: assignStudentsToTeacherApi,
  getTeacherStudents: getTeacherStudentsApi
}

export default teachingGroupApi
