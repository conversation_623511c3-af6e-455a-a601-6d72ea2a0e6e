import request from '@/utils/request'

/**
 * 订单管理API（管理员专用）
 */

// 分页查询订单列表
export function getOrderManagerListApi(params) {
  return request({
    url: '/order-manager/list',
    method: 'get',
    params
  })
}

// 获取订单统计信息
export function getOrderStatisticsApi() {
  return request({
    url: '/order-manager/statistics',
    method: 'get'
  })
}

// 批量取消订单
export function batchCancelOrdersApi(orderIds) {
  return request({
    url: '/order-manager/batch-cancel',
    method: 'put',
    data: orderIds
  })
}

// 获取订单详情（管理员视图）
export function getOrderManagerDetailApi(orderId) {
  return request({
    url: `/order-manager/${orderId}/detail`,
    method: 'get'
  })
}

// 管理员导出订单数据
export function managerExportOrdersApi(params) {
  return request({
    url: '/order-manager/export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}

// 管理员导出交易流水数据
export function managerExportOrderTrxApi(params) {
  return request({
    url: '/order-manager/trx/export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}

// 获取退款计算数据
export function calculateRefundDataApi(orderNo) {
  return request({
    url: `/refund-records/${orderNo}/calculate-refund`,
    method: 'get'
  })
}

// 财务退款
export function financialRefundApi(orderId, refundData) {
  return request({
    url: `/refund-records/${orderId}/financial-refund`,
    method: 'post',
    data: refundData
  })
}

// 继承原有订单API的部分功能
export {
  createOrderApi,
  getOrderDetailApi,
  getOrderByNoApi,
  getOrdersByStudentApi,
  getOrdersBySalerApi,
  getOrdersByStatusApi,
  getOrderTransactionsApi,
  nativePayApi,
  cancelOrderApi,
  generatePaymentApi,
  generateQRCodeApi,
  getPaymentLinkApi,
  initiateContractSignApi
} from './order'