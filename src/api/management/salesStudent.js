import request from '@/utils/request'

// 获取销售学生列表
export function getSalesStudentListApi(params) {
  return request({
    url: '/sales/student/list',
    method: 'get',
    params
  })
}

// 获取销售学生详情
export function getSalesStudentDetailApi(id) {
  return request({
    url: `/sales/student/${id}`,
    method: 'get'
  })
}

// 创建销售学生
export function createSalesStudentApi(data) {
  return request({
    url: '/sales/student',
    method: 'post',
    data
  })
}

// 更新销售学生
export function updateSalesStudentApi(data) {
  return request({
    url: '/sales/student',
    method: 'put',
    data
  })
}

// 删除销售学生
export function deleteSalesStudentApi(ids) {
  return request({
    url: `/sales/student/${ids}`,
    method: 'delete'
  })
}

// 分配学生到销售
export function assignStudentToSalesApi(data) {
  return request({
    url: '/sales/student/assign',
    method: 'post',
    data
  })
}

// 批量分配学生到销售
export function batchAssignStudentsToSalesApi(data) {
  return request({
    url: '/sales/student/batch-assign',
    method: 'post',
    data
  })
}

// 从销售移除学生
export function removeStudentFromSalesApi(studentId, reason) {
  return request({
    url: `/sales/student/${studentId}/sales`,
    method: 'delete',
    params: { reason }
  })
}

// 获取可分配的学生列表
export function getAvailableStudentsApi() {
  return request({
    url: '/sales/student/available',
    method: 'get'
  })
}

// 获取销售学生统计信息
export function getSalesStudentStatsApi() {
  return request({
    url: '/sales/student/stats',
    method: 'get'
  })
}

// 导入学生数据
export function importStudentsApi(file, data) {
  const formData = new FormData()
  formData.append('file', file)
  if (data.salesId) {
    formData.append('salesId', data.salesId)
  }
  if (data.updateSupport !== undefined) {
    formData.append('updateSupport', data.updateSupport)
  }
  
  return request({
    url: '/sales/student/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出学生数据
export function exportStudentsApi(data) {
  return request({
    url: '/sales/student/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 下载学生导入模板
export function downloadStudentTemplateApi() {
  return request({
    url: '/sales/student/template',
    method: 'post',
    responseType: 'blob'
  })
}

// 获取学生分配历史
export function getStudentAssignmentHistoryApi(studentId) {
  return request({
    url: `/sales/student/${studentId}/history`,
    method: 'get'
  })
}

// 获取销售人员选项
export function getSalesOptionsApi(params) {
  return request({
    url: '/sales/student/sales-options',
    method: 'get',
    params
  })
}

// 获取销售组选项
export function getSalesGroupOptionsApi() {
  return request({
    url: '/sales/student/group-options',
    method: 'get'
  })
}
