import request from '@/utils/request'

/**
 * 老师可排课时间看板API
 */
export const teacherScheduleDashboardApi = {
  /**
   * 获取看板数据
   * @param {Object} params 查询参数
   * @param {string} params.startDate 开始日期
   * @param {string} params.endDate 结束日期
   * @param {Array} params.groupIds 教学组ID列表
   */
  getDashboard(params) {
    return request({
      url: '/api/teacher-schedule-dashboard/dashboard',
      method: 'post',
      data: params
    })
  },

  /**
   * 获取快捷日期范围
   * @param {string} quickRange 快捷范围类型
   */
  getQuickDateRange(quickRange) {
    return request({
      url: `/api/teacher-schedule-dashboard/quick-date-range/${quickRange}`,
      method: 'get'
    })
  },

  /**
   * 获取所有快捷日期选项
   */
  getQuickDateOptions() {
    return request({
      url: '/api/teacher-schedule-dashboard/quick-date-options',
      method: 'get'
    })
  },

  /**
   * 计算单个教师指定日期的可排课课次
   * @param {string} teacherId 教师ID
   * @param {string} date 日期
   */
  getTeacherAvailableSlots(teacherId, date) {
    return request({
      url: `/api/teacher-schedule-dashboard/teacher/${teacherId}/available-slots`,
      method: 'get',
      params: { date }
    })
  },

  /**
   * 验证日期范围
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   */
  validateDateRange(startDate, endDate) {
    return request({
      url: '/api/teacher-schedule-dashboard/validate-date-range',
      method: 'get',
      params: { startDate, endDate }
    })
  },

  /**
   * 获取今天的日期
   */
  getToday() {
    return request({
      url: '/api/teacher-schedule-dashboard/today',
      method: 'get'
    })
  },

  /**
   * 获取看板配置信息
   */
  getConfig() {
    return request({
      url: '/api/teacher-schedule-dashboard/config',
      method: 'get'
    })
  },

  /**
   * 获取指定日期的教师详细数据
   * @param {Object} params 查询参数
   * @param {string} params.date 日期
   * @param {Array} params.groupIds 教学组ID列表
   */
  getDayTeacherDetail(params) {
    return request({
      url: '/api/teacher-schedule-dashboard/day-teacher-detail',
      method: 'post',
      data: params
    })
  },

  /**
   * 获取教师在指定日期的已排课程
   * @param {string} teacherId 教师ID
   * @param {string} date 日期
   */
  getTeacherScheduledCourses(teacherId, date) {
    return request({
      url: `/api/teacher-schedule-dashboard/teacher/${teacherId}/scheduled-courses`,
      method: 'get',
      params: { date }
    })
  },

  /**
   * 获取教学组趋势数据
   * @param {Object} params 查询参数
   * @param {string} params.groupId 教学组ID
   * @param {string} params.startDate 开始日期
   * @param {string} params.endDate 结束日期
   */
  getGroupTrend(params) {
    return request({
      url: '/api/teacher-schedule-dashboard/group-trend',
      method: 'post',
      data: params
    })
  },

  /**
   * 获取教师排课详情
   * @param {string} teacherId 教师ID
   * @param {string} date 日期
   */
  getTeacherScheduleDetail(teacherId, date) {
    return request({
      url: `/api/teacher-schedule-dashboard/teacher/${teacherId}/schedule-detail`,
      method: 'get',
      params: { date }
    })
  },

  /**
   * 导出看板数据
   * @param {Object} params 查询参数
   */
  exportDashboardData(params) {
    return request({
      url: '/api/teacher-schedule-dashboard/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  },

  /**
   * 导出指定日期的教师数据
   * @param {Object} params 查询参数
   */
  exportDayTeacherData(params) {
    return request({
      url: '/api/teacher-schedule-dashboard/export-day-teacher',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  }
}

/**
 * 课消配置API
 */
export const courseHoursConfigApi = {
  /**
   * 获取当前配置状态
   */
  getStatus() {
    return request({
      url: '/api/course-hours-config/status',
      method: 'get'
    })
  },

  /**
   * 设置课消功能开关
   * @param {boolean} enabled 是否启用
   */
  setCourseHoursEnabled(enabled) {
    return request({
      url: `/api/course-hours-config/enabled/${enabled}`,
      method: 'post'
    })
  },

  /**
   * 重置为默认值
   */
  reset() {
    return request({
      url: '/api/course-hours-config/reset',
      method: 'post'
    })
  },

  /**
   * 获取Redis键值信息（调试用）
   */
  getRedisKeys() {
    return request({
      url: '/api/course-hours-config/debug/redis-keys',
      method: 'get'
    })
  }
}
