import request from '@/utils/request'

export interface ContractsQueryReq {
  pageNum: number
  pageSize: number
  orderId?: string
  signerUserId?: string
  status?: string
}

export interface ContractsListItem {
  id: string
  orderId: string
  docTemplateId?: string
  fileId?: string
  signerUserId?: string
  status?: string
  fillTime?: string
  signTime?: string
  createTime?: string
}

export interface ContractsDetailResp {
  id: string
  contractNo: string
  orderNo: string
  signerUsername?: string
  initiateUsername?: string
  status?: string
  signTime?: any
  signUrlInfo?: any
  createBy?: string
}

export function getContractsList(query: ContractsQueryReq) {
  return request({
    url: '/contracts/list',
    method: 'get',
    params: query
  })
}

export function getContractDetail(id: string) : ContractsDetailResp {
  return request({
    url: `/contracts/${id}`,
    method: 'get'
  })
}

