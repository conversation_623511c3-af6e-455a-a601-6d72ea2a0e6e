import request from '@/utils/request'

// 获取预约课申请列表
export function getCourseBookingListApi(params) {
  return request({
    url: '/sales/course-booking/list',
    method: 'get',
    params
  })
}

// 获取预约课申请详情
export function getCourseBookingDetailApi(id) {
  return request({
    url: `/sales/course-booking/${id}`,
    method: 'get'
  })
}

// 创建预约课申请
export function createCourseBookingApi(data) {
  return request({
    url: '/sales/course-booking',
    method: 'post',
    data
  })
}

// 更新预约课申请
export function updateCourseBookingApi(data) {
  return request({
    url: '/sales/course-booking',
    method: 'put',
    data
  })
}

// 删除预约课申请
export function deleteCourseBookingApi(ids) {
  return request({
    url: `/sales/course-booking/${ids}`,
    method: 'delete'
  })
}

// 自动匹配教师
export function autoMatchTeachersApi(data) {
  return request({
    url: '/sales/course-booking/auto-match-teachers',
    method: 'post',
    data
  })
}

// 审批预约课申请
export function approveCourseBookingApi(data) {
  return request({
    url: '/sales/course-booking/approve',
    method: 'post',
    data
  })
}

// 拒绝预约课申请
export function rejectCourseBookingApi(data) {
  return request({
    url: '/sales/course-booking/reject',
    method: 'post',
    data
  })
}

// 撤回预约课申请
export function withdrawCourseBookingApi(applicationId, reason) {
  return request({
    url: `/sales/course-booking/${applicationId}/withdraw`,
    method: 'post',
    params: { reason }
  })
}

// 批量审批预约课申请
export function batchApproveCourseBookingApi(data) {
  return request({
    url: '/sales/course-booking/batch-approve',
    method: 'post',
    data
  })
}

// 获取预约课统计信息
export function getCourseBookingStatsApi() {
  return request({
    url: '/sales/course-booking/stats',
    method: 'get'
  })
}

// 获取可选教师列表
export function getAvailableTeachersApi(params) {
  return request({
    url: '/sales/course-booking/available-teachers',
    method: 'get',
    params
  })
}

// 获取教学组列表
export function getTeachingGroupsApi() {
  return request({
    url: '/sales/course-booking/teaching-groups',
    method: 'get'
  })
}

// 检查教师可用性
export function checkTeacherAvailabilityApi(teacherId, timeSlots) {
  return request({
    url: '/sales/course-booking/check-teacher-availability',
    method: 'post',
    params: { teacherId },
    data: timeSlots
  })
}
