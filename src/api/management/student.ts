import request from '@/utils/request'

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

/**
 * 获取学生列表
 */
export function getStudentsApi(params?: any): Promise<ApiResponse<any>> {
  return request({
    url: '/management/students',
    method: 'get',
    params
  })
}

/**
 * 获取学生详情
 */
export function getStudentDetailApi(studentId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/students/${studentId}`,
    method: 'get'
  })
}

/**
 * 创建学生
 */
export function createStudentApi(data: any): Promise<ApiResponse<any>> {
  return request({
    url: '/management/students',
    method: 'post',
    data
  })
}

/**
 * 更新学生信息
 */
export function updateStudentApi(studentId: string, data: any): Promise<ApiResponse<any>> {
  return request({
    url: `/management/students/${studentId}`,
    method: 'put',
    data
  })
}

/**
 * 删除学生
 */
export function deleteStudentApi(studentId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/students/${studentId}`,
    method: 'delete'
  })
}

/**
 * 批量删除学生
 */
export function deleteStudentsApi(studentIds: string[]): Promise<ApiResponse<any>> {
  return request({
    url: '/management/students',
    method: 'delete',
    data: studentIds
  })
}

/**
 * 获取学生统计信息
 */
export function getStudentStatsApi(): Promise<ApiResponse<any>> {
  return request({
    url: '/management/students/stats',
    method: 'get'
  })
}

/**
 * 获取学生课表
 */
export function getStudentScheduleApi(studentId: string, params?: any): Promise<ApiResponse<any[]>> {
  return request({
    url: `/management/students/${studentId}/schedule`,
    method: 'get',
    params
  })
}

/**
 * 获取可分配的学生列表
 */
export function getAvailableStudentsApi(): Promise<ApiResponse<any[]>> {
  return request({
    url: '/management/students/available',
    method: 'get'
  })
}

/**
 * 获取学生课程统计
 */
export function getStudentCourseStatsApi(studentId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/students/${studentId}/course-stats`,
    method: 'get'
  })
}

/**
 * 获取学生最近课程
 */
export function getStudentRecentCoursesApi(studentId: string, params?: any): Promise<ApiResponse<any[]>> {
  return request({
    url: `/management/students/${studentId}/recent-courses`,
    method: 'get',
    params
  })
}

/**
 * 根据教师获取学生列表
 */
export function getStudentsByTeacherApi(teacherId: string): Promise<ApiResponse<any[]>> {
  return request({
    url: `/management/students/by-teacher/${teacherId}`,
    method: 'get'
  })
}

/**
 * 分配教师给学生
 */
export function assignTeacherToStudentApi(studentId: string, teacherId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/students/${studentId}/assign-teacher/${teacherId}`,
    method: 'put'
  })
}

/**
 * 取消学生的教师分配
 */
export function unassignTeacherFromStudentApi(studentId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/management/students/${studentId}/teacher`,
    method: 'delete'
  })
}

/**
 * 获取可用教师列表
 */
export function getAvailableTeachersApi(): Promise<ApiResponse<any[]>> {
  return request({
    url: '/management/teachers/available',
    method: 'get'
  })
}

/**
 * 导出学生列表
 */
export function exportStudentsApi(params?: any): Promise<Blob> {
  return request({
    url: '/management/students/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出所有API
export const studentApi = {
  getStudents: getStudentsApi,
  getStudentDetail: getStudentDetailApi,
  createStudent: createStudentApi,
  updateStudent: updateStudentApi,
  deleteStudent: deleteStudentApi,
  deleteStudents: deleteStudentsApi,
  getStudentStats: getStudentStatsApi,
  getStudentSchedule: getStudentScheduleApi,
  getAvailableStudents: getAvailableStudentsApi,
  getStudentCourseStats: getStudentCourseStatsApi,
  getStudentRecentCourses: getStudentRecentCoursesApi,
  getStudentsByTeacher: getStudentsByTeacherApi,
  assignTeacherToStudent: assignTeacherToStudentApi,
  unassignTeacherFromStudent: unassignTeacherFromStudentApi,
  getAvailableTeachers: getAvailableTeachersApi,
  exportStudents: exportStudentsApi
}
