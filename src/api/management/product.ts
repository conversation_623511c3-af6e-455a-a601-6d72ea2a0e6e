import request from '@/utils/request'

// 类型定义
export interface QueryParams {
  pageNum?: number
  pageSize?: number
  name?: string
  subject?: string
  courseType?: string
  status?: string
}

export interface ProductForm {
  id?: string
  name: string
  description?: string
  subject: string
  courseType?: string
  applicableGrades: string[]
  unitPrice: number
  quantity: number
  hasBonusHours: boolean
  bonusHoursQuantity: number
  hasMaterialFee: boolean
  materialFee: number
  originalPrice: number
  sellingPrice: number
  status: string
  validStatus?: string
  invalidReason?: string
  customerType?: string
  sortOrder: number
  remark?: string
}

export interface ProductItem {
  id: string
  name: string
  description?: string
  subject: string
  courseType?: string
  applicableGrades: string[]
  unitPrice: number
  quantity: number
  hasBonusHours: boolean
  bonusHoursQuantity: number
  hasMaterialFee: boolean
  materialFee: number
  originalPrice: number
  sellingPrice: number
  status: string
  validStatus?: string
  invalidReason?: string
  customerType?: string
  sortOrder: number
  salesCount: number
  createTime: string
  updateTime: string
}

export interface GetAvailableParams {
  name?: string
  subject?: string
  courseType?: string
  showInvalid?: boolean
  studentId?: string
}

export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

export interface PageResponse<T = any> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 产品管理API
 */

// 获取产品列表
export function getProductListApi(params: QueryParams): Promise<ApiResponse<PageResponse<ProductItem>>> {
  return request({
    url: '/management/products',
    method: 'get',
    params
  })
}

// 获取产品详情
export function getProductDetailApi(id: string): Promise<ApiResponse<ProductItem>> {
  return request({
    url: `/management/products/${id}`,
    method: 'get'
  })
}

// 创建产品
export function createProductApi(data: ProductForm): Promise<ApiResponse<string>> {
  return request({
    url: '/management/products',
    method: 'post',
    data
  })
}

// 更新产品
export function updateProductApi(data: ProductForm): Promise<ApiResponse<boolean>> {
  return request({
    url: '/management/products',
    method: 'put',
    data
  })
}

// 删除产品
export function deleteProductApi(ids: string): Promise<ApiResponse<boolean>> {
  return request({
    url: `/management/products/${ids}`,
    method: 'delete'
  })
}

// 上架产品
export function enableProductApi(id: string): Promise<ApiResponse<boolean>> {
  return request({
    url: `/management/products/${id}/enable`,
    method: 'put'
  })
}

// 下架产品
export function disableProductApi(id: string): Promise<ApiResponse<boolean>> {
  return request({
    url: `/management/products/${id}/disable`,
    method: 'put'
  })
}

// 获取所有上架的产品列表（用于下单选择）
// export function getAvailableProductsApi(data: GetAvailableParams): Promise<ApiResponse<ProductItem[]>> {
//   return request({
//     url: '/management/products/available/list',
//     method: 'post',
//     data
//   })
// }

// 根据类型查询产品列表
export function getProductsByTypeApi(type: string): Promise<ApiResponse<ProductItem[]>> {
  return request({
    url: `/management/products/type/${type}`,
    method: 'get'
  })
}

// 根据学科查询产品列表
export function getProductsBySubjectApi(subject: string): Promise<ApiResponse<ProductItem[]>> {
  return request({
    url: `/management/products/subject/${subject}`,
    method: 'get'
  })
}

// 根据标签查询产品列表
export function getProductsByTagApi(tag: string): Promise<ApiResponse<ProductItem[]>> {
  return request({
    url: `/management/products/tag/${tag}`,
    method: 'get'
  })
}

// 查询热门产品列表
export function getHotProductsApi(limit: number = 10): Promise<ApiResponse<ProductItem[]>> {
  return request({
    url: '/management/products/hot',
    method: 'get',
    params: { limit }
  })
}

// 查询产品统计信息
export function getProductStatisticsApi(): Promise<ApiResponse<any>> {
  return request({
    url: '/management/products/statistics',
    method: 'get'
  })
}

// 更新产品库存
export function updateProductStockApi(id: string, quantity: number): Promise<ApiResponse<boolean>> {
  return request({
    url: `/management/products/${id}/stock`,
    method: 'put',
    params: { quantity }
  })
}

// 检查产品库存
export function checkProductStockApi(id: string, quantity: number): Promise<ApiResponse<boolean>> {
  return request({
    url: `/management/products/${id}/stock/check`,
    method: 'get',
    params: { quantity }
  })
}

// 获取所有上架的产品列表（用于下单选择）
export function getAvailableProductsApi(params?: {
  name?: string
  subject?: string
  courseType?: string
  showInvalid?: boolean
  studentId?: string
  pageNum?: number
  pageSize?: number
}): Promise<ApiResponse<ProductItem[]>> {
  return request({
    url: '/management/products/available',
    method: 'get',
    params
  })
}
