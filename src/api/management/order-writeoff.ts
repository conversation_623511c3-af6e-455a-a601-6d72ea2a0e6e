import request from '@/utils/request'

// 订单核销接口
export function writeOffOrderApi(data: {
  studentId: string
  orderNo: string
  orderImg: string
  remark?: string
}) {
  return request({
    url: '/order-writeoff/write-off',
    method: 'post',
    data
  })
}

export function getWriteOffDetailApi(orderNo: string, orderSource: string) {
  return request({
    url: `/order-writeoff/detail/${orderNo}`,
    method: 'get',
    params: { orderSource }
  })
}

// 获取核销记录列表
export function getWriteOffListApi(params: {
  pageNum?: number
  pageSize?: number
  studentId?: string | number
  orderSource?: string
  orderNo?: string
}) {
  return request({
    url: '/management/order/writeoff/list',
    method: 'get',
    params
  })
}

// 删除核销记录
export function deleteWriteOffApi(id: string | number) {
  return request({
    url: `/management/order/writeoff/${id}`,
    method: 'delete'
  })
}