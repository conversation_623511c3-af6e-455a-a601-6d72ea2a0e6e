import request from '@/utils/request'

/**
 * 订单管理API
 */

// 创建订单
export function createOrderApi(data) {
  return request({
    url: '/order/create',
    method: 'post',
    data
  })
}

export function getSalesStudentListApi(params) {
  return request({
    url: '/order/student/list',
    method: 'get',
    params
  })
}

export function getSalesStudentDetailApi(id) {
  return request({
    url: `/order/student/detail/${id}`,
    method: 'get'
  })
}

// 根据订单ID查询订单详情
export function getOrderDetailApi(orderId) {
  return request({
    url: `/order/${orderId}`,
    method: 'get'
  })
}

// 根据订单号查询订单详情
export function getOrderByNoApi(orderNo) {
  return request({
    url: `/order/no/${orderNo}`,
    method: 'get'
  })
}

// 根据学生ID查询订单列表
export function getOrdersByStudentApi(studentId) {
  return request({
    url: `/order/student/${studentId}`,
    method: 'get'
  })
}

// 根据销售员ID查询订单列表
export function getOrdersBySalerApi(salerId) {
  return request({
    url: `/order/saler/${salerId}`,
    method: 'get'
  })
}

// 根据订单状态查询订单列表
export function getOrdersByStatusApi(orderStatus) {
  return request({
    url: `/order/status/${orderStatus}`,
    method: 'get'
  })
}

// 根据订单ID查询交易流水列表
export function getOrderTransactionsApi(orderId) {
  return request({
    url: `/order/${orderId}/transactions`,
    method: 'get'
  })
}

// 发起支付（生成支付二维码）
export function nativePayApi(orderTrxId) {
  return request({
    url: `/order/pay/${orderTrxId}`,
    method: 'post'
  })
}

// 取消订单
export function cancelOrderApi(orderId) {
  return request({
    url: `/order/${orderId}/cancel`,
    method: 'put'
  })
}

// 生成支付信息
export function generatePaymentApi(orderTrxId) {
  return request({
    url: `/order/payment/${orderTrxId}`,
    method: 'post'
  })
}

// 生成支付二维码
export function generateQRCodeApi(orderTrxId) {
  return request({
    url: `/order/qrcode/${orderTrxId}`,
    method: 'post'
  })
}

// 获取支付链接
export function getPaymentLinkApi(orderTrxId) {
  return request({
    url: `/order/payment-link/${orderTrxId}`,
    method: 'get'
  })
}

// 发送支付微信消息，根据订单
export function sendWechatPaymentOrderApi(orderId) {
  return request({
    url: `/order/send-wechat-order/${orderId}`,
    method: 'get'
  })
}

// 发送支付微信消息，根据交易流水
export function sendWechatPaymentTrxApi(orderTrxId) {
  return request({
    url: `/order/send-wechat-trx/${orderTrxId}`,
    method: 'get'
  })
}

/**
 * 退款申请
 * @param orderId
 * @param refundData
 * @returns {*}
 */
export function refundApplyApi(orderId, refundData) {
  return request({
    url: `/order-manager/${orderId}/refund-apply`,
    method: 'post',
    data: refundData
  })

}

// 导出订单数据
export function exportOrdersApi(params) {
  return request({
    url: '/order/export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}

// 发起合同签署
export function initiateContractSignApi(orderId) {
  return request({
    url: `/order/${orderId}/sign`,
    method: 'post'
  })
}

// 获取学生已购买的订单课型
export function getAlreadyPaidOrdersApi(studentId) {
  return request({
    url: `/order/already/paid/${studentId}`,
    method: 'get'
  })
}
