import request from '@/utils/request'

// 预约课申请相关接口

// 首选时间段
export interface PreferredTimeSlot {
  weekday: number // 星期几 (1-7, 1=周一, 7=周日)
  startTime: string // 开始时间 (HH:mm格式)
  endTime: string // 结束时间 (HH:mm格式)
  priority: number // 优先级 (1-3, 1=最优先)
}

// 首选教师信息
export interface PreferredTeacherInfo {
  teacherId: string
  teacherName: string
  teacherPhone: string
  groupName?: string
  available?: boolean
  unavailableReason?: string
  priority?: number
}

// 预约课申请请求
export interface CourseBookingApplicationRequest {
  studentId: string
  subject: string
  specification: string
  nature: string // 课程性质
  teacherCandidates: string[] // 候选教师ID列表
  preferredTimeSlots: Array<{
    dayOfWeek: number // 星期几 (1-7, 1=周一, 7=周日)
    startHour: number // 开始小时
    endHour: number // 结束小时
  }>
  applyReason: string // 申请说明
}

// 预约课申请基本信息
export interface CourseBookingApplication {
  id: string
  studentId: string
  studentName: string
  studentPhone: string
  salesId: string
  salesName: string
  salesGroupId?: string
  salesGroupName?: string
  subject: string
  specification: string
  preferredTeacherInfos: PreferredTeacherInfo[]
  teachingGroupId?: string
  teachingGroupName?: string
  teachingGroupLeaderName?: string
  preferredTimeSlots: PreferredTimeSlot[]
  applicationReason: string
  status: string
  approvedTeacherId?: string
  approvedTeacherName?: string
  approvalTime?: string
  approvalBy?: string
  approvalByName?: string
  rejectionReason?: string
  courseHoursPackageId?: string
  createTime: string
  updateTime: string
}

// 预约课申请详细信息
export interface CourseBookingApplicationDetail extends CourseBookingApplication {
  createBy: string
  updateBy: string
  processHistory: ProcessHistory[]
}

// 申请处理历史
export interface ProcessHistory {
  id: string
  applicationId: string
  processType: string // submit, approve, reject, cancel
  processorId: string
  processorName: string
  processTime: string
  processRemark: string
  beforeStatus: string
  afterStatus: string
}

// 预约课申请列表查询请求
export interface CourseBookingListRequest {
  pageNum?: number
  pageSize?: number
  studentName?: string
  salesId?: string
  salesGroupId?: string
  subject?: string
  specification?: string
  status?: string
  teachingGroupId?: string
  createTimeStart?: string
  createTimeEnd?: string
}

// 预约课申请确认请求
export interface CourseBookingApprovalRequest {
  applicationId: string
  approvedTeacherId: string
  approvalRemark?: string
}

// 预约课申请拒绝请求
export interface CourseBookingRejectionRequest {
  applicationId: string
  rejectionReason: string
}

// 预约课申请统计
export interface CourseBookingStats {
  totalApplications: number
  pendingApplications: number
  approvedApplications: number
  rejectedApplications: number
  approvalRate: number
  monthlyApplications: number
  monthlyApprovals: number
  averageProcessingTime: number
}

// 可选教师查询请求
export interface AvailableTeachersRequest {
  subject: string
  specification: string
  teachingGroupId?: string
  keyword?: string
  groupId?: string
  gender?: string
}

/**
 * 提交预约课申请
 */
export function submitCourseBookingApi(data: CourseBookingApplicationRequest) {
  return request({
    url: '/course-booking/apply',
    method: 'post',
    data
  })
}

/**
 * 分页查询预约课申请列表
 */
export function getCourseBookingListApi(params: CourseBookingListRequest) {
  return request({
    url: '/course-booking/history',
    method: 'get',
    params
  })
}

/**
 * 获取预约课申请详情
 */
export function getCourseBookingDetailApi(applicationId: string) {
  return request({
    url: `/course-booking/${applicationId}`,
    method: 'get'
  })
}

/**
 * 确认预约课申请
 */
export function approveCourseBookingApi(data: CourseBookingApprovalRequest) {
  return request({
    url: '/course-booking/approve',
    method: 'post',
    data
  })
}

/**
 * 拒绝预约课申请
 */
export function rejectCourseBookingApi(data: CourseBookingRejectionRequest) {
  return request({
    url: '/course-booking/reject',
    method: 'post',
    data
  })
}

/**
 * 取消预约课申请
 */
export function cancelCourseBookingApi(applicationId: string, cancelReason: string) {
  return request({
    url: `/course-booking/cancel/${applicationId}`,
    method: 'post',
    data: { cancelReason }
  })
}

/**
 * 批量处理预约课申请
 */
export function batchProcessCourseBookingApi(applicationIds: string[], processType: string, processRemark: string) {
  return request({
    url: '/course-booking/batch-process',
    method: 'post',
    data: {
      applicationIds,
      processType,
      processRemark
    }
  })
}

/**
 * 获取销售的预约课申请列表
 */
export function getSalesCourseBookingApi(salesId: string, status?: string) {
  return request({
    url: `/course-booking/sales/${salesId}`,
    method: 'get',
    params: { status }
  })
}

/**
 * 获取教学组的预约课申请列表
 */
export function getTeachingGroupCourseBookingApi(teachingGroupId: string, status?: string) {
  return request({
    url: `/course-booking/teaching-group/${teachingGroupId}`,
    method: 'get',
    params: { status }
  })
}

/**
 * 获取学生的预约课申请历史
 */
export function getStudentCourseBookingHistoryApi(studentId: string) {
  return request({
    url: `/course-booking/student/${studentId}/history`,
    method: 'get'
  })
}

/**
 * 获取预约课申请统计
 */
export function getCourseBookingStatsApi(salesId?: string, teachingGroupId?: string) {
  return request({
    url: '/course-booking/stats',
    method: 'get',
    params: { salesId, teachingGroupId }
  })
}

/**
 * 检查学生是否可以申请预约课
 */
export function checkStudentEligibleApi(studentId: string, subject: string, specification: string) {
  return request({
    url: '/course-booking/check-eligible',
    method: 'get',
    params: { studentId, subject, specification }
  })
}

/**
 * 获取可选择的教师列表
 */
export function getAvailableTeachersApi(params: AvailableTeachersRequest) {
  return request({
    url: '/course-booking/available-teachers',
    method: 'get',
    params
  })
}

/**
 * 验证教师是否可用
 */
export function validateTeacherAvailabilityApi(teacherId: string, subject: string, specification: string, timeSlots: PreferredTimeSlot[]) {
  return request({
    url: '/course-booking/validate-teacher',
    method: 'post',
    data: {
      teacherId,
      subject,
      specification,
      timeSlots
    }
  })
}

/**
 * 自动分配教师
 */
export function autoAssignTeacherApi(applicationId: string) {
  return request({
    url: `/course-booking/auto-assign/${applicationId}`,
    method: 'post'
  })
}

/**
 * 获取申请处理历史
 */
export function getCourseBookingProcessHistoryApi(applicationId: string) {
  return request({
    url: `/course-booking/process-history/${applicationId}`,
    method: 'get'
  })
}

/**
 * 获取待处理的申请数量
 */
export function getPendingCourseBookingCountApi(teachingGroupId?: string) {
  return request({
    url: '/course-booking/pending-count',
    method: 'get',
    params: { teachingGroupId }
  })
}

/**
 * 获取超时未处理的申请列表
 */
export function getTimeoutCourseBookingApi(timeoutHours: number) {
  return request({
    url: '/course-booking/timeout',
    method: 'get',
    params: { timeoutHours }
  })
}

/**
 * 发送超时提醒通知
 */
export function sendTimeoutRemindersApi(timeoutHours: number) {
  return request({
    url: '/course-booking/send-timeout-reminders',
    method: 'post',
    data: { timeoutHours }
  })
}

/**
 * 计算申请优先级
 */
export function calculateApplicationPriorityApi(applicationId: string) {
  return request({
    url: `/course-booking/calculate-priority/${applicationId}`,
    method: 'get'
  })
}

/**
 * 重新排序申请列表
 */
export function reorderApplicationsApi(teachingGroupId: string) {
  return request({
    url: `/course-booking/reorder/${teachingGroupId}`,
    method: 'post'
  })
}
