import request from '@/utils/request'

// 获取销售人员列表
export function getSalesStaffListApi(params) {
  return request({
    url: '/sales/staff/list',
    method: 'get',
    params
  })
}

// 获取销售人员详情
export function getSalesStaffDetailApi(id) {
  return request({
    url: `/sales/staff/${id}`,
    method: 'get'
  })
}

// 创建销售人员
export function createSalesStaffApi(data) {
  return request({
    url: '/sales/staff',
    method: 'post',
    data
  })
}

// 更新销售人员
export function updateSalesStaffApi(data) {
  return request({
    url: '/sales/staff',
    method: 'put',
    data
  })
}

// 删除销售人员
export function deleteSalesStaffApi(ids) {
  return request({
    url: `/sales/staff/${ids}`,
    method: 'delete'
  })
}

// 分配销售到销售组
export function assignSalesToGroupApi(data) {
  return request({
    url: '/sales/staff/assign',
    method: 'post',
    data
  })
}

// 批量分配销售到销售组
export function batchAssignSalesToGroupApi(data) {
  return request({
    url: '/sales/staff/batch-assign',
    method: 'post',
    data
  })
}

// 从销售组移除销售
export function removeSalesFromGroupApi(salesId, groupId) {
  return request({
    url: `/sales/staff/${salesId}/group/${groupId}`,
    method: 'delete'
  })
}

// 获取可分配的销售列表
export function getAvailableSalesStaffApi() {
  return request({
    url: '/sales/staff/available',
    method: 'get'
  })
}

// 获取销售人员统计信息
export function getSalesStaffStatsApi() {
  return request({
    url: '/sales/staff/stats',
    method: 'get'
  })
}

// 重置销售人员密码
export function resetSalesStaffPasswordApi(salesId, newPassword = '654321') {
  return request({
    url: `/sales/staff/${salesId}/reset-password`,
    method: 'put',
    params: { newPassword }
  })
}

// 启用/停用销售人员
export function changeSalesStaffStatusApi(salesId, status) {
  return request({
    url: `/sales/staff/${salesId}/status`,
    method: 'put',
    params: { status }
  })
}

// 获取销售人员选项列表（用于下拉选择）
export function getSalesStaffOptionsApi(params) {
  return request({
    url: '/sales/staff/options',
    method: 'get',
    params
  })
}
