import request from '@/utils/request'
import type {
  ApiResponse,
  GetScheduleParams,
  GetReviewPlansParams,
  CancelCourseParams,
  RescheduleCourseParams,
  ConsumeCourseParams,
  GetStudentsParams,
  GetTeachersParams,
  PageResponse,
  CourseSchedule,
  ReviewPlan,
  ScheduleRequest,
  Student,
  Teacher,
  TeacherAvailableTimeSlots,
  DownloadCourseMaterialResponse,
  ReviewUploadRequest,
  ReviewUploadResponse,
  ReviewUploadViewResponse
} from './types'

/**
 * 获取课表数据
 * @param params 查询参数
 * @returns 课表列表
 */
export function getScheduleApi(params: GetScheduleParams): Promise<ApiResponse<CourseSchedule[]>> {
  return request({
    url: '/curriculum/schedule',
    method: 'get',
    params
  })
}

/**
 * 排课
 * @param data 排课数据
 * @returns 操作结果
 */
export function createScheduleApi(data: ScheduleRequest): Promise<ApiResponse<void>> {
  return request({
    url: '/curriculum/schedule',
    method: 'post',
    data
  })
}

/**
 * 开始上课
 * 原来是自动开始上课，现在改成只检查是不是可以上课，不自动开始
 * @param courseId 课程ID
 * @returns 操作结果
 */
export function startCourseApi(courseId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/course/preflight/${courseId}`,
    method: 'post'
  })
}

/**
 * 停课
 * @param courseId 课程ID
 * @param data 停课数据
 * @returns 操作结果
 */
export function cancelCourseApi(courseId: string, data: CancelCourseParams): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/course/${courseId}/cancel`,
    method: 'post',
    data
  })
}

/**
 * 结束上课
 * @param courseId 课程ID
 * @returns 操作结果
 */
export function endCourseApi(courseId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/course/${courseId}/end`,
    method: 'post'
  })
}

/**
 * 调课
 * @param data 调课数据
 * @returns 操作结果
 */
export function rescheduleCourseApi(data: RescheduleCourseParams): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/course/reschedule`,
    method: 'post',
    data
  })
}

/**
 * 获取抗遗忘复习计划
 * @param params 查询参数
 * @returns 抗遗忘复习计划列表
 */
export function getReviewPlansApi(params: GetReviewPlansParams): Promise<ApiResponse<ReviewPlan[]>> {
  console.log('🔄 [抗遗忘复习] getReviewPlansApi 请求参数:', params)

  // 构建请求参数
  const requestParams: any = {
    keyword: params.keyword,
    teacherId: params.teacherId,
    studentId: params.studentId,
    reviewTypes: params.reviewTypes,
    statuses: params.statuses,
    startTime: params.startTime,
    endTime: params.endTime,
    orderBy: params.orderBy || 'scheduled_time',
    orderDirection: params.orderDirection || 'ASC'
  }

  // 根据viewType设置时间范围和状态过滤
  if (params.viewType) {
    const today = new Date()
    const todayStr = today.toISOString().split('T')[0]

    if (params.viewType === 'today') {
      // 今日：显示今天及之前的待复习项目
      requestParams.endTime = todayStr + ' 23:59:59'
      console.log('🔄 [抗遗忘复习] 今日视图，结束时间:', requestParams.endTime)
    } else if (params.viewType === 'uncompleted') {
      // 待复习：显示明天及以后的待复习项目
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)
      const tomorrowStr = tomorrow.toISOString().split('T')[0]
      requestParams.startTime = tomorrowStr + ' 00:00:00'
      console.log('🔄 [抗遗忘复习] 待复习视图，开始时间:', requestParams.startTime)
    }
    // 如果没有指定viewType，则获取所有数据（不设置时间限制）
  }

  console.log('🔄 [抗遗忘复习] 最终请求参数:', requestParams)

  // 使用新的多条件查询接口
  return request({
    url: '/course/review/list',
    method: 'get',
    params: requestParams
  })
}

/**
 * 分页查询抗遗忘复习计划
 * @param params 查询参数
 * @returns 分页结果
 */
export function getReviewPlansPageApi(params: GetReviewPlansParams): Promise<ApiResponse<{
  records: ReviewPlan[]
  total: number
  size: number
  current: number
  pages: number
}>> {
  console.log('🔄 [抗遗忘复习] getReviewPlansPageApi 请求参数:', params)

  return request({
    url: '/course/review/page',
    method: 'get',
    params: {
      keyword: params.keyword,
      teacherId: params.teacherId,
      studentId: params.studentId,
      reviewTypes: params.reviewTypes,
      statuses: params.statuses,
      startTime: params.startTime,
      endTime: params.endTime,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10,
      orderBy: params.orderBy || 'scheduled_time',
      orderDirection: params.orderDirection || 'ASC'
    }
  })
}

/**
 * 开始抗遗忘复习
 * @param courseId 课程ID
 * @param reviewId 复习计划ID
 * @returns 操作结果
 */
export function startReviewApi(courseId: string, reviewId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/course/start-review/${courseId}/review/${reviewId}`,
    method: 'post'
  })
}

/**
 * 完成复习
 * @param reviewId 复习ID
 * @returns 操作结果
 */
export function completeReviewApi(reviewId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/review/${reviewId}/complete`,
    method: 'post'
  })
}

/**
 * 获取学生列表
 * @param params 查询参数
 * @returns 学生列表
 */
export function getStudentsApi(params: GetStudentsParams): Promise<ApiResponse<PageResponse<Student>>> {
  return request({
    url: '/curriculum/students',
    method: 'get',
    params
  })
}

/**
 * 获取教师列表
 * @param params 查询参数
 * @returns 教师列表
 */
export function getTeachersApi(params?: GetTeachersParams): Promise<ApiResponse<Teacher[]>> {
  return request({
    url: '/curriculum/teachers',
    method: 'get',
    params
  })
}

/**
 * 获取教师可排课时间段
 * @param teacherId 教师ID
 * @returns 教师可排课时间段
 */
export function getTeacherAvailableTimeSlotsApi(teacherId: string): Promise<ApiResponse<TeacherAvailableTimeSlots>> {
    const id = teacherId ?? ''
    return request({
    url: `/user/teacher/available-time-slots?id=${id}`,
    method: 'get'
  })
}

/**
 * 下载课程资料
 * @param courseId 课程ID
 * @param type 资料类型 (handout: 讲义, exercise: 练习, error_handout: 错词讲义, error_exercise: 错题练习)
 * @returns 下载信息
 */
export function downloadCourseMaterialApi(courseId: string, type: 'handout' | 'exercise' | 'error_handout' | 'error_exercise'): Promise<ApiResponse<DownloadCourseMaterialResponse>> {
  return request({
    url: `/course/material/download`,
    method: 'get',
    params: { courseId, type }
  })
}

/**
 * 消课
 * @param data 消课数据
 * @returns 操作结果
 */
export function consumeCourseApi(data: ConsumeCourseParams): Promise<ApiResponse<void>> {
  return request({
    url: '/curriculum/course/consume',
    method: 'post',
    data
  })
}

/**
 * 上传抗遗忘复习完成情况
 * @param data 上传数据（FormData格式）
 * @returns 上传结果
 */
export function uploadReviewCompletionApi(data: FormData): Promise<ApiResponse<ReviewUploadResponse>> {
  return request({
    url: '/course/review/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 查看抗遗忘复习上传内容
 * @param reviewScheduleId 复习计划ID
 * @returns 上传内容详情
 */
export function viewReviewUploadApi(reviewScheduleId: string): Promise<ApiResponse<ReviewUploadViewResponse>> {
  return request({
    url: `/course/review/upload/${reviewScheduleId}`,
    method: 'get'
  })
}

// 导出所有API方法
export const curriculumApi = {
  getSchedule: getScheduleApi,
  createSchedule: createScheduleApi,
  startCourse: startCourseApi,
  cancelCourse: cancelCourseApi,
  endCourse: endCourseApi,
  rescheduleCourse: rescheduleCourseApi,
  getReviewPlans: getReviewPlansApi,
  startReview: startReviewApi,
  completeReview: completeReviewApi,
  getStudents: getStudentsApi,
  getTeachers: getTeachersApi,
  getTeacherAvailableTimeSlots: getTeacherAvailableTimeSlotsApi,
  downloadCourseMaterial: downloadCourseMaterialApi,
  consumeCourse: consumeCourseApi,
  uploadReviewCompletion: uploadReviewCompletionApi,
  viewReviewUpload: viewReviewUploadApi
}

export default curriculumApi