// 课程数据模型 - 统一支持正常课程和复习课
export interface CourseSchedule {
  id: string
  studentId: string
  studentName: string
  teacherId: string
  teacherName: string
  startTime?: string  // 复习课可选，允许协商时间
  endTime?: string    // 复习课可选，允许协商时间
  scheduledDate?: string  // 复习课使用，协商的上课日期
  duration: number
  status: '待开始' | '进行中' | '已完成' | '停课'
  type: '学习课' | '复习课'  // 课程类型：正常课程或复习课
  subject?: string
  classroom?: string
  cancelReason?: string
  cancelType?: 'teacher' | 'student'
  rescheduleReason?: string
  rescheduleType?: 'teacher' | 'student'
  specification?: string
  courseType?: string // 课程性质：正式课、试听课
  useSystem?: boolean // 是否使用系统上课
  grade?: string // 年级信息
}

// 抗遗忘复习计划数据模型
export interface ReviewSchedule {
  id: string
  studentId: string
  studentName?: string  // 前端显示用，可能需要关联查询
  courseId: string
  reviewType: 'D2' | 'D4' | 'D7' | 'D14' | 'D21'
  name: string
  scheduledTime: string
  actualStartTime?: string
  actualEndTime?: string
  status: '待开始' | '进行中' | '已完成' | '已跳过'
  wordIds: string[]
  textbookItemIds: string[]
  version: number
  content?: string
  statWordTotal?: number
  statWordCorrect?: number
  statWordIncorrect?: number
  statStepTotal?: number
  statStepCorrect?: number
  statStepIncorrect?: number
  reviewCourseId?: string
  reviewByOneself?: boolean
  reviewByUserId?: string
  uploadedImages?: string[]  // 上传的图片URL列表
  uploadedDescription?: string  // 上传的说明文字
  uploadedTime?: string  // 上传时间
}

// 为了保持向后兼容，保留复习课类型别名，但现在指向ReviewSchedule
export type ReviewPlan = ReviewSchedule

// 排课请求数据模型
export interface ScheduleRequest {
  studentId: string
  teacherId: string
  dateRange: [string, string]
  totalLessons: number
  weeklySchedules: {
    dayOfWeek: number // 0-6, 0为周日
    startTime: string
    endTime: string
    duration?: number
  }[]
  duration: number
  subject: string
  specification: string // 新增规格字段
  type: string
  courseType: string // 新增课程性质字段：正式课、试听课
  useSystem: boolean // 新增使用系统字段
  grade?: string // 学生年级信息
}

// 学生数据模型
export interface Student {
  id: string
  name: string
  phone: string
  avatar?: string
  grade?: string
  status: 'active' | 'inactive'
}

// API响应基础接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 获取课表参数
export interface GetScheduleParams {
  teacherId?: string
  studentId?: string
  startDate: string
  endDate: string
  viewType: 'week' | 'month' | 'today' | 'future'
  type?: '学习课' | '复习课' | 'all'
}

// 获取复习课参数
export interface GetReviewPlansParams {
  /** 关键字搜索（复习任务名称） */
  keyword?: string
  /** 老师ID */
  teacherId?: string
  /** 学生ID */
  studentId?: string
  /** 复习类型列表 */
  reviewTypes?: string[]
  /** 复习状态列表 */
  statuses?: string[]
  /** 复习时间范围 - 开始时间 */
  startTime?: string
  /** 复习时间范围 - 结束时间 */
  endTime?: string
  /** 页码（分页查询时使用） */
  pageNum?: number
  /** 每页大小（分页查询时使用） */
  pageSize?: number
  /** 排序字段 */
  orderBy?: string
  /** 排序方向 */
  orderDirection?: 'ASC' | 'DESC'
  /** 视图类型（兼容旧版本） */
  viewType?: 'today' | 'future' | 'uncompleted'
  /** 日期（兼容旧版本） */
  date?: string
}

// 停课参数
export interface CancelCourseParams {
  reason: string
  type: 'teacher' | 'student'
}

// 调课参数
export interface RescheduleCourseParams {
  courseId: string
  newDate: string
  newStartTime: string
  newEndTime: string
  rescheduleType: 'teacher' | 'student'
  reason: string
}

// 消课参数
export interface ConsumeCourseParams {
  courseId: string
  courseDate: string
  startTime: string
  endTime: string
  recordingUrl: string
  images: string
  description?: string
}

// 获取学生列表参数
export interface GetStudentsParams {
  teacherId?: string
  keyword?: string
  pageNum?: number
  pageSize?: number
}

// 分页响应
export interface PageResponse<T> {
  total: number
  rows: T[]
  pageNum: number
  pageSize: number
}

// 课表API接口定义
export interface CurriculumAPI {
  // 获取课表数据
  getSchedule(params: GetScheduleParams): Promise<ApiResponse<CourseSchedule[]>>
  
  // 排课
  createSchedule(data: ScheduleRequest): Promise<ApiResponse<void>>
  
  // 开始上课
  startCourse(courseId: string): Promise<ApiResponse<void>>
  
  // 停课
  cancelCourse(courseId: string, data: CancelCourseParams): Promise<ApiResponse<void>>

  // 调课
  rescheduleCourse(data: RescheduleCourseParams): Promise<ApiResponse<void>>

  // 结束上课
  endCourse(courseId: string): Promise<ApiResponse<void>>
  
  // 获取复习课
  getReviewPlans(params: GetReviewPlansParams): Promise<ApiResponse<ReviewPlan[]>>
  
  // 开始复习
  startReview(reviewId: string): Promise<ApiResponse<void>>
  
  // 完成复习
  completeReview(reviewId: string): Promise<ApiResponse<void>>
  
  // 获取学生列表
  getStudents(params: GetStudentsParams): Promise<ApiResponse<PageResponse<Student>>>
  
  // 获取教师列表
  getTeachers(params: GetTeachersParams): Promise<ApiResponse<Teacher[]>>
  
  // 获取教师可排课时间段
  getTeacherAvailableTimeSlots(teacherId: string): Promise<ApiResponse<TeacherAvailableTimeSlots>>

  // 下载课程资料
  downloadCourseMaterial(courseId: string, type: 'handout' | 'exercise'): Promise<ApiResponse<DownloadCourseMaterialResponse>>
}

// 教师信息
export interface Teacher {
  id: string
  name: string
  phone: string
  email?: string
  avatar?: string
  status: 'active' | 'inactive'
  createdAt?: string
}

// 时间段
export interface TimeSlot {
  startTime: string
  endTime: string
}

// 可排课时间段
export interface AvailableTimeSlot {
  weekday: number  // 星期几，1-7，1为周一，7为周日
  timeSlots: TimeSlot[]
}

// 教师可排课时间段响应
export interface TeacherAvailableTimeSlots {
  teacherId: string
  teacherName: string
availableTimeSlots: AvailableTimeSlot[]
}

// 获取教师列表参数
export interface GetTeachersParams {
  keyword?: string
  page?: number
  pageSize?: number
}

// 下载课程资料参数
export interface DownloadCourseMaterialParams {
  courseId: string
  type: 'handout' | 'exercise' | 'error_handout' | 'error_exercise'  // handout: 讲义, exercise: 练习, error_handout: 错词讲义, error_exercise: 错题练习
}

// 下载课程资料响应
export interface DownloadCourseMaterialResponse {
  downloadUrl: string
  fileName?: string
  contentType?: string
}

// 抗遗忘复习上传请求参数
export interface ReviewUploadRequest {
  reviewScheduleId: string
  imageUrls: string[]
  description?: string
}

// 抗遗忘复习上传响应
export interface ReviewUploadResponse {
  reviewScheduleId: string
  status: string
  uploadedTime: string
  message: string
}

// 抗遗忘复习上传内容查看响应
export interface ReviewUploadViewResponse {
  reviewScheduleId: string
  reviewName: string
  reviewType: string
  studentName: string
  imageUrls?: string[]
  description?: string
  uploadedTime?: string
  hasUploaded: boolean
}