import request from '@/utils/request'

// 获取销售学生列表
export function getSalesStudentList(params) {
  return request({
    url: '/sales/student/list',
    method: 'get',
    params
  })
}

// 获取销售学生详情
export function getSalesStudentDetail(id) {
  return request({
    url: `/sales/student/${id}`,
    method: 'get'
  })
}

// 创建销售学生
export function createSalesStudent(data) {
  return request({
    url: '/sales/student',
    method: 'post',
    data
  })
}

// 更新销售学生
export function updateSalesStudent(id, data) {
  return request({
    url: `/sales/student/${id}`,
    method: 'put',
    data
  })
}

// 删除销售学生
export function deleteSalesStudent(ids) {
  return request({
    url: '/sales/student',
    method: 'delete',
    data: { ids }
  })
}

// 分配学生给销售
export function assignSalesStudent(id, data) {
  return request({
    url: `/sales/student/${id}/assign`,
    method: 'post',
    data
  })
}

// 批量分配学生
export function batchAssignSalesStudent(data) {
  return request({
    url: '/sales/student/batch-assign',
    method: 'post',
    data
  })
}

// 导入学生
export function importSalesStudent(data) {
  return request({
    url: '/sales/student/import',
    method: 'post',
    data
  })
}

// 下载导入模板
export function downloadTemplate() {
  return request({
    url: '/sales/student/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取销售学生统计
export function getSalesStudentStats() {
  return request({
    url: '/sales/student/stats',
    method: 'get'
  })
}

// 获取销售人员选项
export function getSalesOptions(params) {
  return request({
    url: '/sales/student/sales-options',
    method: 'get',
    params
  })
}

// 获取销售组选项
export function getSalesGroupOptions() {
  return request({
    url: '/sales/student/group-options',
    method: 'get'
  })
}
