<template>
  <el-dialog
    title="导出进度"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div class="export-progress">
      <div class="progress-info">
        <el-icon class="loading-icon" v-if="status === 'processing'">
          <Loading />
        </el-icon>
        <el-icon class="success-icon" v-else-if="status === 'completed'">
          <Check />
        </el-icon>
        <el-icon class="error-icon" v-else-if="status === 'failed'">
          <Close />
        </el-icon>
        
        <div class="progress-text">
          <h3>{{ getStatusText() }}</h3>
          <p v-if="message">{{ message }}</p>
        </div>
      </div>
      
      <el-progress
        v-if="status === 'processing'"
        :percentage="progress"
        :stroke-width="8"
        status="success"
        :show-text="true"
      />
      
      <div v-if="status === 'completed'" class="export-result">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="文件名">{{ fileName }}</el-descriptions-item>
          <el-descriptions-item label="导出数量">{{ exportCount }} 条</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="导出时间">{{ formatTime(exportTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div v-if="status === 'failed'" class="error-info">
        <el-alert
          :title="errorMessage || '导出失败'"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button v-if="status === 'processing'" @click="handleCancel">取消导出</el-button>
      <el-button v-if="status === 'failed'" type="primary" @click="handleRetry">重试</el-button>
      <el-button v-if="status === 'completed'" type="primary" @click="handleClose">完成</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { Loading, Check, Close } from '@element-plus/icons-vue'

export default {
  name: 'ExportProgress',
  components: {
    Loading,
    Check,
    Close
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    status: {
      type: String,
      default: 'processing', // processing, completed, failed
      validator: value => ['processing', 'completed', 'failed'].includes(value)
    },
    progress: {
      type: Number,
      default: 0
    },
    message: {
      type: String,
      default: ''
    },
    fileName: {
      type: String,
      default: ''
    },
    exportCount: {
      type: Number,
      default: 0
    },
    fileSize: {
      type: Number,
      default: 0
    },
    exportTime: {
      type: Date,
      default: null
    },
    errorMessage: {
      type: String,
      default: ''
    }
  },
  methods: {
    getStatusText() {
      switch (this.status) {
        case 'processing':
          return '正在导出数据...'
        case 'completed':
          return '导出完成'
        case 'failed':
          return '导出失败'
        default:
          return '导出中'
      }
    },
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleString('zh-CN')
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleRetry() {
      this.$emit('retry')
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.export-progress {
  text-align: center;
  padding: 20px 0;
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.loading-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 10px;
  animation: rotate 2s linear infinite;
}

.success-icon {
  font-size: 24px;
  color: #67c23a;
  margin-right: 10px;
}

.error-icon {
  font-size: 24px;
  color: #f56c6c;
  margin-right: 10px;
}

.progress-text h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 500;
}

.progress-text p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.export-result {
  margin-top: 20px;
  text-align: left;
}

.error-info {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
