<template>
  <el-dialog
    title="导出记录"
    :visible.sync="visible"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="export-history">
      <!-- 搜索条件 -->
      <el-form :model="queryForm" :inline="true" size="small" class="search-form">
        <el-form-item label="导出类型">
          <el-select v-model="queryForm.exportType" placeholder="请选择导出类型" clearable>
            <el-option label="订单数据" value="order" />
            <el-option label="交易流水" value="order_trx" />
          </el-select>
        </el-form-item>
        <el-form-item label="导出状态">
          <el-select v-model="queryForm.status" placeholder="请选择导出状态" clearable>
            <el-option label="导出中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="导出时间">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 导出记录表格 -->
      <el-table
        :data="exportList"
        v-loading="loading"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="exportType" label="导出类型" width="100">
          <template #default="scope">
            <el-tag :type="getExportTypeTagType(scope.row.exportType)">
              {{ getExportTypeText(scope.row.exportType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileName" label="文件名" min-width="200" show-overflow-tooltip />
        <el-table-column prop="exportCount" label="导出数量" width="100">
          <template #default="scope">
            {{ scope.row.exportCount }} 条
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="100">
          <template #default="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="exportTime" label="导出时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.exportTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="exportUser" label="导出人" width="100" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 'completed'"
              type="text"
              size="small"
              @click="handleDownload(scope.row)"
            >
              下载
            </el-button>
            <el-button
              v-if="scope.row.status === 'failed'"
              type="text"
              size="small"
              @click="handleRetry(scope.row)"
            >
              重试
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-show="total > 0"
        :current-page="queryForm.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryForm.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; text-align: right"
      />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="danger" @click="handleClearAll">清空记录</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ExportHistory',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      exportList: [],
      total: 0,
      queryForm: {
        exportType: '',
        status: '',
        dateRange: [],
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadExportHistory()
      }
    }
  },
  methods: {
    loadExportHistory() {
      this.loading = true
      
      // 模拟数据，实际应该调用API
      setTimeout(() => {
        this.exportList = [
          {
            id: '1',
            exportType: 'order',
            fileName: '订单数据_20250807143000.xlsx',
            exportCount: 1250,
            fileSize: 2048576,
            status: 'completed',
            exportTime: new Date('2025-08-07 14:30:00'),
            exportUser: '张三',
            downloadUrl: '/download/order_20250807143000.xlsx'
          },
          {
            id: '2',
            exportType: 'order_trx',
            fileName: '交易流水_20250807142000.xlsx',
            exportCount: 3500,
            fileSize: 1536000,
            status: 'completed',
            exportTime: new Date('2025-08-07 14:20:00'),
            exportUser: '李四',
            downloadUrl: '/download/trx_20250807142000.xlsx'
          },
          {
            id: '3',
            exportType: 'order',
            fileName: '订单数据_20250807141000.xlsx',
            exportCount: 0,
            fileSize: 0,
            status: 'failed',
            exportTime: new Date('2025-08-07 14:10:00'),
            exportUser: '王五',
            errorMessage: '查询超时'
          }
        ]
        this.total = this.exportList.length
        this.loading = false
      }, 1000)
    },
    getExportTypeText(type) {
      const typeMap = {
        'order': '订单数据',
        'order_trx': '交易流水'
      }
      return typeMap[type] || type
    },
    getExportTypeTagType(type) {
      const typeMap = {
        'order': 'primary',
        'order_trx': 'success'
      }
      return typeMap[type] || 'info'
    },
    getStatusText(status) {
      const statusMap = {
        'processing': '导出中',
        'completed': '已完成',
        'failed': '失败'
      }
      return statusMap[status] || status
    },
    getStatusTagType(status) {
      const statusMap = {
        'processing': 'warning',
        'completed': 'success',
        'failed': 'danger'
      }
      return statusMap[status] || 'info'
    },
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleString('zh-CN')
    },
    handleSearch() {
      this.queryForm.pageNum = 1
      this.loadExportHistory()
    },
    handleReset() {
      this.queryForm = {
        exportType: '',
        status: '',
        dateRange: [],
        pageNum: 1,
        pageSize: 10
      }
      this.loadExportHistory()
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.loadExportHistory()
    },
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.loadExportHistory()
    },
    handleDownload(row) {
      // 实际应该调用下载API
      const link = document.createElement('a')
      link.href = row.downloadUrl
      link.download = row.fileName
      link.click()
      this.$message.success('开始下载文件')
    },
    handleRetry(row) {
      this.$emit('retry-export', row)
      this.$message.info('重新发起导出')
    },
    handleDelete(row) {
      this.$confirm('确认删除该导出记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际应该调用删除API
        this.exportList = this.exportList.filter(item => item.id !== row.id)
        this.total = this.exportList.length
        this.$message.success('删除成功')
      })
    },
    handleClearAll() {
      this.$confirm('确认清空所有导出记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际应该调用清空API
        this.exportList = []
        this.total = 0
        this.$message.success('清空成功')
      })
    },
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.export-history {
  min-height: 400px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
