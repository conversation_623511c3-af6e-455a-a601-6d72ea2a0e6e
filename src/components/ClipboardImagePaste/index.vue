<template>
  <div class="clipboard-paste-container">
    <el-button
      type="default"
      :icon="DocumentCopy"
      :loading="pasting"
      @click="handlePasteFromClipboard"
      :disabled="disabled"
      size="small"
    >
      粘贴图片
    </el-button>
    
    <div v-if="showTip" class="paste-tip">
      支持粘贴截图或复制的图片
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  showTip: {
    type: Boolean,
    default: true
  },
  maxSize: {
    type: Number,
    default: 10 // MB
  },
  acceptTypes: {
    type: Array,
    default: () => ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp']
  },
  maxImages: {
    type: Number,
    default: 10 // 最大图片数量
  }
})

const emit = defineEmits(['paste-success', 'paste-error', 'images-change'])

const pasting = ref(false)

// 处理从剪切板粘贴图片
const handlePasteFromClipboard = async () => {
  if (props.disabled) return

  // 检查是否被禁用
  if (props.disabled) {
    ElMessage.warning('已达到最大图片数量限制')
    return
  }

  pasting.value = true
  console.log('开始粘贴图片...')

  try {
    // 方法1: 尝试使用现代 Clipboard API
    if (navigator.clipboard && navigator.clipboard.read) {
      console.log('尝试使用 Clipboard API...')
      
      try {
        const clipboardItems = await navigator.clipboard.read()
        console.log('Clipboard API 返回项目数量:', clipboardItems.length)
        
        const pastedFiles = []
        let skippedCount = 0
        
        for (const clipboardItem of clipboardItems) {
          console.log('剪切板项目类型:', clipboardItem.types)
          
          for (const type of clipboardItem.types) {
            if (type.startsWith('image/')) {
              console.log('找到图片类型:', type)
              const blob = await clipboardItem.getType(type)
              
              // 检查文件大小
              const fileSizeMB = blob.size / 1024 / 1024
              if (fileSizeMB > props.maxSize) {
                console.log(`图片大小超限: ${fileSizeMB.toFixed(2)}MB`)
                skippedCount++
                continue
              }

              // 创建 File 对象
              const fileName = `clipboard-image-${Date.now()}-${pastedFiles.length}.${getFileExtension(type)}`
              const file = new File([blob], fileName, { type })

              console.log('Clipboard API 成功获取图片:', { name: fileName, size: blob.size, type })
              pastedFiles.push(file)
              
              // 只处理第一个图片类型，避免重复
              break
            }
          }
        }
        
        if (pastedFiles.length > 0) {
          // 通知父组件处理文件
          handlePasteSuccess(pastedFiles)
          
          if (skippedCount > 0) {
            ElMessage.warning(`成功粘贴 ${pastedFiles.length} 张图片，跳过 ${skippedCount} 张（大小超限）`)
          } else if (pastedFiles.length > 1) {
            ElMessage.success(`成功粘贴 ${pastedFiles.length} 张图片`)
          } else {
            ElMessage.success('图片粘贴成功')
          }
          return
        } else if (skippedCount > 0) {
          ElMessage.error('所有图片都因大小超限被跳过')
          return
        }
        
        console.log('Clipboard API 未找到图片')
      } catch (clipboardError) {
        console.log('Clipboard API 失败:', clipboardError)
        
        if (clipboardError.name === 'NotAllowedError') {
          ElMessage.error('访问剪切板被拒绝，请允许浏览器访问剪切板权限')
          return
        }
      }
    }

    // 方法2: 如果 Clipboard API 不可用或失败，提示用户使用手动粘贴
    console.log('Clipboard API 不可用，提示用户手动粘贴')
    showManualPasteDialog()

  } catch (error) {
    console.error('粘贴图片失败:', error)
    ElMessage.error('粘贴图片失败，请重试')
    emit('paste-error', error)
  } finally {
    pasting.value = false
  }
}

// 处理粘贴成功
const handlePasteSuccess = (files) => {
  // 直接通知父组件处理文件
  emit('images-change', Array.isArray(files) ? files : [files])
  emit('paste-success', files)
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 显示手动粘贴对话框
const showManualPasteDialog = () => {
  // 创建一个模态对话框让用户手动粘贴
  const dialog = document.createElement('div')
  dialog.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  `

  const content = document.createElement('div')
  content.style.cssText = `
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    text-align: center;
  `

  const pasteArea = document.createElement('div')
  pasteArea.contentEditable = true
  pasteArea.style.cssText = `
    border: 2px dashed #409eff;
    padding: 40px 20px;
    margin: 20px 0;
    border-radius: 6px;
    background-color: #f8f9fa;
    outline: none;
    cursor: text;
  `
  pasteArea.innerHTML = '点击此处，然后按 Ctrl+V 粘贴图片'

  const closeBtn = document.createElement('button')
  closeBtn.textContent = '取消'
  closeBtn.style.cssText = `
    background: #f5f5f5;
    border: 1px solid #ddd;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
  `

  content.innerHTML = `
    <h3 style="margin-top: 0; color: #303133;">粘贴图片</h3>
    <p style="color: #606266; margin-bottom: 20px;">
      请在下方区域中按 <strong>Ctrl+V</strong> 粘贴您复制的图片
    </p>
  `
  content.appendChild(pasteArea)
  content.appendChild(closeBtn)
  dialog.appendChild(content)
  document.body.appendChild(dialog)

  let handled = false

  const cleanup = () => {
    if (document.body.contains(dialog)) {
      document.body.removeChild(dialog)
    }
  }

  const handlePaste = (event) => {
    if (handled) return
    handled = true

    console.log('手动粘贴事件触发')
    event.preventDefault()

    const items = event.clipboardData?.items
    if (!items || items.length === 0) {
      console.log('剪切板为空或无法访问')
      ElMessage.warning('剪切板中没有内容')
      cleanup()
      return
    }

    console.log('手动粘贴检测到剪切板项目数量:', items.length)

    const pastedFiles = []
    let skippedCount = 0

    // 查找所有图片项目
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      console.log(`手动粘贴项目 ${i}: kind=${item.kind}, type=${item.type}`)

      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile()
        if (file) {
          console.log('手动粘贴成功获取图片文件:', {
            name: file.name,
            size: file.size,
            type: file.type
          })

          // 检查文件大小
          const fileSizeMB = file.size / 1024 / 1024
          if (fileSizeMB > props.maxSize) {
            console.log(`图片大小超限: ${fileSizeMB.toFixed(2)}MB`)
            skippedCount++
            continue
          }

          pastedFiles.push(file)
        }
      }
    }

    if (pastedFiles.length > 0) {
      // 成功获取图片，通知父组件
      handlePasteSuccess(pastedFiles)
      
      if (skippedCount > 0) {
        ElMessage.warning(`成功粘贴 ${pastedFiles.length} 张图片，跳过 ${skippedCount} 张（大小超限）`)
      } else if (pastedFiles.length > 1) {
        ElMessage.success(`成功粘贴 ${pastedFiles.length} 张图片`)
      } else {
        ElMessage.success('图片粘贴成功')
      }
      cleanup()
      return
    } else if (skippedCount > 0) {
      ElMessage.error('所有图片都因大小超限被跳过')
    } else {
      // 没有找到图片
      console.log('手动粘贴未找到图片项目')
      ElMessage.warning('剪切板中没有找到图片，请先复制或截图一张图片')
    }
  }

  // 键盘事件处理
  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      event.preventDefault()
      event.stopPropagation()
      // 不直接关闭，而是提示用户
      ElMessage.info('请点击"取消"按钮关闭对话框')
    }
  }

  // 事件监听
  pasteArea.addEventListener('paste', handlePaste)
  closeBtn.addEventListener('click', cleanup)
  document.addEventListener('keydown', handleKeyDown)
  
  // 点击背景不关闭对话框，需要明确点击取消按钮
  // dialog.addEventListener('click', (e) => {
  //   if (e.target === dialog) {
  //     cleanup()
  //   }
  // })

  // 修改 cleanup 函数，移除键盘事件监听
  const originalCleanup = cleanup
  const enhancedCleanup = () => {
    document.removeEventListener('keydown', handleKeyDown)
    originalCleanup()
  }
  
  // 更新事件监听器
  closeBtn.removeEventListener('click', cleanup)
  closeBtn.addEventListener('click', enhancedCleanup)

  // 聚焦到粘贴区域
  setTimeout(() => {
    pasteArea.focus()
  }, 100)
}





// 根据 MIME 类型获取文件扩展名
const getFileExtension = (mimeType) => {
  const extensions = {
    'image/png': 'png',
    'image/jpg': 'jpg',
    'image/jpeg': 'jpg',
    'image/gif': 'gif',
    'image/webp': 'webp'
  }
  return extensions[mimeType] || 'png'
}


</script>

<style scoped>
.clipboard-paste-container {
  display: inline-block;
}

.paste-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>