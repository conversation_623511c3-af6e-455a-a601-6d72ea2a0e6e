<template>
  <el-dialog
    title="订单核销申请"
    v-model="visible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="orderInfo" class="writeoff-content">
      <!-- 订单信息 -->
      <el-card class="order-info-card" style="margin-bottom: 20px;">
        <template #header>
          <span>订单信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderInfo.no }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(orderInfo.orderStatus)">{{ orderInfo.orderStatus }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ orderInfo.studentName }}</el-descriptions-item>
          <el-descriptions-item label="销售员">{{ orderInfo.salerName }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ (orderInfo.totalAmt / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(orderInfo.createTime) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 核销申请表单 -->
      <el-form
        ref="writeOffForm"
        :model="writeOffForm"
        :rules="writeOffRules"
        label-width="120px"
      >
        <el-form-item label="核销来源" prop="orderSource">
          <el-select v-model="writeOffForm.orderSource" placeholder="请选择核销来源" style="width: 100%">
            <el-option label="抖店" value="抖店" />
            <el-option label="星橙CRM" value="星橙CRM" />
          </el-select>
        </el-form-item>

        <el-form-item label="第三方订单号" prop="orderNo">
          <el-input
            v-model="writeOffForm.orderNo"
            placeholder="请输入第三方平台订单号"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="选择产品" prop="productId">
          <el-select
            v-model="writeOffForm.productId"
            placeholder="请选择要核销的产品"
            style="width: 100%"
            filterable
            @change="handleProductChange"
          >
            <el-option
              v-for="product in productList"
              :key="product.id"
              :label="`${product.name} (¥${(product.sellingPrice / 100).toFixed(2)})`"
              :value="product.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="selectedProduct" label="产品信息">
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="产品名称">{{ selectedProduct.name }}</el-descriptions-item>
            <el-descriptions-item label="产品价格">¥{{ (selectedProduct.sellingPrice / 100).toFixed(2) }}</el-descriptions-item>
            <el-descriptions-item label="学科">{{ selectedProduct.subject }}</el-descriptions-item>
            <el-descriptions-item label="课型">{{ selectedProduct.courseType }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>

        <el-form-item label="订单截图" prop="orderImg">
          <el-upload
            class="upload-demo"
            :action="uploadAction"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :file-list="fileList"
            list-type="picture-card"
            :limit="1"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                只能上传jpg/png文件，且不超过2MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="submitting" @click="handleSubmit">
        {{ submitting ? '提交中...' : '提交申请' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { Plus } from '@element-plus/icons-vue'
import { submitWriteOffApplication } from '@/api/management/order-writeroff'
import { getProductList } from '@/api/management/product'
import { formatDateTime } from '@/utils/date'
import { getToken } from '@/utils/auth'

export default {
  name: 'OrderWriteOffDialog',
  components: {
    Plus
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    orderInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      visible: this.value,
      submitting: false,
      productList: [],
      selectedProduct: null,
      fileList: [],
      uploadAction: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      writeOffForm: {
        orderSource: '',
        orderNo: '',
        productId: '',
        orderImg: '',
        studentId: ''
      },
      writeOffRules: {
        orderSource: [
          { required: true, message: '请选择核销来源', trigger: 'change' }
        ],
        orderNo: [
          { required: true, message: '请输入第三方订单号', trigger: 'blur' },
          { min: 3, max: 100, message: '订单号长度在3到100个字符', trigger: 'blur' }
        ],
        productId: [
          { required: true, message: '请选择产品', trigger: 'change' }
        ],
        orderImg: [
          { required: true, message: '请上传订单截图', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    value(val) {
      this.visible = val
      if (val && this.orderInfo) {
        this.resetForm()
        this.loadProductList()
      }
    },
    visible(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    formatDateTime,
    getStatusType(status) {
      const statusMap = {
        '未付款': 'warning',
        '已付款': 'success',
        '已全额支付': 'success',
        '已部分支付': 'primary',
        '已取消': 'danger',
        '已退款': 'info'
      }
      return statusMap[status] || 'info'
    },
    async loadProductList() {
      try {
        const response = await getProductList({ status: '上架' })
        if (response.code === 200) {
          this.productList = response.data || response.rows || []
        }
      } catch (error) {
        console.error('获取产品列表失败:', error)
        this.$message.error('获取产品列表失败')
      }
    },
    handleProductChange(productId) {
      this.selectedProduct = this.productList.find(p => p.id === productId)
    },
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    handleUploadSuccess(response) {
      if (response.code === 200) {
        this.writeOffForm.orderImg = response.url || response.data
        this.fileList = [{
          name: 'orderImg',
          url: this.writeOffForm.orderImg
        }]
        this.$message.success('图片上传成功')
      } else {
        this.$message.error('图片上传失败: ' + response.msg)
      }
    },
    handleUploadError() {
      this.$message.error('图片上传失败')
    },
    resetForm() {
      this.writeOffForm = {
        orderSource: '',
        orderNo: '',
        productId: '',
        orderImg: '',
        studentId: this.orderInfo?.studentId || ''
      }
      this.selectedProduct = null
      this.fileList = []
      if (this.$refs.writeOffForm) {
        this.$refs.writeOffForm.resetFields()
      }
    },
    handleSubmit() {
      this.$refs.writeOffForm.validate(async (valid) => {
        if (valid) {
          await this.doSubmit()
        }
      })
    },
    async doSubmit() {
      try {
        this.submitting = true
        
        const submitData = {
          ...this.writeOffForm,
          studentId: this.orderInfo.studentId
        }
        
        const response = await submitWriteOffApplication(submitData)
        
        if (response.code === 200) {
          this.$message.success('核销申请提交成功')
          this.$emit('success')
          this.handleClose()
        } else {
          this.$message.error('提交失败: ' + (response.msg || '未知错误'))
        }
      } catch (error) {
        console.error('提交核销申请失败:', error)
        this.$message.error('提交失败: ' + (error.message || '未知错误'))
      } finally {
        this.submitting = false
      }
    },
    handleClose() {
      this.visible = false
      this.resetForm()
    }
  }
}
</script>

<style scoped>
.writeoff-content {
  padding: 10px 0;
}

.order-info-card {
  border-radius: 8px;
}

.upload-demo {
  width: 100%;
}

.el-upload__tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>
